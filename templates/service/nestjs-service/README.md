# 🔧 **NestJS Service Template**

> **Production-ready NestJS microservice with enterprise patterns**

## 🎯 **Template Overview**

This template provides a **complete NestJS microservice** following enterprise-grade patterns and best practices:

- ✅ **Clean Architecture** - Domain-driven design with clear separation
- ✅ **TypeScript** - Full type safety and modern JavaScript features
- ✅ **Authentication & Authorization** - JWT, RBAC, and security middleware
- ✅ **Database Integration** - TypeORM with PostgreSQL and Redis
- ✅ **API Documentation** - Swagger/OpenAPI with interactive docs
- ✅ **Testing Framework** - Unit, integration, and E2E tests
- ✅ **Monitoring & Logging** - Structured logging and health checks
- ✅ **Docker Ready** - Complete containerization setup
- ✅ **CI/CD Pipeline** - GitHub Actions workflow included

## 🚀 **Quick Start**

### **⚡ Generate Service**

```bash
# Using the generator script
./tools/generators/generate-service.sh --type=nestjs --name=user-service

# Manual setup
cp -r templates/service/nestjs-service/template ./services/user-service
cd services/user-service
npm install
```

### **🔧 Configuration**

1. **Environment Setup**

   ```bash
   cp .env.template .env
   # Edit .env with your configuration
   ```

2. **Database Setup**

   ```bash
   # Start PostgreSQL and Redis
   docker-compose up -d postgres redis

   # Run migrations
   npm run migration:run
   ```

3. **Start Development**
   ```bash
   npm run start:dev
   ```

## 📁 **Template Structure**

```
nestjs-service/
├── 📋 README.md                     # Service documentation
├── 📋 package.json                  # Dependencies and scripts
├── 📋 tsconfig.json                 # TypeScript configuration
├── 📋 .env.template                 # Environment template
├── 📋 Dockerfile                    # Container configuration
├── 📋 docker-compose.yml            # Local development setup
├── 📋 jest.config.js                # Testing configuration
│
├── 📁 src/                          # 💻 SOURCE CODE
│   ├── 📁 main.ts                   # Application entry point
│   ├── 📁 app.module.ts             # Root application module
│   │
│   ├── 📁 domain/                   # 🏛️ DOMAIN LAYER
│   │   ├── entities/                # Domain entities
│   │   ├── repositories/            # Repository interfaces
│   │   ├── services/                # Domain services
│   │   └── value-objects/           # Value objects
│   │
│   ├── 📁 application/              # 🎯 APPLICATION LAYER
│   │   ├── commands/                # Command handlers (CQRS)
│   │   ├── queries/                 # Query handlers (CQRS)
│   │   ├── dto/                     # Data transfer objects
│   │   └── use-cases/               # Application use cases
│   │
│   ├── 📁 infrastructure/           # 🏗️ INFRASTRUCTURE LAYER
│   │   ├── database/                # Database configuration
│   │   ├── repositories/            # Repository implementations
│   │   ├── external-services/       # External API clients
│   │   └── messaging/               # Message queue setup
│   │
│   ├── 📁 presentation/             # 🌐 PRESENTATION LAYER
│   │   ├── controllers/             # REST controllers
│   │   ├── graphql/                 # GraphQL resolvers
│   │   ├── middleware/              # Custom middleware
│   │   └── guards/                  # Authentication guards
│   │
│   ├── 📁 shared/                   # 🔧 SHARED UTILITIES
│   │   ├── decorators/              # Custom decorators
│   │   ├── filters/                 # Exception filters
│   │   ├── interceptors/            # Response interceptors
│   │   └── utils/                   # Utility functions
│   │
│   └── 📁 config/                   # ⚙️ CONFIGURATION
│       ├── database.config.ts       # Database configuration
│       ├── auth.config.ts           # Authentication configuration
│       └── app.config.ts            # Application configuration
│
├── 📁 test/                         # 🧪 TESTING
│   ├── unit/                        # Unit tests
│   ├── integration/                 # Integration tests
│   ├── e2e/                         # End-to-end tests
│   └── fixtures/                    # Test data fixtures
│
├── 📁 docs/                         # 📚 DOCUMENTATION
│   ├── api.md                       # API documentation
│   ├── deployment.md                # Deployment guide
│   └── development.md               # Development guide
│
└── 📁 scripts/                      # 🤖 AUTOMATION SCRIPTS
    ├── setup.sh                     # Setup script
    ├── test.sh                      # Testing script
    └── deploy.sh                    # Deployment script
```

## 🎨 **Features Included**

### **🔐 Authentication & Security**

- JWT token authentication
- Role-based access control (RBAC)
- API rate limiting
- Input validation and sanitization
- CORS configuration
- Helmet security headers

### **💾 Database Integration**

- TypeORM with PostgreSQL
- Redis for caching and sessions
- Database migrations
- Connection pooling
- Query optimization

### **📊 Monitoring & Observability**

- Structured logging with Winston
- Health check endpoints
- Prometheus metrics
- Request tracing
- Error tracking

### **🧪 Testing Framework**

- Jest for unit testing
- Supertest for integration testing
- Test database setup
- Mock services and repositories
- Coverage reporting

### **🚀 DevOps Ready**

- Docker containerization
- Docker Compose for local development
- GitHub Actions CI/CD
- Environment-based configuration
- Production-ready deployment

## 💻 **Usage Examples**

### **🎯 Creating a New Entity**

```typescript
// src/domain/entities/user.entity.ts
import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity("users")
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  name: string;

  @Column()
  password: string;

  @Column({ default: true })
  isActive: boolean;
}
```

### **🎯 Creating a Controller**

```typescript
// src/presentation/controllers/user.controller.ts
import { Controller, Get, Post, Body, Param, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger";
import { JwtAuthGuard } from "../guards/jwt-auth.guard";
import { CreateUserDto } from "../application/dto/create-user.dto";

@ApiTags("users")
@Controller("users")
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: "Create a new user" })
  async create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get user by ID" })
  async findOne(@Param("id") id: string) {
    return this.userService.findOne(id);
  }
}
```

### **🎯 Creating a Service**

```typescript
// src/application/use-cases/user.service.ts
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { User } from "../domain/entities/user.entity";
import { CreateUserDto } from "./dto/create-user.dto";

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  async findOne(id: string): Promise<User> {
    return this.userRepository.findOne({ where: { id } });
  }
}
```

### **🎯 Writing Tests**

```typescript
// test/unit/user.service.spec.ts
import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { UserService } from "../../src/application/use-cases/user.service";
import { User } from "../../src/domain/entities/user.entity";

describe("UserService", () => {
  let service: UserService;
  let repository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it("should create a user", async () => {
    const createUserDto = { email: "<EMAIL>", name: "Test User" };
    const user = { id: "1", ...createUserDto };

    jest.spyOn(repository, "create").mockReturnValue(user as User);
    jest.spyOn(repository, "save").mockResolvedValue(user as User);

    expect(await service.create(createUserDto)).toEqual(user);
  });
});
```

## 🔧 **Configuration**

### **📋 Environment Variables**

```bash
# .env.template
# Application
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=nestjs_service

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# External Services
EXTERNAL_API_URL=https://api.example.com
EXTERNAL_API_KEY=your-api-key
```

### **📦 Package.json Scripts**

```json
{
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "migration:generate": "typeorm migration:generate",
    "migration:run": "typeorm migration:run",
    "migration:revert": "typeorm migration:revert"
  }
}
```

## 🚀 **Deployment**

### **🐳 Docker Deployment**

```bash
# Build and run with Docker
docker build -t nestjs-service .
docker run -p 3000:3000 nestjs-service

# Using Docker Compose
docker-compose up -d
```

### **☸️ Kubernetes Deployment**

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nestjs-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nestjs-service
  template:
    metadata:
      labels:
        app: nestjs-service
    spec:
      containers:
        - name: nestjs-service
          image: nestjs-service:latest
          ports:
            - containerPort: 3000
          env:
            - name: DATABASE_HOST
              value: "postgres-service"
            - name: REDIS_HOST
              value: "redis-service"
```

## 📊 **Monitoring & Health Checks**

### **🏥 Health Check Endpoint**

```typescript
// src/presentation/controllers/health.controller.ts
import { Controller, Get } from "@nestjs/common";
import {
  HealthCheck,
  HealthCheckService,
  TypeOrmHealthIndicator,
} from "@nestjs/terminus";

@Controller("health")
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([() => this.db.pingCheck("database")]);
  }
}
```

### **📈 Metrics Collection**

```typescript
// src/shared/interceptors/metrics.interceptor.ts
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from "@nestjs/common";
import { Observable } from "rxjs";
import { tap } from "rxjs/operators";
import * as prometheus from "prom-client";

@Injectable()
export class MetricsInterceptor implements NestInterceptor {
  private readonly httpRequestDuration = new prometheus.Histogram({
    name: "http_request_duration_seconds",
    help: "Duration of HTTP requests in seconds",
    labelNames: ["method", "route", "status"],
  });

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    const request = context.switchToHttp().getRequest();

    return next.handle().pipe(
      tap(() => {
        const duration = (Date.now() - start) / 1000;
        this.httpRequestDuration
          .labels(request.method, request.route?.path, "200")
          .observe(duration);
      })
    );
  }
}
```

## 🎓 **Best Practices**

### **🏗️ Architecture Guidelines**

- Follow Clean Architecture principles
- Implement Domain-Driven Design patterns
- Use CQRS for complex business logic
- Separate concerns across layers
- Keep domain logic pure and testable

### **🔒 Security Best Practices**

- Always validate input data
- Use parameterized queries
- Implement proper authentication
- Apply principle of least privilege
- Log security events

### **⚡ Performance Optimization**

- Use database indexing strategically
- Implement caching where appropriate
- Optimize N+1 query problems
- Use connection pooling
- Monitor and profile regularly

### **🧪 Testing Strategy**

- Write tests before implementation (TDD)
- Maintain high test coverage (>90%)
- Use test doubles for external dependencies
- Test edge cases and error scenarios
- Keep tests fast and reliable

## 🤝 **Contributing**

1. **Fork the template repository**
2. **Create your feature branch** (`git checkout -b feature/amazing-feature`)
3. **Follow coding standards** and write tests
4. **Commit your changes** (`git commit -m 'Add amazing feature'`)
5. **Push to the branch** (`git push origin feature/amazing-feature`)
6. **Open a Pull Request**

## 📞 **Support**

- **📖 Documentation**: [NestJS Docs](https://docs.nestjs.com/)
- **💬 Community**: [NestJS Discord](https://discord.gg/nestjs)
- **🐛 Issues**: [Report Issues](https://github.com/enterprise-platform/issues)
- **📧 Email**: <EMAIL>

---

**🎯 This NestJS template is your foundation for building scalable, maintainable microservices. Use it to accelerate development while maintaining enterprise standards!**
