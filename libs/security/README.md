# 🔒 Enterprise Security Framework

> **Security by Design** - Implementing Zero Trust Architecture với Defense in Depth

## 📋 Tổng Quan

Security framework này implement **comprehensive security strategy** theo knowledge base:

### **Core Security Principles**
- **CIA Triad**: Confidentiality, Integrity, Availability
- **Zero Trust**: Never trust, always verify
- **Defense in Depth**: Multiple security layers
- **Least Privilege**: Minimum necessary access
- **Security by Design**: Built-in từ đầu

### **Compliance Standards**
- **OWASP Top 10**: Web application security risks
- **GDPR**: Data protection và privacy
- **SOC 2**: Security controls framework
- **ISO 27001**: Information security management

## 🏗️ Security Architecture

### **Multi-Layer Security Model**
```
┌─────────────────────────────────────────┐
│           Application Layer             │ ← Input Validation, Output Encoding
├─────────────────────────────────────────┤
│            API Gateway Layer            │ ← Authentication, Authorization, Rate Limiting
├─────────────────────────────────────────┤
│           Transport Layer               │ ← TLS/HTTPS, Certificate Management
├─────────────────────────────────────────┤
│            Network Layer                │ ← Firewalls, VPN, Network Segmentation
├─────────────────────────────────────────┤
│         Infrastructure Layer            │ ← Container Security, OS Hardening
└─────────────────────────────────────────┘
```

### **Security Components**
| Component | Technology | Purpose |
|-----------|------------|---------|
| **Authentication** | OAuth2, JWT, MFA | Identity verification |
| **Authorization** | RBAC, ABAC | Access control |
| **Encryption** | AES-256, RSA | Data protection |
| **Secrets Management** | HashiCorp Vault | Secure credential storage |
| **API Security** | Rate limiting, CORS | API protection |
| **Container Security** | Distroless images, Scanning | Container hardening |
| **Network Security** | TLS, VPN, Firewall | Network protection |

## 🔐 Authentication & Authorization

### **OAuth2 + JWT Implementation**
```typescript
/**
 * JWT Authentication Service
 * Theo OAuth2 và JWT best practices
 */
@Injectable()
export class AuthenticationService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly configService: ConfigService
  ) {}

  async authenticate(credentials: LoginCredentials): Promise<AuthResult> {
    // 1. Validate input
    const validation = await this.validateCredentials(credentials);
    if (validation.isFailure) {
      return AuthResult.fail(validation.error);
    }

    // 2. Verify user credentials
    const user = await this.userService.validateUser(
      credentials.email,
      credentials.password
    );
    
    if (!user) {
      // Prevent timing attacks
      await this.simulatePasswordCheck();
      return AuthResult.fail('Invalid credentials');
    }

    // 3. Check account status
    if (!user.isActive || user.isLocked) {
      return AuthResult.fail('Account is disabled');
    }

    // 4. Generate tokens
    const tokens = await this.generateTokens(user);
    
    // 5. Log successful authentication
    await this.auditService.logAuthentication(user.id, 'SUCCESS');
    
    return AuthResult.success(tokens);
  }

  private async generateTokens(user: User): Promise<TokenPair> {
    const payload: JwtPayload = {
      sub: user.id.value,
      email: user.email.value,
      roles: user.roles.map(r => r.name),
      permissions: user.getPermissions(),
      iat: Math.floor(Date.now() / 1000),
      iss: this.configService.get('JWT_ISSUER'),
      aud: this.configService.get('JWT_AUDIENCE')
    };

    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: '15m', // Short-lived access token
      secret: this.configService.get('JWT_ACCESS_SECRET')
    });

    const refreshToken = await this.jwtService.signAsync(
      { sub: user.id.value, type: 'refresh' },
      {
        expiresIn: '7d', // Longer-lived refresh token
        secret: this.configService.get('JWT_REFRESH_SECRET')
      }
    );

    return { accessToken, refreshToken };
  }
}
```

### **Role-Based Access Control (RBAC)**
```typescript
/**
 * RBAC Authorization Guard
 * Theo Least Privilege principle
 */
@Injectable()
export class RBACGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(
      'roles',
      [context.getHandler(), context.getClass()]
    );

    if (!requiredRoles) {
      return true; // No roles required
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    const hasRole = requiredRoles.some(role => 
      user.roles?.includes(role)
    );

    if (!hasRole) {
      throw new ForbiddenException(
        `Insufficient permissions. Required roles: ${requiredRoles.join(', ')}`
      );
    }

    return true;
  }
}

// Usage with decorators
@Controller('admin')
@UseGuards(JwtAuthGuard, RBACGuard)
export class AdminController {
  @Get('users')
  @Roles('admin', 'user-manager')
  async getUsers() {
    // Only admin or user-manager can access
  }

  @Delete('users/:id')
  @Roles('admin')
  async deleteUser(@Param('id') id: string) {
    // Only admin can delete users
  }
}
```

## 🛡️ Input Validation & Sanitization

### **Comprehensive Input Validation**
```typescript
/**
 * Input Validation với Class Validator
 * Theo OWASP Input Validation guidelines
 */
export class CreateUserDto {
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email is required' })
  @MaxLength(254, { message: 'Email too long' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  email: string;

  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  @MinLength(2, { message: 'Name too short' })
  @MaxLength(100, { message: 'Name too long' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'Name contains invalid characters' })
  @Transform(({ value }) => value?.trim())
  name: string;

  @IsString({ message: 'Password must be a string' })
  @MinLength(8, { message: 'Password must be at least 8 characters' })
  @MaxLength(128, { message: 'Password too long' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    { message: 'Password must contain uppercase, lowercase, number and special character' }
  )
  password: string;

  @IsOptional()
  @IsPhoneNumber(null, { message: 'Invalid phone number' })
  phone?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : undefined)
  birthDate?: string;
}

/**
 * SQL Injection Prevention
 * Sử dụng Parameterized Queries
 */
@Injectable()
export class UserRepository {
  constructor(private readonly db: Database) {}

  async findByEmail(email: string): Promise<User | null> {
    // ✅ Safe - Parameterized query
    const query = `
      SELECT id, email, name, created_at 
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `;
    
    const result = await this.db.query(query, [email]);
    return result.rows[0] ? this.mapToUser(result.rows[0]) : null;
  }

  async search(filters: UserSearchFilters): Promise<User[]> {
    // ✅ Safe - Query builder với validation
    const queryBuilder = this.db
      .select('*')
      .from('users')
      .where('deleted_at', 'IS', null);

    if (filters.name) {
      // Sanitize input trước khi sử dụng
      const safeName = filters.name.replace(/[%_]/g, '\\$&');
      queryBuilder.where('name', 'ILIKE', `%${safeName}%`);
    }

    if (filters.email) {
      queryBuilder.where('email', '=', filters.email);
    }

    return queryBuilder.execute();
  }
}
```

## 🔒 Data Encryption

### **Encryption at Rest & in Transit**
```typescript
/**
 * Encryption Service
 * Theo AES-256 encryption standards
 */
@Injectable()
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16;  // 128 bits
  private readonly tagLength = 16; // 128 bits

  constructor(private readonly configService: ConfigService) {}

  /**
   * Encrypt sensitive data
   */
  encrypt(plaintext: string): EncryptedData {
    const key = this.getEncryptionKey();
    const iv = crypto.randomBytes(this.ivLength);
    
    const cipher = crypto.createCipher(this.algorithm, key, iv);
    
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData: EncryptedData): string {
    const key = this.getEncryptionKey();
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const tag = Buffer.from(encryptedData.tag, 'hex');
    
    const decipher = crypto.createDecipher(this.algorithm, key, iv);
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Hash passwords với bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12; // High cost factor
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify password hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  private getEncryptionKey(): Buffer {
    const key = this.configService.get('ENCRYPTION_KEY');
    if (!key) {
      throw new Error('Encryption key not configured');
    }
    return Buffer.from(key, 'hex');
  }
}
```

## 🚨 Security Monitoring & Incident Response

### **Security Event Logging**
```typescript
/**
 * Security Audit Service
 * Theo Security Monitoring best practices
 */
@Injectable()
export class SecurityAuditService {
  constructor(
    private readonly logger: Logger,
    private readonly alertService: AlertService
  ) {}

  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    const auditLog = {
      timestamp: new Date().toISOString(),
      eventType: event.type,
      severity: event.severity,
      userId: event.userId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      resource: event.resource,
      action: event.action,
      result: event.result,
      details: event.details,
      sessionId: event.sessionId
    };

    // Log to security audit trail
    this.logger.warn('SECURITY_EVENT', auditLog);

    // Send alerts for high-severity events
    if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {
      await this.alertService.sendSecurityAlert(auditLog);
    }

    // Check for suspicious patterns
    await this.detectSuspiciousActivity(event);
  }

  private async detectSuspiciousActivity(event: SecurityEvent): Promise<void> {
    // Multiple failed login attempts
    if (event.type === 'AUTHENTICATION_FAILED') {
      const recentFailures = await this.countRecentFailures(
        event.ipAddress,
        event.userId
      );
      
      if (recentFailures >= 5) {
        await this.triggerAccountLockdown(event.userId);
        await this.alertService.sendAlert({
          type: 'BRUTE_FORCE_DETECTED',
          severity: 'HIGH',
          details: `Multiple failed login attempts detected for user ${event.userId}`
        });
      }
    }

    // Unusual access patterns
    if (event.type === 'RESOURCE_ACCESS') {
      const isUnusualAccess = await this.analyzeAccessPattern(event);
      if (isUnusualAccess) {
        await this.alertService.sendAlert({
          type: 'UNUSUAL_ACCESS_PATTERN',
          severity: 'MEDIUM',
          details: `Unusual access pattern detected for user ${event.userId}`
        });
      }
    }
  }
}
```

## 🔧 Security Configuration

### **Environment-based Security Settings**
```typescript
/**
 * Security Configuration
 * Theo Environment-specific security settings
 */
export const securityConfig = {
  development: {
    jwt: {
      accessTokenExpiry: '1h',
      refreshTokenExpiry: '24h',
      issuer: 'enterprise-dev',
      audience: 'enterprise-app-dev'
    },
    cors: {
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000 // requests per window
    },
    encryption: {
      algorithm: 'aes-256-gcm',
      keyRotationDays: 90
    }
  },
  
  production: {
    jwt: {
      accessTokenExpiry: '15m', // Shorter in production
      refreshTokenExpiry: '7d',
      issuer: 'enterprise-prod',
      audience: 'enterprise-app'
    },
    cors: {
      origin: ['https://app.enterprise.com'],
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      max: 100 // Stricter in production
    },
    encryption: {
      algorithm: 'aes-256-gcm',
      keyRotationDays: 30 // More frequent rotation
    },
    security: {
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      csp: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"]
      }
    }
  }
};
```

## 📊 Security Metrics & KPIs

### **Security Dashboard Metrics**
- **Authentication Success Rate**: >99%
- **Failed Login Attempts**: <1% of total attempts
- **API Response Time**: <200ms (P95)
- **Security Incidents**: 0 critical, <5 medium per month
- **Vulnerability Scan Results**: 0 high/critical findings
- **Certificate Expiry**: >30 days remaining
- **Security Training Completion**: 100% of team

### **Compliance Reporting**
- **GDPR Compliance**: Data processing logs, consent tracking
- **SOC 2**: Access controls, monitoring, incident response
- **OWASP**: Regular security assessments, penetration testing
- **ISO 27001**: Risk assessments, security policies

---

**🎯 Mục tiêu**: Tạo ra security framework toàn diện, đảm bảo bảo mật ở mọi layer theo industry best practices và compliance requirements.
