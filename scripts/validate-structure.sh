#!/bin/bash

# 🔍 Enterprise Platform Structure Validation Script
# Comprehensive validation of the restructured enterprise architecture

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Emojis
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
MAGNIFYING="🔍"
SPARKLES="✨"

# Validation counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_status() {
    local color=$1
    local emoji=$2
    local message=$3
    echo -e "${color}${emoji} ${message}${NC}"
}

print_success() {
    print_status "$GREEN" "$CHECK" "$1"
    ((PASSED_CHECKS++))
    ((TOTAL_CHECKS++))
}

print_error() {
    print_status "$RED" "$CROSS" "$1"
    ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
}

print_warning() {
    print_status "$YELLOW" "$WARNING" "$1"
    ((WARNING_CHECKS++))
    ((TOTAL_CHECKS++))
}

print_info() { print_status "$BLUE" "$INFO" "$1"; }
print_header() { print_status "$PURPLE" "$MAGNIFYING" "$1"; }

# Function to check if file exists
check_file_exists() {
    local file_path="$1"
    local description="$2"

    if [ -f "$PROJECT_ROOT/$file_path" ]; then
        print_success "$description exists: $file_path"
        return 0
    else
        print_error "$description missing: $file_path"
        return 1
    fi
}

# Function to check if directory exists
check_directory_exists() {
    local dir_path="$1"
    local description="$2"

    if [ -d "$PROJECT_ROOT/$dir_path" ]; then
        print_success "$description exists: $dir_path"
        return 0
    else
        print_error "$description missing: $dir_path"
        return 1
    fi
}

# Function to check file content
check_file_content() {
    local file_path="$1"
    local pattern="$2"
    local description="$3"

    if [ -f "$PROJECT_ROOT/$file_path" ]; then
        if grep -q "$pattern" "$PROJECT_ROOT/$file_path"; then
            print_success "$description found in $file_path"
            return 0
        else
            print_warning "$description not found in $file_path"
            return 1
        fi
    else
        print_error "File not found for content check: $file_path"
        return 1
    fi
}

# Function to validate root structure
validate_root_structure() {
    print_header "Validating Root Structure"

    # Essential root files
    check_file_exists "README.md" "Main README"
    check_file_exists "Makefile" "Build automation"
    check_file_exists "package.json" "Root package configuration"
    check_file_exists ".gitignore" "Git ignore rules"
    check_file_exists "docker-compose.yml" "Docker Compose configuration"

    # Essential directories
    check_directory_exists "docs" "Documentation hub"
    check_directory_exists "services" "Microservices directory"
    check_directory_exists "templates" "Code templates directory"
    check_directory_exists "scripts" "Automation scripts directory"
    check_directory_exists "tools" "Development tools directory"
    check_directory_exists "examples" "Implementation examples directory"

    echo ""
}

# Function to validate documentation structure
validate_documentation_structure() {
    print_header "Validating Documentation Structure"

    # Main documentation files
    check_file_exists "docs/README.md" "Documentation hub README"
    check_file_exists "docs/NAVIGATION.md" "Navigation guide"
    check_file_exists "docs/PROJECT_RESTRUCTURE_PLAN.md" "Restructure plan"

    # Documentation sections
    local doc_sections=(
        "01-getting-started"
        "02-architecture"
        "03-development"
        "04-api"
        "05-deployment"
        "06-operations"
        "07-knowledge-base"
        "08-tutorials"
        "09-reference"
        "10-contributing"
    )

    for section in "${doc_sections[@]}"; do
        check_directory_exists "docs/$section" "Documentation section: $section"
    done

    echo ""
}

# Function to validate templates structure
validate_templates_structure() {
    print_header "Validating Templates Structure"

    # Main templates README
    check_file_exists "templates/README.md" "Templates overview"

    # Template categories
    local template_categories=(
        "service"
        "component"
        "infrastructure"
        "testing"
        "documentation"
    )

    for category in "${template_categories[@]}"; do
        check_directory_exists "templates/$category" "Template category: $category"
    done

    # Specific service templates
    check_directory_exists "templates/service/nestjs-service" "NestJS service template"
    check_file_exists "templates/service/nestjs-service/README.md" "NestJS template documentation"

    echo ""
}

# Function to validate scripts structure
validate_scripts_structure() {
    print_header "Validating Scripts Structure"

    # Main automation scripts
    check_file_exists "scripts/setup.sh" "Main setup script"
    check_file_exists "scripts/health-check.sh" "Health check script"

    # Script directories
    local script_dirs=(
        "setup"
        "deployment"
        "maintenance"
        "monitoring"
    )

    for dir in "${script_dirs[@]}"; do
        check_directory_exists "scripts/$dir" "Script directory: $dir"
    done

    # Tools and generators
    check_directory_exists "tools/generators" "Code generators directory"
    check_file_exists "tools/generators/generate-service.sh" "Service generator script"

    echo ""
}

# Function to validate content quality
validate_content_quality() {
    print_header "Validating Content Quality"

    # Check README content
    check_file_content "README.md" "Enterprise-Grade Universal Software Architecture" "Main project title"
    check_file_content "README.md" "5-Minute Quick Start" "Quick start section"
    check_file_content "README.md" "Complete Technology Stack" "Technology stack section"

    # Check documentation navigation
    check_file_content "docs/NAVIGATION.md" "Complete Navigation Guide" "Navigation title"
    check_file_content "docs/NAVIGATION.md" "Learning Paths by Experience Level" "Learning paths section"

    # Check templates documentation
    check_file_content "templates/README.md" "Enterprise Code Templates" "Templates title"
    check_file_content "templates/README.md" "Production-ready templates" "Templates description"

    echo ""
}

# Function to validate file permissions
validate_file_permissions() {
    print_header "Validating File Permissions"

    # Check script executability
    local scripts=(
        "scripts/setup.sh"
        "scripts/health-check.sh"
        "tools/generators/generate-service.sh"
    )

    for script in "${scripts[@]}"; do
        if [ -f "$PROJECT_ROOT/$script" ]; then
            if [ -x "$PROJECT_ROOT/$script" ]; then
                print_success "Script is executable: $script"
            else
                print_warning "Script is not executable: $script"
                print_info "Run: chmod +x $script"
            fi
        fi
    done

    echo ""
}

# Function to validate JSON files
validate_json_files() {
    print_header "Validating JSON Files"

    # Find and validate all JSON files
    find "$PROJECT_ROOT" -name "*.json" -not -path "*/node_modules/*" | while read -r json_file; do
        local relative_path="${json_file#$PROJECT_ROOT/}"

        if command -v jq >/dev/null 2>&1; then
            if jq empty "$json_file" >/dev/null 2>&1; then
                print_success "Valid JSON: $relative_path"
            else
                print_error "Invalid JSON: $relative_path"
            fi
        else
            if python3 -m json.tool "$json_file" >/dev/null 2>&1; then
                print_success "Valid JSON: $relative_path"
            else
                print_error "Invalid JSON: $relative_path"
            fi
        fi
    done

    echo ""
}

# Function to validate markdown files
validate_markdown_files() {
    print_header "Validating Markdown Files"

    local markdown_files=(
        "README.md"
        "docs/README.md"
        "docs/NAVIGATION.md"
        "docs/PROJECT_RESTRUCTURE_PLAN.md"
        "templates/README.md"
        "templates/service/nestjs-service/README.md"
    )

    for md_file in "${markdown_files[@]}"; do
        if [ -f "$PROJECT_ROOT/$md_file" ]; then
            # Check for basic markdown structure
            if grep -q "^#" "$PROJECT_ROOT/$md_file"; then
                print_success "Valid markdown structure: $md_file"
            else
                print_warning "No headers found in: $md_file"
            fi

            # Check for broken internal links (simplified check)
            if grep -q "\[.*\](.*\.md)" "$PROJECT_ROOT/$md_file"; then
                print_info "Internal links found in: $md_file (manual verification recommended)"
            fi
        fi
    done

    echo ""
}

# Function to validate consistency
validate_consistency() {
    print_header "Validating Consistency"

    # Check for consistent naming conventions
    local inconsistent_names=0

    # Check service directories follow kebab-case
    if [ -d "$PROJECT_ROOT/services" ]; then
        for service_dir in "$PROJECT_ROOT/services"/*; do
            if [ -d "$service_dir" ]; then
                local service_name=$(basename "$service_dir")
                if [[ ! "$service_name" =~ ^[a-z][a-z0-9-]*[a-z0-9]$ ]]; then
                    print_warning "Service name not in kebab-case: $service_name"
                    ((inconsistent_names++))
                fi
            fi
        done
    fi

    if [ $inconsistent_names -eq 0 ]; then
        print_success "All service names follow kebab-case convention"
    fi

    # Check for consistent documentation structure
    local missing_readmes=0
    local important_dirs=(
        "docs"
        "templates"
        "services"
        "examples"
    )

    for dir in "${important_dirs[@]}"; do
        if [ -d "$PROJECT_ROOT/$dir" ] && [ ! -f "$PROJECT_ROOT/$dir/README.md" ]; then
            print_warning "Missing README.md in: $dir"
            ((missing_readmes++))
        fi
    done

    if [ $missing_readmes -eq 0 ]; then
        print_success "All important directories have README.md files"
    fi

    echo ""
}

# Function to validate completeness
validate_completeness() {
    print_header "Validating Completeness"

    # Check if all planned sections are implemented
    local planned_sections=(
        "docs/01-getting-started"
        "docs/02-architecture"
        "docs/03-development"
        "docs/04-api"
        "docs/05-deployment"
        "docs/06-operations"
        "docs/07-knowledge-base"
        "docs/08-tutorials"
        "docs/09-reference"
        "docs/10-contributing"
    )

    local missing_sections=0
    for section in "${planned_sections[@]}"; do
        if [ ! -d "$PROJECT_ROOT/$section" ]; then
            print_warning "Planned section not implemented: $section"
            ((missing_sections++))
        fi
    done

    if [ $missing_sections -eq 0 ]; then
        print_success "All planned documentation sections are present"
    else
        print_info "$missing_sections documentation sections need to be implemented"
    fi

    # Check template completeness
    local planned_templates=(
        "templates/service/nestjs-service"
        "templates/service/fastapi-service"
        "templates/service/go-service"
        "templates/component/react-component"
        "templates/infrastructure/docker"
        "templates/testing/unit-test"
    )

    local missing_templates=0
    for template in "${planned_templates[@]}"; do
        if [ ! -d "$PROJECT_ROOT/$template" ]; then
            print_warning "Planned template not implemented: $template"
            ((missing_templates++))
        fi
    done

    if [ $missing_templates -eq 0 ]; then
        print_success "All planned templates are present"
    else
        print_info "$missing_templates templates need to be implemented"
    fi

    echo ""
}

# Function to generate validation report
generate_validation_report() {
    local report_file="validation-report-$(date +%Y%m%d-%H%M%S).md"

    cat > "$report_file" << EOF
# Enterprise Platform Validation Report

**Generated on:** $(date)
**Project Root:** $PROJECT_ROOT

## Summary

- **Total Checks:** $TOTAL_CHECKS
- **Passed:** $PASSED_CHECKS
- **Failed:** $FAILED_CHECKS
- **Warnings:** $WARNING_CHECKS

## Validation Score

**Overall Score:** $(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))%

### Score Breakdown
- **Excellent (90-100%):** All systems are properly structured
- **Good (80-89%):** Minor issues that should be addressed
- **Fair (70-79%):** Several issues need attention
- **Poor (<70%):** Major restructuring required

## Recommendations

EOF

    if [ $FAILED_CHECKS -eq 0 ] && [ $WARNING_CHECKS -eq 0 ]; then
        echo "✅ **Perfect Structure!** No issues found." >> "$report_file"
    elif [ $FAILED_CHECKS -eq 0 ]; then
        echo "⚠️ **Good Structure** with minor warnings. Address warnings for optimal setup." >> "$report_file"
    elif [ $FAILED_CHECKS -le 3 ]; then
        echo "🔧 **Needs Attention** - Fix failed checks and address warnings." >> "$report_file"
    else
        echo "🚨 **Major Issues** - Significant restructuring needed." >> "$report_file"
    fi

    cat >> "$report_file" << EOF

## Next Steps

1. **Address Failed Checks:** Fix all items marked with ❌
2. **Review Warnings:** Consider addressing items marked with ⚠️
3. **Complete Missing Sections:** Implement any missing documentation or templates
4. **Run Tests:** Execute \`make test\` to verify functionality
5. **Update Documentation:** Ensure all documentation is current

## Validation Details

For detailed validation output, see the console output from this script.

---

**Generated by:** Enterprise Platform Validation Script
**Version:** 1.0.0
EOF

    print_success "Validation report generated: $report_file"
}

# Main validation function
main_validation() {
    print_header "Enterprise Platform Structure Validation"
    echo ""
    print_info "Validating project structure and content quality..."
    echo ""

    # Run all validation checks
    validate_root_structure
    validate_documentation_structure
    validate_templates_structure
    validate_scripts_structure
    validate_content_quality
    validate_file_permissions
    validate_json_files
    validate_markdown_files
    validate_consistency
    validate_completeness

    # Display summary
    print_header "Validation Summary"
    echo ""
    echo -e "${CYAN}📊 Validation Results:${NC}"
    echo -e "${GREEN}  ✅ Passed: $PASSED_CHECKS${NC}"
    echo -e "${RED}  ❌ Failed: $FAILED_CHECKS${NC}"
    echo -e "${YELLOW}  ⚠️  Warnings: $WARNING_CHECKS${NC}"
    echo -e "${BLUE}  📋 Total: $TOTAL_CHECKS${NC}"
    echo ""

    # Calculate score
    local score=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    echo -e "${PURPLE}🎯 Overall Score: $score%${NC}"

    if [ $score -ge 90 ]; then
        echo -e "${GREEN}🌟 Excellent! Your enterprise platform structure is outstanding.${NC}"
    elif [ $score -ge 80 ]; then
        echo -e "${YELLOW}👍 Good structure with minor improvements needed.${NC}"
    elif [ $score -ge 70 ]; then
        echo -e "${YELLOW}⚠️ Fair structure but several issues need attention.${NC}"
    else
        echo -e "${RED}🚨 Poor structure - major improvements required.${NC}"
    fi

    echo ""

    # Generate report if requested
    if [ "${1:-}" = "--report" ]; then
        generate_validation_report
    fi

    # Return appropriate exit code
    if [ $FAILED_CHECKS -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# Parse command line arguments
case "${1:-validate}" in
    "validate"|"")
        main_validation
        ;;
    "report")
        main_validation --report
        ;;
    "help"|"-h"|"--help")
        echo "Enterprise Platform Structure Validation Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  validate    Run structure validation (default)"
        echo "  report      Run validation and generate report"
        echo "  help        Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Run validation"
        echo "  $0 report            # Run validation and generate report"
        echo ""
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac