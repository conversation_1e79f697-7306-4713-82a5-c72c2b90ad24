# 📱 **MOBILE DEVELOPMENT**

> **Master mobile app development across platforms - From native iOS/Android to cross-platform solutions**

## 🎯 **Overview**

Mobile Development encompasses creating applications for smartphones and tablets across different platforms. This section provides comprehensive knowledge about native iOS and Android development, cross-platform frameworks, mobile UI/UX patterns, and mobile-specific technologies that every mobile developer must master.

### **📊 What You'll Learn**

- **🍎 iOS Development** - Swift, SwiftUI, UIKit, Xcode, App Store deployment
- **🤖 Android Development** - Kotlin, Jetpack Compose, Android Studio, Google Play
- **🔄 Cross-Platform** - React Native, Flutter, Xamarin, hybrid solutions
- **📱 Mobile UI/UX** - Platform-specific design patterns, responsive mobile design
- **⚡ Performance** - Optimization, testing, analytics, deployment strategies

## 📁 **Knowledge Structure**

### **🍎 iOS Development** - [ios/](ios/README.md)

#### **iOS Fundamentals**

| Concept                                       | Description                     | Importance  | Difficulty |
| --------------------------------------------- | ------------------------------- | ----------- | ---------- |
| [Swift Language](ios/swift-language.md)       | Modern iOS programming language | 🔥 Critical | ⭐⭐⭐     |
| [Xcode IDE](ios/xcode.md)                     | Apple's development environment | 🔥 Critical | ⭐⭐       |
| [iOS SDK](ios/ios-sdk.md)                     | iOS frameworks and APIs         | 🔥 Critical | ⭐⭐⭐     |
| [App Lifecycle](ios/app-lifecycle.md)         | iOS app states and transitions  | ⚡ High     | ⭐⭐⭐     |
| [Memory Management](ios/memory-management.md) | ARC and memory optimization     | ⚡ High     | ⭐⭐⭐⭐   |

#### **UI Frameworks**

| Framework                         | Approach           | Use Cases              | Learning Curve |
| --------------------------------- | ------------------ | ---------------------- | -------------- |
| [SwiftUI](ios/swiftui.md)         | Declarative UI     | Modern iOS apps        | ⭐⭐⭐         |
| [UIKit](ios/uikit.md)             | Imperative UI      | Legacy and complex UIs | ⭐⭐⭐⭐       |
| [Storyboards](ios/storyboards.md) | Visual UI design   | Rapid prototyping      | ⭐⭐           |
| [Auto Layout](ios/auto-layout.md) | Responsive layouts | All screen sizes       | ⭐⭐⭐         |

#### **iOS Architecture Patterns**

| Pattern                                         | Purpose                                 | Complexity | Popularity |
| ----------------------------------------------- | --------------------------------------- | ---------- | ---------- |
| [MVC](ios/mvc.md)                               | Model-View-Controller                   | ⭐⭐       | 🔥 High    |
| [MVVM](ios/mvvm.md)                             | Model-View-ViewModel                    | ⭐⭐⭐     | 🔥 High    |
| [VIPER](ios/viper.md)                           | View-Interactor-Presenter-Entity-Router | ⭐⭐⭐⭐⭐ | ⚡ Medium  |
| [Clean Architecture](ios/clean-architecture.md) | Layered architecture                    | ⭐⭐⭐⭐   | ⚡ Medium  |

#### **iOS Technologies**

| Technology                                      | Purpose               | Use Cases          | Complexity |
| ----------------------------------------------- | --------------------- | ------------------ | ---------- |
| [Core Data](ios/core-data.md)                   | Data persistence      | Local database     | ⭐⭐⭐⭐   |
| [CloudKit](ios/cloudkit.md)                     | Cloud synchronization | iCloud integration | ⭐⭐⭐     |
| [Core Location](ios/core-location.md)           | Location services     | Maps, geofencing   | ⭐⭐⭐     |
| [Core Animation](ios/core-animation.md)         | Animations            | Smooth transitions | ⭐⭐⭐⭐   |
| [Push Notifications](ios/push-notifications.md) | Remote notifications  | User engagement    | ⭐⭐⭐     |

### **🤖 Android Development** - [android/](android/README.md)

#### **Android Fundamentals**

| Concept                                             | Description                 | Importance  | Difficulty |
| --------------------------------------------------- | --------------------------- | ----------- | ---------- |
| [Kotlin Language](android/kotlin.md)                | Modern Android programming  | 🔥 Critical | ⭐⭐⭐     |
| [Android Studio](android/android-studio.md)         | Official Android IDE        | 🔥 Critical | ⭐⭐       |
| [Android SDK](android/android-sdk.md)               | Android frameworks and APIs | 🔥 Critical | ⭐⭐⭐     |
| [Activity Lifecycle](android/activity-lifecycle.md) | Android component lifecycle | 🔥 Critical | ⭐⭐⭐     |
| [Gradle Build System](android/gradle.md)            | Build automation            | ⚡ High     | ⭐⭐⭐     |

#### **UI Frameworks**

| Framework                                        | Approach            | Use Cases            | Learning Curve |
| ------------------------------------------------ | ------------------- | -------------------- | -------------- |
| [Jetpack Compose](android/jetpack-compose.md)    | Declarative UI      | Modern Android apps  | ⭐⭐⭐         |
| [View System](android/view-system.md)            | Traditional UI      | Legacy apps          | ⭐⭐⭐         |
| [XML Layouts](android/xml-layouts.md)            | Declarative layouts | Traditional approach | ⭐⭐           |
| [ConstraintLayout](android/constraint-layout.md) | Flexible layouts    | Complex UIs          | ⭐⭐⭐         |

#### **Android Architecture**

| Component                                             | Purpose                | Importance  | Complexity |
| ----------------------------------------------------- | ---------------------- | ----------- | ---------- |
| [Activities](android/activities.md)                   | Screen components      | 🔥 Critical | ⭐⭐       |
| [Fragments](android/fragments.md)                     | Reusable UI components | 🔥 Critical | ⭐⭐⭐     |
| [Services](android/services.md)                       | Background processing  | ⚡ High     | ⭐⭐⭐     |
| [Broadcast Receivers](android/broadcast-receivers.md) | System events          | ⚡ High     | ⭐⭐       |
| [Content Providers](android/content-providers.md)     | Data sharing           | ⚡ Medium   | ⭐⭐⭐⭐   |

#### **Android Jetpack**

| Component                             | Purpose              | Use Cases          | Adoption  |
| ------------------------------------- | -------------------- | ------------------ | --------- |
| [Navigation](android/navigation.md)   | App navigation       | Multi-screen apps  | 🔥 High   |
| [Room](android/room.md)               | Database abstraction | Local data storage | 🔥 High   |
| [ViewModel](android/viewmodel.md)     | UI state management  | MVVM architecture  | 🔥 High   |
| [LiveData](android/livedata.md)       | Observable data      | Reactive UI        | ⚡ Medium |
| [WorkManager](android/workmanager.md) | Background tasks     | Scheduled work     | ⚡ Medium |

### **🔄 Cross-Platform Development** - [cross-platform/](cross-platform/README.md)

#### **Cross-Platform Frameworks**

| Framework                                      | Technology            | Performance  | Learning Curve |
| ---------------------------------------------- | --------------------- | ------------ | -------------- |
| [React Native](cross-platform/react-native.md) | JavaScript/TypeScript | ⚡ Good      | ⭐⭐⭐         |
| [Flutter](cross-platform/flutter.md)           | Dart                  | 🔥 Excellent | ⭐⭐⭐         |
| [Xamarin](cross-platform/xamarin.md)           | C#                    | ⚡ Good      | ⭐⭐⭐⭐       |
| [Ionic](cross-platform/ionic.md)               | Web technologies      | ⚡ Fair      | ⭐⭐           |
| [Cordova/PhoneGap](cross-platform/cordova.md)  | Web technologies      | ⚡ Fair      | ⭐⭐           |

#### **Framework Comparison**

| Aspect                | React Native | Flutter   | Xamarin   | Ionic     |
| --------------------- | ------------ | --------- | --------- | --------- |
| **Performance**       | Good         | Excellent | Good      | Fair      |
| **Development Speed** | Fast         | Fast      | Medium    | Very Fast |
| **Code Sharing**      | 70-80%       | 90-95%    | 80-90%    | 95%       |
| **Native Feel**       | Good         | Excellent | Excellent | Fair      |
| **Community**         | Large        | Growing   | Medium    | Medium    |
| **Learning Curve**    | Medium       | Medium    | High      | Low       |

#### **Hybrid Solutions**

| Approach                                      | Technology       | Use Cases           | Complexity |
| --------------------------------------------- | ---------------- | ------------------- | ---------- |
| [Progressive Web Apps](cross-platform/pwa.md) | Web technologies | Web-first apps      | ⭐⭐       |
| [Capacitor](cross-platform/capacitor.md)      | Web + native     | Modern hybrid       | ⭐⭐⭐     |
| [Electron](cross-platform/electron.md)        | Web technologies | Desktop apps        | ⭐⭐⭐     |
| [Tauri](cross-platform/tauri.md)              | Rust + web       | Lightweight desktop | ⭐⭐⭐⭐   |

### **📱 Mobile UI/UX Patterns** - [mobile-ui/](mobile-ui/README.md)

#### **Platform Design Guidelines**

| Platform                                               | Design System           | Key Principles            | Resources        |
| ------------------------------------------------------ | ----------------------- | ------------------------- | ---------------- |
| [iOS Human Interface Guidelines](mobile-ui/ios-hig.md) | iOS design system       | Clarity, deference, depth | Apple HIG        |
| [Material Design](mobile-ui/material-design.md)        | Android design system   | Material metaphor, motion | Google Material  |
| [Fluent Design](mobile-ui/fluent-design.md)            | Microsoft design system | Light, depth, motion      | Microsoft Fluent |

#### **Mobile Navigation Patterns**

| Pattern                                             | Use Cases               | Platforms    | Complexity |
| --------------------------------------------------- | ----------------------- | ------------ | ---------- |
| [Tab Bar](mobile-ui/tab-bar.md)                     | Primary navigation      | iOS, Android | ⭐⭐       |
| [Navigation Drawer](mobile-ui/navigation-drawer.md) | Secondary navigation    | Android      | ⭐⭐⭐     |
| [Bottom Navigation](mobile-ui/bottom-navigation.md) | Primary navigation      | Android      | ⭐⭐       |
| [Stack Navigation](mobile-ui/stack-navigation.md)   | Hierarchical navigation | Both         | ⭐⭐       |

#### **Mobile Interaction Patterns**

| Pattern                                         | Purpose         | Implementation | Difficulty |
| ----------------------------------------------- | --------------- | -------------- | ---------- |
| [Pull to Refresh](mobile-ui/pull-to-refresh.md) | Content refresh | Gesture-based  | ⭐⭐       |
| [Infinite Scroll](mobile-ui/infinite-scroll.md) | Content loading | Pagination     | ⭐⭐⭐     |
| [Swipe Actions](mobile-ui/swipe-actions.md)     | Quick actions   | Gesture-based  | ⭐⭐⭐     |
| [Modal Presentations](mobile-ui/modals.md)      | Focused tasks   | Overlay UI     | ⭐⭐       |

### **⚡ Mobile Performance & Optimization** - [performance/](performance/README.md)

#### **Performance Metrics**

| Metric                                        | Description         | Target           | Tools                               |
| --------------------------------------------- | ------------------- | ---------------- | ----------------------------------- |
| [App Launch Time](performance/launch-time.md) | Time to interactive | <2 seconds       | Xcode Instruments, Android Profiler |
| [Frame Rate](performance/frame-rate.md)       | UI smoothness       | 60 FPS           | GPU profilers                       |
| [Memory Usage](performance/memory.md)         | RAM consumption     | <100MB idle      | Memory profilers                    |
| [Battery Usage](performance/battery.md)       | Power consumption   | Minimal drain    | Battery profilers                   |
| [Network Efficiency](performance/network.md)  | Data usage          | Minimal requests | Network monitors                    |

#### **Optimization Techniques**

| Technique                                                     | Purpose            | Impact    | Difficulty |
| ------------------------------------------------------------- | ------------------ | --------- | ---------- |
| [Image Optimization](performance/image-optimization.md)       | Reduce app size    | 🔥 High   | ⭐⭐       |
| [Code Splitting](performance/code-splitting.md)               | Faster loading     | ⚡ Medium | ⭐⭐⭐     |
| [Lazy Loading](performance/lazy-loading.md)                   | Memory efficiency  | ⚡ Medium | ⭐⭐⭐     |
| [Caching Strategies](performance/caching.md)                  | Faster data access | 🔥 High   | ⭐⭐⭐     |
| [Background Processing](performance/background-processing.md) | UI responsiveness  | ⚡ Medium | ⭐⭐⭐⭐   |

## 🎓 **Learning Path**

### **🚀 Beginner Level (0-2 years)**

#### **Phase 1: Platform Fundamentals (3-4 months)**

1. **Choose Your Platform**

   - [iOS Development Basics](fundamentals/ios-basics.md)
   - [Android Development Basics](fundamentals/android-basics.md)
   - [Cross-Platform Overview](fundamentals/cross-platform-overview.md)
   - [Mobile Development Concepts](fundamentals/mobile-concepts.md)

2. **Programming Language**

   - [Swift for iOS](languages/swift.md)
   - [Kotlin for Android](languages/kotlin.md)
   - [Dart for Flutter](languages/dart.md)
   - [JavaScript for React Native](languages/javascript.md)

3. **Development Environment**
   - [Xcode Setup and Usage](tools/xcode-setup.md)
   - [Android Studio Setup](tools/android-studio-setup.md)
   - [Version Control for Mobile](tools/version-control.md)
   - [Debugging and Testing Tools](tools/debugging.md)

#### **Phase 2: UI Development (3-4 months)**

1. **Native UI Development**

   - [SwiftUI Basics](ui/swiftui-basics.md)
   - [Jetpack Compose Basics](ui/compose-basics.md)
   - [Layout Systems](ui/layout-systems.md)
   - [Navigation Implementation](ui/navigation.md)

2. **Mobile Design Patterns**

   - [Platform Design Guidelines](design/platform-guidelines.md)
   - [Responsive Mobile Design](design/responsive-design.md)
   - [Touch Interface Design](design/touch-interfaces.md)
   - [Accessibility in Mobile](design/mobile-accessibility.md)

3. **User Interaction**
   - [Gesture Recognition](interaction/gestures.md)
   - [Animations and Transitions](interaction/animations.md)
   - [Form Design and Validation](interaction/forms.md)
   - [Feedback and Notifications](interaction/feedback.md)

#### **Phase 3: Data and Networking (2-3 months)**

1. **Data Management**

   - [Local Data Storage](data/local-storage.md)
   - [Core Data (iOS)](data/core-data.md)
   - [Room Database (Android)](data/room-database.md)
   - [Data Synchronization](data/synchronization.md)

2. **Networking**
   - [HTTP Requests and APIs](networking/http-apis.md)
   - [JSON Parsing](networking/json-parsing.md)
   - [Image Loading and Caching](networking/image-loading.md)
   - [Offline Functionality](networking/offline.md)

### **⚡ Intermediate Level (2-5 years)**

#### **Phase 1: Advanced Features (3-4 months)**

1. **Platform-Specific Features**

   - [iOS Advanced Features](advanced/ios-advanced.md)
   - [Android Advanced Features](advanced/android-advanced.md)
   - [Camera and Media](advanced/camera-media.md)
   - [Location Services](advanced/location.md)

2. **Performance Optimization**

   - [App Performance Profiling](performance/profiling.md)
   - [Memory Management](performance/memory-management.md)
   - [Battery Optimization](performance/battery-optimization.md)
   - [Network Optimization](performance/network-optimization.md)

3. **Testing and Quality**
   - [Unit Testing](testing/unit-testing.md)
   - [UI Testing](testing/ui-testing.md)
   - [Integration Testing](testing/integration-testing.md)
   - [Test-Driven Development](testing/tdd.md)

#### **Phase 2: Cross-Platform Development (3-4 months)**

1. **React Native**

   - [React Native Fundamentals](cross-platform/rn-fundamentals.md)
   - [Navigation in React Native](cross-platform/rn-navigation.md)
   - [State Management](cross-platform/rn-state.md)
   - [Native Modules](cross-platform/rn-native-modules.md)

2. **Flutter**
   - [Flutter Fundamentals](cross-platform/flutter-fundamentals.md)
   - [Widget System](cross-platform/flutter-widgets.md)
   - [State Management](cross-platform/flutter-state.md)
   - [Platform Channels](cross-platform/flutter-channels.md)

### **🏆 Advanced Level (5+ years)**

#### **Phase 1: Architecture and Scalability (3-4 months)**

1. **Advanced Architecture**

   - [Clean Architecture](architecture/clean-architecture.md)
   - [Modular Architecture](architecture/modular-architecture.md)
   - [Microservices for Mobile](architecture/microservices.md)
   - [Scalable App Design](architecture/scalable-design.md)

2. **DevOps and CI/CD**
   - [Mobile CI/CD Pipelines](devops/ci-cd.md)
   - [Automated Testing](devops/automated-testing.md)
   - [App Store Deployment](devops/app-store-deployment.md)
   - [Monitoring and Analytics](devops/monitoring.md)

#### **Phase 2: Leadership and Innovation (2-3 months)**

1. **Team Leadership**

   - [Mobile Team Management](leadership/team-management.md)
   - [Code Review Best Practices](leadership/code-review.md)
   - [Technical Decision Making](leadership/technical-decisions.md)
   - [Mentoring Mobile Developers](leadership/mentoring.md)

2. **Innovation and Trends**
   - [Emerging Mobile Technologies](innovation/emerging-tech.md)
   - [AR/VR Mobile Development](innovation/ar-vr.md)
   - [AI/ML in Mobile Apps](innovation/ai-ml.md)
   - [IoT and Mobile Integration](innovation/iot-mobile.md)

## 💡 **Practical Applications & Case Studies**

### **🎯 Real-World Mobile Development Projects**

#### **Complete iOS App with SwiftUI**

```swift
// SwiftUI iOS App: Task Management Application
import SwiftUI
import CoreData
import UserNotifications

// MARK: - Data Models
struct Task: Identifiable, Codable {
    let id = UUID()
    var title: String
    var description: String
    var isCompleted: Bool = false
    var priority: Priority = .medium
    var dueDate: Date?
    var createdAt: Date = Date()

    enum Priority: String, CaseIterable, Codable {
        case low = "Low"
        case medium = "Medium"
        case high = "High"
        case urgent = "Urgent"

        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .blue
            case .high: return .orange
            case .urgent: return .red
            }
        }
    }
}

// MARK: - View Models
class TaskViewModel: ObservableObject {
    @Published var tasks: [Task] = []
    @Published var searchText = ""
    @Published var selectedPriority: Task.Priority?
    @Published var showingAddTask = false

    private let userDefaults = UserDefaults.standard
    private let tasksKey = "SavedTasks"

    init() {
        loadTasks()
        requestNotificationPermission()
    }

    var filteredTasks: [Task] {
        var filtered = tasks

        if !searchText.isEmpty {
            filtered = filtered.filter { task in
                task.title.localizedCaseInsensitiveContains(searchText) ||
                task.description.localizedCaseInsensitiveContains(searchText)
            }
        }

        if let priority = selectedPriority {
            filtered = filtered.filter { $0.priority == priority }
        }

        return filtered.sorted { task1, task2 in
            if task1.isCompleted != task2.isCompleted {
                return !task1.isCompleted
            }

            if task1.priority != task2.priority {
                return task1.priority.rawValue > task2.priority.rawValue
            }

            return task1.createdAt > task2.createdAt
        }
    }

    func addTask(_ task: Task) {
        tasks.append(task)
        saveTasks()
        scheduleNotification(for: task)
    }

    func updateTask(_ task: Task) {
        if let index = tasks.firstIndex(where: { $0.id == task.id }) {
            tasks[index] = task
            saveTasks()
        }
    }

    func deleteTask(_ task: Task) {
        tasks.removeAll { $0.id == task.id }
        saveTasks()
        cancelNotification(for: task)
    }

    func toggleTaskCompletion(_ task: Task) {
        if let index = tasks.firstIndex(where: { $0.id == task.id }) {
            tasks[index].isCompleted.toggle()
            saveTasks()

            if tasks[index].isCompleted {
                cancelNotification(for: task)
            } else {
                scheduleNotification(for: tasks[index])
            }
        }
    }

    private func saveTasks() {
        if let encoded = try? JSONEncoder().encode(tasks) {
            userDefaults.set(encoded, forKey: tasksKey)
        }
    }

    private func loadTasks() {
        if let data = userDefaults.data(forKey: tasksKey),
           let decoded = try? JSONDecoder().decode([Task].self, from: data) {
            tasks = decoded
        }
    }

    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if let error = error {
                print("Notification permission error: \(error)")
            }
        }
    }

    private func scheduleNotification(for task: Task) {
        guard let dueDate = task.dueDate, dueDate > Date(), !task.isCompleted else { return }

        let content = UNMutableNotificationContent()
        content.title = "Task Due Soon"
        content.body = task.title
        content.sound = .default
        content.badge = 1

        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: dueDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)

        let request = UNNotificationRequest(identifier: task.id.uuidString, content: content, trigger: trigger)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Notification scheduling error: \(error)")
            }
        }
    }

    private func cancelNotification(for task: Task) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [task.id.uuidString])
    }
}

// MARK: - Views
struct ContentView: View {
    @StateObject private var viewModel = TaskViewModel()

    var body: some View {
        NavigationView {
            VStack {
                SearchBar(text: $viewModel.searchText)

                PriorityFilter(selectedPriority: $viewModel.selectedPriority)

                TaskList(
                    tasks: viewModel.filteredTasks,
                    onToggle: viewModel.toggleTaskCompletion,
                    onDelete: viewModel.deleteTask
                )

                Spacer()
            }
            .navigationTitle("Tasks")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        viewModel.showingAddTask = true
                    }) {
                        Image(systemName: "plus")
                            .font(.title2)
                    }
                }
            }
            .sheet(isPresented: $viewModel.showingAddTask) {
                AddTaskView { task in
                    viewModel.addTask(task)
                }
            }
        }
    }
}

struct SearchBar: View {
    @Binding var text: String

    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search tasks...", text: $text)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.horizontal)
    }
}

struct PriorityFilter: View {
    @Binding var selectedPriority: Task.Priority?

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                FilterChip(
                    title: "All",
                    isSelected: selectedPriority == nil,
                    color: .gray
                ) {
                    selectedPriority = nil
                }

                ForEach(Task.Priority.allCases, id: \.self) { priority in
                    FilterChip(
                        title: priority.rawValue,
                        isSelected: selectedPriority == priority,
                        color: priority.color
                    ) {
                        selectedPriority = selectedPriority == priority ? nil : priority
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? color : Color(.systemGray6))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct TaskList: View {
    let tasks: [Task]
    let onToggle: (Task) -> Void
    let onDelete: (Task) -> Void

    var body: some View {
        List {
            ForEach(tasks) { task in
                TaskRow(
                    task: task,
                    onToggle: { onToggle(task) }
                )
            }
            .onDelete { indexSet in
                for index in indexSet {
                    onDelete(tasks[index])
                }
            }
        }
        .listStyle(PlainListStyle())
    }
}

struct TaskRow: View {
    let task: Task
    let onToggle: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            Button(action: onToggle) {
                Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(task.isCompleted ? .green : .gray)
            }
            .buttonStyle(PlainButtonStyle())

            VStack(alignment: .leading, spacing: 4) {
                Text(task.title)
                    .font(.headline)
                    .strikethrough(task.isCompleted)
                    .foregroundColor(task.isCompleted ? .secondary : .primary)

                if !task.description.isEmpty {
                    Text(task.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                HStack {
                    PriorityBadge(priority: task.priority)

                    if let dueDate = task.dueDate {
                        Spacer()

                        Text(dueDate, style: .date)
                            .font(.caption2)
                            .foregroundColor(dueDate < Date() ? .red : .secondary)
                    }
                }
            }

            Spacer()
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
    }
}

struct PriorityBadge: View {
    let priority: Task.Priority

    var body: some View {
        Text(priority.rawValue)
            .font(.caption2)
            .fontWeight(.semibold)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(priority.color.opacity(0.2))
            )
            .foregroundColor(priority.color)
    }
}

struct AddTaskView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var title = ""
    @State private var description = ""
    @State private var priority = Task.Priority.medium
    @State private var dueDate = Date()
    @State private var hasDueDate = false

    let onSave: (Task) -> Void

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Task Details")) {
                    TextField("Title", text: $title)

                    TextField("Description", text: $description, axis: .vertical)
                        .lineLimit(3...6)
                }

                Section(header: Text("Priority")) {
                    Picker("Priority", selection: $priority) {
                        ForEach(Task.Priority.allCases, id: \.self) { priority in
                            HStack {
                                Circle()
                                    .fill(priority.color)
                                    .frame(width: 12, height: 12)
                                Text(priority.rawValue)
                            }
                            .tag(priority)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                Section(header: Text("Due Date")) {
                    Toggle("Set due date", isOn: $hasDueDate)

                    if hasDueDate {
                        DatePicker("Due date", selection: $dueDate, displayedComponents: [.date, .hourAndMinute])
                    }
                }
            }
            .navigationTitle("New Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        let task = Task(
                            title: title,
                            description: description,
                            priority: priority,
                            dueDate: hasDueDate ? dueDate : nil
                        )
                        onSave(task)
                        presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(title.isEmpty)
                }
            }
        }
    }
}

// MARK: - App Entry Point
@main
struct TaskManagerApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}
```

## 📊 **Assessment & Practice**

### **🧪 Mobile Development Assessment Questions**

#### **Platform Fundamentals**

- [ ] Can you develop native iOS apps using Swift and SwiftUI?
- [ ] Do you understand Android development with Kotlin and Jetpack Compose?
- [ ] Can you implement cross-platform solutions with React Native or Flutter?
- [ ] Do you know mobile app architecture patterns and best practices?

#### **UI/UX Development**

- [ ] Can you create responsive mobile interfaces?
- [ ] Do you understand platform-specific design guidelines?
- [ ] Can you implement smooth animations and transitions?
- [ ] Do you know accessibility best practices for mobile?

#### **Performance & Optimization**

- [ ] Can you optimize app performance and memory usage?
- [ ] Do you understand mobile testing strategies?
- [ ] Can you implement offline functionality and data synchronization?
- [ ] Do you know app store deployment and distribution?

#### **Advanced Features**

- [ ] Can you integrate device features (camera, location, sensors)?
- [ ] Do you understand push notifications and background processing?
- [ ] Can you implement secure authentication and data protection?
- [ ] Do you know mobile analytics and crash reporting?

### **🏋️ Mobile Development Practice Projects**

#### **Beginner Projects**

1. **Todo List App**

   - Create basic CRUD functionality
   - Implement local data storage
   - Add simple animations

2. **Weather App**
   - Integrate with weather API
   - Display current and forecast data
   - Implement location services

#### **Intermediate Projects**

1. **Social Media App**

   - User authentication and profiles
   - Photo sharing and comments
   - Real-time messaging

2. **E-commerce App**
   - Product catalog and search
   - Shopping cart and checkout
   - Payment integration

#### **Advanced Projects**

1. **Fitness Tracking App**

   - Health data integration
   - Workout tracking and analytics
   - Social features and challenges

2. **AR Shopping App**
   - Augmented reality product preview
   - 3D model rendering
   - Advanced camera integration

## 🔗 **Cross-References**

### **Related Knowledge Areas**

- **[Programming Fundamentals](../01-programming-fundamentals/README.md)** - Swift, Kotlin, Dart programming
- **[UI/UX Design](../12-ui-ux-design/README.md)** - Mobile design patterns and principles
- **[Database Engineering](../04-database-engineering/README.md)** - Mobile data storage solutions
- **[DevOps & Cloud](../05-devops-cloud/README.md)** - Mobile CI/CD and deployment
- **[Security](../06-security/README.md)** - Mobile app security best practices

### **Learning Dependencies**

```mermaid
graph TD
    A[Programming Fundamentals] --> B[Platform Choice]
    B --> C[iOS Development]
    B --> D[Android Development]
    B --> E[Cross-Platform]

    C --> F[SwiftUI/UIKit]
    D --> G[Jetpack Compose]
    E --> H[React Native/Flutter]

    F --> I[iOS Advanced Features]
    G --> J[Android Advanced Features]
    H --> K[Cross-Platform Advanced]

    I --> L[Mobile Architecture]
    J --> L
    K --> L

    L --> M[Performance Optimization]
    M --> N[Mobile Leadership]
```

---

**📱 Master Mobile Development to create amazing apps that users love across all platforms. From native iOS and Android to cross-platform solutions, mobile development is the key to reaching billions of users worldwide!**

2. **Performance Optimization**

   - [App Performance Profiling](performance/profiling.md)
   - [Memory Management](performance/memory-management.md)
   - [Battery Optimization](performance/battery-optimization.md)
   - [Network Optimization](performance/network-optimization.md)

3. **Testing and Quality**
   - [Unit Testing](testing/unit-testing.md)
   - [UI Testing](testing/ui-testing.md)
   - [Integration Testing](testing/integration-testing.md)
   - [Test-Driven Development](testing/tdd.md)

#### **Phase 2: Cross-Platform Development (3-4 months)**

1. **React Native**

   - [React Native Fundamentals](cross-platform/rn-fundamentals.md)
   - [Navigation in React Native](cross-platform/rn-navigation.md)
   - [State Management](cross-platform/rn-state.md)
   - [Native Modules](cross-platform/rn-native-modules.md)

2. **Flutter**
   - [Flutter Fundamentals](cross-platform/flutter-fundamentals.md)
   - [Widget System](cross-platform/flutter-widgets.md)
   - [State Management](cross-platform/flutter-state.md)
   - [Platform Channels](cross-platform/flutter-channels.md)

### **🏆 Advanced Level (5+ years)**

#### **Phase 1: Architecture and Scalability (3-4 months)**

1. **Advanced Architecture**

   - [Clean Architecture](architecture/clean-architecture.md)
   - [Modular Architecture](architecture/modular-architecture.md)
   - [Microservices for Mobile](architecture/microservices.md)
   - [Scalable App Design](architecture/scalable-design.md)

2. **DevOps and CI/CD**
   - [Mobile CI/CD Pipelines](devops/ci-cd.md)
   - [Automated Testing](devops/automated-testing.md)
   - [App Store Deployment](devops/app-store-deployment.md)
   - [Monitoring and Analytics](devops/monitoring.md)

#### **Phase 2: Leadership and Innovation (2-3 months)**

1. **Team Leadership**

   - [Mobile Team Management](leadership/team-management.md)
   - [Code Review Best Practices](leadership/code-review.md)
   - [Technical Decision Making](leadership/technical-decisions.md)
   - [Mentoring Mobile Developers](leadership/mentoring.md)

2. **Innovation and Trends**
   - [Emerging Mobile Technologies](innovation/emerging-tech.md)
   - [AR/VR Mobile Development](innovation/ar-vr.md)
   - [AI/ML in Mobile Apps](innovation/ai-ml.md)
   - [IoT and Mobile Integration](innovation/iot-mobile.md)
