# ☁️ **DEVOPS & CLOUD ENGINEERING**

> **Master the art of building, deploying, and scaling applications in the cloud - From containers to Kubernetes to Infrastructure as Code**

## 🎯 **Overview**

DevOps and Cloud Engineering bridge the gap between development and operations, enabling teams to deliver software faster, more reliably, and at scale. This section covers containerization, orchestration, CI/CD pipelines, infrastructure automation, and cloud-native architectures.

### **📊 What You'll Learn**

- **🐳 Containerization** - Docker, container orchestration, microservices deployment
- **☸️ Kubernetes** - Container orchestration, service mesh, cloud-native patterns
- **🔄 CI/CD** - Automated testing, deployment pipelines, GitOps workflows
- **🏗️ Infrastructure as Code** - Terraform, CloudFormation, configuration management
- **📊 Monitoring & Observability** - Logging, metrics, tracing, alerting

## 📁 **Knowledge Structure**

### **🐳 Containerization** - [containerization/](containerization/README.md)

| Technology                                           | Purpose              | Use Cases                        | Complexity | Adoption     |
| ---------------------------------------------------- | -------------------- | -------------------------------- | ---------- | ------------ |
| [Docker](containerization/docker.md)                 | Container runtime    | Application packaging, local dev | ⭐⭐       | 🔥 Universal |
| [Podman](containerization/podman.md)                 | Rootless containers  | Security-focused environments    | ⭐⭐⭐     | 📈 Growing   |
| [containerd](containerization/containerd.md)         | Container runtime    | Kubernetes backend               | ⭐⭐⭐     | ⚡ High      |
| [Docker Compose](containerization/docker-compose.md) | Multi-container apps | Local development, testing       | ⭐⭐       | 🔥 Universal |

#### **Container Concepts**

| Concept                                                                    | Description                    | Importance  | Difficulty |
| -------------------------------------------------------------------------- | ------------------------------ | ----------- | ---------- |
| [Container Images](containerization/images.md)                             | Immutable application packages | 🔥 Critical | ⭐⭐       |
| [Dockerfile Best Practices](containerization/dockerfile-best-practices.md) | Optimized image building       | 🔥 Critical | ⭐⭐⭐     |
| [Container Networking](containerization/networking.md)                     | Inter-container communication  | ⚡ High     | ⭐⭐⭐     |
| [Volume Management](containerization/volumes.md)                           | Data persistence               | ⚡ High     | ⭐⭐       |
| [Container Security](containerization/security.md)                         | Secure containerization        | 🔥 Critical | ⭐⭐⭐⭐   |

### **☸️ Kubernetes** - [kubernetes/](kubernetes/README.md)

#### **Core Concepts**

| Component                                            | Purpose                  | Importance  | Complexity |
| ---------------------------------------------------- | ------------------------ | ----------- | ---------- |
| [Pods](kubernetes/pods.md)                           | Basic deployment unit    | 🔥 Critical | ⭐⭐       |
| [Services](kubernetes/services.md)                   | Network abstraction      | 🔥 Critical | ⭐⭐⭐     |
| [Deployments](kubernetes/deployments.md)             | Application lifecycle    | 🔥 Critical | ⭐⭐⭐     |
| [ConfigMaps & Secrets](kubernetes/config-secrets.md) | Configuration management | 🔥 Critical | ⭐⭐       |
| [Ingress](kubernetes/ingress.md)                     | External access          | ⚡ High     | ⭐⭐⭐     |
| [Persistent Volumes](kubernetes/storage.md)          | Storage abstraction      | ⚡ High     | ⭐⭐⭐⭐   |

#### **Advanced Kubernetes**

| Technology                                 | Purpose                | Use Cases                   | Complexity |
| ------------------------------------------ | ---------------------- | --------------------------- | ---------- |
| [Helm](kubernetes/helm.md)                 | Package manager        | Application templating      | ⭐⭐⭐     |
| [Istio](kubernetes/istio.md)               | Service mesh           | Microservices communication | ⭐⭐⭐⭐⭐ |
| [Prometheus](kubernetes/prometheus.md)     | Monitoring             | Metrics collection          | ⭐⭐⭐     |
| [ArgoCD](kubernetes/argocd.md)             | GitOps                 | Continuous deployment       | ⭐⭐⭐⭐   |
| [Cert-Manager](kubernetes/cert-manager.md) | Certificate management | TLS automation              | ⭐⭐⭐     |

### **🔄 CI/CD Pipelines** - [ci-cd/](ci-cd/README.md)

#### **CI/CD Platforms**

| Platform                                  | Strengths                    | Use Cases                     | Complexity |
| ----------------------------------------- | ---------------------------- | ----------------------------- | ---------- |
| [GitHub Actions](ci-cd/github-actions.md) | Git integration, marketplace | Open source projects          | ⭐⭐       |
| [GitLab CI](ci-cd/gitlab-ci.md)           | Built-in DevOps platform     | Enterprise GitLab users       | ⭐⭐⭐     |
| [Jenkins](ci-cd/jenkins.md)               | Flexibility, plugins         | Enterprise, complex workflows | ⭐⭐⭐⭐   |
| [Azure DevOps](ci-cd/azure-devops.md)     | Microsoft ecosystem          | .NET applications             | ⭐⭐⭐     |
| [CircleCI](ci-cd/circleci.md)             | Performance, parallelization | High-throughput teams         | ⭐⭐⭐     |

#### **Pipeline Concepts**

| Concept                                                 | Description                  | Importance  | Difficulty |
| ------------------------------------------------------- | ---------------------------- | ----------- | ---------- |
| [Pipeline as Code](ci-cd/pipeline-as-code.md)           | Version-controlled pipelines | 🔥 Critical | ⭐⭐       |
| [Testing Strategies](ci-cd/testing-strategies.md)       | Automated quality gates      | 🔥 Critical | ⭐⭐⭐     |
| [Deployment Strategies](ci-cd/deployment-strategies.md) | Blue-green, canary, rolling  | ⚡ High     | ⭐⭐⭐⭐   |
| [GitOps](ci-cd/gitops.md)                               | Git-driven deployments       | ⚡ High     | ⭐⭐⭐⭐   |
| [Security Scanning](ci-cd/security-scanning.md)         | Vulnerability detection      | 🔥 Critical | ⭐⭐⭐     |

### **🏗️ Infrastructure as Code** - [infrastructure/](infrastructure/README.md)

#### **IaC Tools**

| Tool                                                   | Approach                 | Use Cases                    | Complexity |
| ------------------------------------------------------ | ------------------------ | ---------------------------- | ---------- |
| [Terraform](infrastructure/terraform.md)               | Declarative, multi-cloud | Infrastructure provisioning  | ⭐⭐⭐     |
| [AWS CloudFormation](infrastructure/cloudformation.md) | AWS-native               | AWS-only environments        | ⭐⭐⭐     |
| [Pulumi](infrastructure/pulumi.md)                     | Programming languages    | Complex infrastructure logic | ⭐⭐⭐⭐   |
| [Ansible](infrastructure/ansible.md)                   | Configuration management | Server configuration         | ⭐⭐⭐     |
| [CDK](infrastructure/cdk.md)                           | Code-first approach      | AWS with familiar languages  | ⭐⭐⭐⭐   |

#### **Cloud Platforms**

| Platform                                       | Strengths              | Market Share      | Learning Priority |
| ---------------------------------------------- | ---------------------- | ----------------- | ----------------- |
| [AWS](infrastructure/aws.md)                   | Comprehensive services | 🔥 Dominant       | 🔥 Critical       |
| [Azure](infrastructure/azure.md)               | Enterprise integration | ⚡ Growing        | ⚡ High           |
| [Google Cloud](infrastructure/gcp.md)          | AI/ML, data analytics  | 📈 Niche leader   | 📈 Medium         |
| [DigitalOcean](infrastructure/digitalocean.md) | Simplicity, cost       | 📋 Small projects | 📋 Low            |

### **📊 Monitoring & Observability** - [monitoring/](monitoring/README.md)

#### **The Three Pillars**

| Pillar                           | Tools                         | Purpose                | Complexity |
| -------------------------------- | ----------------------------- | ---------------------- | ---------- |
| [Metrics](monitoring/metrics.md) | Prometheus, Grafana, DataDog  | Performance monitoring | ⭐⭐⭐     |
| [Logging](monitoring/logging.md) | ELK Stack, Fluentd, Loki      | Event tracking         | ⭐⭐⭐     |
| [Tracing](monitoring/tracing.md) | Jaeger, Zipkin, OpenTelemetry | Request flow analysis  | ⭐⭐⭐⭐   |

#### **Monitoring Stack**

| Component                                    | Purpose             | Popular Tools        | Complexity |
| -------------------------------------------- | ------------------- | -------------------- | ---------- |
| [Data Collection](monitoring/collection.md)  | Gather metrics/logs | Prometheus, Fluentd  | ⭐⭐       |
| [Storage](monitoring/storage.md)             | Time-series data    | InfluxDB, Prometheus | ⭐⭐⭐     |
| [Visualization](monitoring/visualization.md) | Dashboards, charts  | Grafana, Kibana      | ⭐⭐       |
| [Alerting](monitoring/alerting.md)           | Incident response   | PagerDuty, Slack     | ⭐⭐⭐     |

## 🎓 **Learning Path**

### **🚀 Beginner Level (0-2 years)**

#### **Phase 1: Containerization Fundamentals (2-3 months)**

1. **Docker Basics**

   - [Container concepts](containerization/concepts.md)
   - [Docker installation and setup](containerization/docker.md#installation)
   - [Basic Docker commands](containerization/docker.md#commands)
   - [Writing Dockerfiles](containerization/dockerfile-best-practices.md)

2. **Container Development**

   - [Multi-stage builds](containerization/docker.md#multi-stage)
   - [Docker Compose for local development](containerization/docker-compose.md)
   - [Container networking basics](containerization/networking.md)
   - [Volume management](containerization/volumes.md)

3. **Hands-on Practice**
   - Containerize a simple web application
   - Set up multi-container development environment
   - Practice with different base images

#### **Phase 2: Basic CI/CD (1-2 months)**

1. **Version Control Integration**

   - [Git workflows](ci-cd/git-workflows.md)
   - [Branch strategies](ci-cd/branching-strategies.md)
   - [Pull request automation](ci-cd/pr-automation.md)

2. **Simple Pipelines**
   - [GitHub Actions basics](ci-cd/github-actions.md#basics)
   - [Automated testing](ci-cd/testing-strategies.md#unit-tests)
   - [Basic deployment](ci-cd/deployment-strategies.md#simple)

#### **Phase 3: Cloud Fundamentals (1-2 months)**

1. **Cloud Basics**

   - [Cloud computing concepts](infrastructure/cloud-concepts.md)
   - [AWS/Azure/GCP overview](infrastructure/cloud-comparison.md)
   - [Basic cloud services](infrastructure/cloud-services.md)

2. **Infrastructure Basics**
   - [Virtual machines vs containers](infrastructure/vm-vs-containers.md)
   - [Basic networking](infrastructure/networking-basics.md)
   - [Storage concepts](infrastructure/storage-concepts.md)

### **⚡ Intermediate Level (2-5 years)**

#### **Phase 1: Kubernetes Mastery (3-4 months)**

1. **Core Kubernetes**

   - [Kubernetes architecture](kubernetes/architecture.md)
   - [Pod, Service, Deployment](kubernetes/core-concepts.md)
   - [ConfigMaps and Secrets](kubernetes/config-secrets.md)
   - [Ingress and networking](kubernetes/networking.md)

2. **Application Deployment**

   - [Helm charts](kubernetes/helm.md)
   - [Rolling updates](kubernetes/deployments.md#rolling-updates)
   - [Health checks](kubernetes/health-checks.md)
   - [Resource management](kubernetes/resources.md)

3. **Kubernetes Operations**
   - [Cluster management](kubernetes/cluster-management.md)
   - [Troubleshooting](kubernetes/troubleshooting.md)
   - [Security best practices](kubernetes/security.md)

#### **Phase 2: Advanced CI/CD (2-3 months)**

1. **Complex Pipelines**

   - [Multi-stage pipelines](ci-cd/multi-stage-pipelines.md)
   - [Parallel execution](ci-cd/parallel-execution.md)
   - [Pipeline optimization](ci-cd/optimization.md)

2. **Deployment Strategies**

   - [Blue-green deployments](ci-cd/deployment-strategies.md#blue-green)
   - [Canary releases](ci-cd/deployment-strategies.md#canary)
   - [Feature flags](ci-cd/feature-flags.md)

3. **GitOps**
   - [ArgoCD setup](kubernetes/argocd.md)
   - [GitOps workflows](ci-cd/gitops.md)
   - [Configuration management](ci-cd/config-management.md)

#### **Phase 3: Infrastructure as Code (2-3 months)**

1. **Terraform Mastery**

   - [Terraform fundamentals](infrastructure/terraform.md#fundamentals)
   - [State management](infrastructure/terraform.md#state)
   - [Modules and reusability](infrastructure/terraform.md#modules)

2. **Cloud Architecture**
   - [Multi-tier applications](infrastructure/multi-tier.md)
   - [Auto-scaling](infrastructure/auto-scaling.md)
   - [Load balancing](infrastructure/load-balancing.md)

### **🏆 Advanced Level (5+ years)**

#### **Phase 1: Production Operations (3-4 months)**

1. **Monitoring & Observability**

   - [Prometheus and Grafana](monitoring/prometheus-grafana.md)
   - [Distributed tracing](monitoring/tracing.md)
   - [Log aggregation](monitoring/logging.md)
   - [SLI/SLO/SLA](monitoring/sli-slo-sla.md)

2. **Incident Response**
   - [On-call practices](monitoring/on-call.md)
   - [Incident management](monitoring/incident-management.md)
   - [Post-mortem culture](monitoring/post-mortems.md)

#### **Phase 2: Advanced Kubernetes (2-3 months)**

1. **Service Mesh**

   - [Istio fundamentals](kubernetes/istio.md)
   - [Traffic management](kubernetes/traffic-management.md)
   - [Security policies](kubernetes/security-policies.md)

2. **Cluster Operations**
   - [Multi-cluster management](kubernetes/multi-cluster.md)
   - [Cluster autoscaling](kubernetes/cluster-autoscaling.md)
   - [Disaster recovery](kubernetes/disaster-recovery.md)

#### **Phase 3: Platform Engineering (3-4 months)**

1. **Developer Experience**

   - [Internal developer platforms](platform/internal-platforms.md)
   - [Self-service infrastructure](platform/self-service.md)
   - [Developer tooling](platform/developer-tools.md)

2. **Advanced Architecture**
   - [Multi-cloud strategies](infrastructure/multi-cloud.md)
   - [Edge computing](infrastructure/edge-computing.md)
   - [Serverless architectures](infrastructure/serverless.md)

## 💡 **Practical Applications & Case Studies**

### **🎯 Real-World Scenarios**

#### **Microservices Deployment with Kubernetes**

```yaml
# Complete microservices deployment example
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  labels:
    app: user-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        version: v1
    spec:
      containers:
        - name: user-service
          image: myregistry/user-service:v1.2.3
          ports:
            - containerPort: 8080
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: url
            - name: REDIS_URL
              valueFrom:
                configMapKeyRef:
                  name: cache-config
                  key: redis-url
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: user-service
spec:
  selector:
    app: user-service
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: user-service-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
    - hosts:
        - api.example.com
      secretName: api-tls
  rules:
    - host: api.example.com
      http:
        paths:
          - path: /users
            pathType: Prefix
            backend:
              service:
                name: user-service
                port:
                  number: 80
```

#### **Infrastructure as Code with Terraform**

```hcl
# Complete AWS infrastructure setup
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  backend "s3" {
    bucket = "my-terraform-state"
    key    = "production/terraform.tfstate"
    region = "us-west-2"
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC and Networking
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "${var.environment}-vpc"
  cidr = "10.0.0.0/16"

  azs             = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  private_subnets = ["********/24", "********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24", "10.0.103.0/24"]

  enable_nat_gateway = true
  enable_vpn_gateway = false

  tags = {
    Environment = var.environment
    Terraform   = "true"
  }
}

# EKS Cluster
module "eks" {
  source = "terraform-aws-modules/eks/aws"

  cluster_name    = "${var.environment}-cluster"
  cluster_version = "1.28"

  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets

  # EKS Managed Node Groups
  eks_managed_node_groups = {
    main = {
      min_size     = 2
      max_size     = 10
      desired_size = 3

      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"

      k8s_labels = {
        Environment = var.environment
        NodeGroup   = "main"
      }
    }
  }

  tags = {
    Environment = var.environment
    Terraform   = "true"
  }
}

# RDS Database
resource "aws_db_instance" "main" {
  identifier = "${var.environment}-database"

  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.micro"

  allocated_storage     = 20
  max_allocated_storage = 100
  storage_encrypted     = true

  db_name  = var.db_name
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = var.environment != "production"

  tags = {
    Environment = var.environment
    Terraform   = "true"
  }
}

# Security Groups
resource "aws_security_group" "rds" {
  name_prefix = "${var.environment}-rds-"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [module.vpc.vpc_cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Environment = var.environment
    Terraform   = "true"
  }
}

resource "aws_db_subnet_group" "main" {
  name       = "${var.environment}-db-subnet-group"
  subnet_ids = module.vpc.private_subnets

  tags = {
    Environment = var.environment
    Terraform   = "true"
  }
}

# Variables
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "db_name" {
  description = "Database name"
  type        = string
  default     = "myapp"
}

variable "db_username" {
  description = "Database username"
  type        = string
  default     = "postgres"
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

# Outputs
output "cluster_endpoint" {
  description = "EKS cluster endpoint"
  value       = module.eks.cluster_endpoint
}

output "cluster_name" {
  description = "EKS cluster name"
  value       = module.eks.cluster_name
}

output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}
```

#### **CI/CD Pipeline with GitHub Actions**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Run linting
        run: npm run lint

      - name: Security audit
        run: npm audit --audit-level high

  build:
    needs: test
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.image.outputs.image }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Output image
        id: image
        run: echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}" >> $GITHUB_OUTPUT

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.28.0"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name production-cluster

      - name: Deploy to Kubernetes
        run: |
          # Update image in deployment
          kubectl set image deployment/user-service \
            user-service=${{ needs.build.outputs.image }} \
            --namespace=production

          # Wait for rollout to complete
          kubectl rollout status deployment/user-service \
            --namespace=production \
            --timeout=300s

      - name: Verify deployment
        run: |
          # Check if pods are ready
          kubectl get pods -l app=user-service \
            --namespace=production

          # Run health check
          kubectl exec -n production \
            deployment/user-service -- \
            curl -f http://localhost:8080/health
```

### **🔧 Performance Optimization Examples**

#### **Kubernetes Resource Optimization**

```yaml
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 50
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60

---
# Vertical Pod Autoscaler
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: user-service-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
      - containerName: user-service
        maxAllowed:
          cpu: 2
          memory: 4Gi
        minAllowed:
          cpu: 100m
          memory: 128Mi
```

#### **Monitoring Setup with Prometheus**

```yaml
# Prometheus configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "alert_rules.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

    scrape_configs:
      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https

      # Node metrics
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics

      # Application metrics
      - job_name: 'user-service'
        kubernetes_sd_configs:
        - role: endpoints
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: user-service
        - source_labels: [__meta_kubernetes_endpoint_port_name]
          action: keep
          regex: metrics

  alert_rules.yml: |
    groups:
    - name: application.rules
      rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90%"

      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.pod }} is restarting frequently"
```

## 📊 **Assessment & Practice**

### **🧪 Self-Assessment Questions**

#### **Containerization**

- [ ] Can you write optimized Dockerfiles with multi-stage builds?
- [ ] Do you understand container networking and volume management?
- [ ] Can you troubleshoot container issues effectively?
- [ ] Do you know container security best practices?

#### **Kubernetes**

- [ ] Can you deploy applications using Deployments, Services, and Ingress?
- [ ] Do you understand Kubernetes networking and storage?
- [ ] Can you configure autoscaling and resource management?
- [ ] Do you know how to troubleshoot Kubernetes issues?

#### **CI/CD**

- [ ] Can you design and implement CI/CD pipelines?
- [ ] Do you understand different deployment strategies?
- [ ] Can you implement automated testing and security scanning?
- [ ] Do you know GitOps principles and practices?

#### **Infrastructure as Code**

- [ ] Can you write Terraform modules and manage state?
- [ ] Do you understand cloud architecture patterns?
- [ ] Can you implement infrastructure security and compliance?
- [ ] Do you know multi-cloud and disaster recovery strategies?

### **🏋️ Practice Projects**

#### **Beginner Projects**

1. **Containerized Web Application**

   - Dockerize a simple web app
   - Set up Docker Compose for local development
   - Implement health checks and logging

2. **Basic CI/CD Pipeline**
   - Create GitHub Actions workflow
   - Implement automated testing
   - Deploy to staging environment

#### **Intermediate Projects**

1. **Kubernetes Microservices**

   - Deploy multi-service application
   - Implement service discovery and load balancing
   - Set up monitoring and logging

2. **Infrastructure as Code**
   - Provision cloud infrastructure with Terraform
   - Implement auto-scaling and load balancing
   - Set up monitoring and alerting

#### **Advanced Projects**

1. **Production-Ready Platform**

   - Multi-environment Kubernetes clusters
   - GitOps deployment workflows
   - Comprehensive monitoring and observability

2. **Multi-Cloud Architecture**
   - Deploy across multiple cloud providers
   - Implement disaster recovery
   - Advanced security and compliance

## 🔗 **Cross-References**

### **Related Knowledge Areas**

- **[Programming Fundamentals](../01-programming-fundamentals/README.md)** - Scripting and automation
- **[Software Design](../02-software-design/README.md)** - Microservices patterns
- **[System Architecture](../03-system-architecture/README.md)** - Distributed systems design
- **[Database Engineering](../04-database-engineering/README.md)** - Database deployment and scaling
- **[Security](../06-security/README.md)** - Infrastructure and application security

### **Learning Dependencies**

```mermaid
graph TD
    A[Containerization] --> B[Kubernetes]
    B --> C[Advanced Orchestration]

    A --> D[CI/CD Basics]
    D --> E[Advanced Pipelines]
    E --> F[GitOps]

    A --> G[Infrastructure as Code]
    G --> H[Cloud Architecture]
    H --> I[Multi-Cloud]

    B --> J[Monitoring]
    J --> K[Observability]
    K --> L[SRE Practices]
```

---

**☁️ Master DevOps and Cloud Engineering to bridge the gap between development and operations, enabling your teams to deliver software faster, more reliably, and at scale. The cloud is the future - make sure you're ready for it!**

#### **Phase 1: Production Operations (3-4 months)**

1. **Monitoring & Observability**

   - [Prometheus and Grafana](monitoring/prometheus-grafana.md)
   - [Distributed tracing](monitoring/tracing.md)
   - [Log aggregation](monitoring/logging.md)
   - [SLI/SLO/SLA](monitoring/sli-slo-sla.md)

2. **Incident Response**
   - [On-call practices](monitoring/on-call.md)
   - [Incident management](monitoring/incident-management.md)
   - [Post-mortem culture](monitoring/post-mortems.md)

#### **Phase 2: Advanced Kubernetes (2-3 months)**

1. **Service Mesh**

   - [Istio fundamentals](kubernetes/istio.md)
   - [Traffic management](kubernetes/traffic-management.md)
   - [Security policies](kubernetes/security-policies.md)

2. **Cluster Operations**
   - [Multi-cluster management](kubernetes/multi-cluster.md)
   - [Cluster autoscaling](kubernetes/cluster-autoscaling.md)
   - [Disaster recovery](kubernetes/disaster-recovery.md)

#### **Phase 3: Platform Engineering (3-4 months)**

1. **Developer Experience**

   - [Internal developer platforms](platform/internal-platforms.md)
   - [Self-service infrastructure](platform/self-service.md)
   - [Developer tooling](platform/developer-tools.md)

2. **Advanced Architecture**
   - [Multi-cloud strategies](infrastructure/multi-cloud.md)
   - [Edge computing](infrastructure/edge-computing.md)
   - [Serverless architectures](infrastructure/serverless.md)
