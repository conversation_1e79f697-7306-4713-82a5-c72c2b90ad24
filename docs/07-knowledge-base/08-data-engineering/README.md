# 📊 **DATA ENGINEERING**

> **Master the art of building robust data pipelines and infrastructure - From ETL to real-time streaming and data lakes**

## 🎯 **Overview**

Data Engineering is the backbone of modern data-driven organizations. This section provides comprehensive knowledge about data pipelines, data warehousing, streaming systems, and big data technologies that enable organizations to collect, process, and analyze data at scale.

### **📊 What You'll Learn**

- **🔄 Data Pipelines** - ETL/ELT processes, workflow orchestration, data quality
- **🏗️ Data Architecture** - Data lakes, warehouses, lakehouses, modern data stack
- **⚡ Stream Processing** - Real-time data processing, event-driven architectures
- **📈 Big Data Technologies** - Hadoop, Spark, distributed computing
- **☁️ Cloud Data Platforms** - AWS, Azure, GCP data services

## 📁 **Knowledge Structure**

### **🔄 Data Pipelines** - [data-pipelines/](data-pipelines/README.md)

#### **Pipeline Fundamentals**

| Concept                                                   | Description                       | Importance  | Difficulty |
| --------------------------------------------------------- | --------------------------------- | ----------- | ---------- |
| [ETL vs ELT](data-pipelines/etl-vs-elt.md)                | Extract, Transform, Load patterns | 🔥 Critical | ⭐⭐       |
| [Data Quality](data-pipelines/data-quality.md)            | Validation, cleansing, monitoring | 🔥 Critical | ⭐⭐⭐     |
| [Pipeline Orchestration](data-pipelines/orchestration.md) | Workflow management               | 🔥 Critical | ⭐⭐⭐     |
| [Data Lineage](data-pipelines/data-lineage.md)            | Tracking data flow                | ⚡ High     | ⭐⭐⭐     |
| [Error Handling](data-pipelines/error-handling.md)        | Fault tolerance, recovery         | ⚡ High     | ⭐⭐⭐     |

#### **Orchestration Tools**

| Tool                                        | Strengths               | Use Cases              | Complexity |
| ------------------------------------------- | ----------------------- | ---------------------- | ---------- |
| [Apache Airflow](data-pipelines/airflow.md) | Python-based, flexible  | Complex workflows      | ⭐⭐⭐     |
| [Prefect](data-pipelines/prefect.md)        | Modern Python framework | Data science workflows | ⭐⭐       |
| [Dagster](data-pipelines/dagster.md)        | Asset-centric approach  | Data-aware pipelines   | ⭐⭐⭐     |
| [Apache NiFi](data-pipelines/nifi.md)       | Visual interface        | Real-time data flows   | ⭐⭐⭐     |
| [Luigi](data-pipelines/luigi.md)            | Spotify's framework     | Batch processing       | ⭐⭐       |

### **🏗️ Data Architecture** - [data-architecture/](data-architecture/README.md)

#### **Storage Architectures**

| Architecture                                          | Purpose                | Use Cases        | Complexity |
| ----------------------------------------------------- | ---------------------- | ---------------- | ---------- |
| [Data Warehouse](data-architecture/data-warehouse.md) | Structured analytics   | BI, reporting    | ⭐⭐⭐     |
| [Data Lake](data-architecture/data-lake.md)           | Raw data storage       | Big data, ML     | ⭐⭐⭐     |
| [Data Lakehouse](data-architecture/lakehouse.md)      | Unified architecture   | Modern analytics | ⭐⭐⭐⭐   |
| [Data Mesh](data-architecture/data-mesh.md)           | Decentralized approach | Enterprise scale | ⭐⭐⭐⭐⭐ |

#### **Modern Data Stack**

| Layer                                                 | Technologies        | Purpose                       | Examples |
| ----------------------------------------------------- | ------------------- | ----------------------------- | -------- |
| [Ingestion](data-architecture/ingestion.md)           | Data collection     | Fivetran, Airbyte, Stitch     | ⭐⭐     |
| [Storage](data-architecture/storage.md)               | Data persistence    | Snowflake, BigQuery, Redshift | ⭐⭐⭐   |
| [Transformation](data-architecture/transformation.md) | Data modeling       | dbt, Dataform                 | ⭐⭐⭐   |
| [Orchestration](data-architecture/orchestration.md)   | Workflow management | Airflow, Prefect              | ⭐⭐⭐   |
| [Visualization](data-architecture/visualization.md)   | Analytics & BI      | Tableau, Looker, Power BI     | ⭐⭐     |

### **⚡ Stream Processing** - [stream-processing/](stream-processing/README.md)

#### **Streaming Platforms**

| Platform                                            | Strengths                      | Use Cases            | Complexity |
| --------------------------------------------------- | ------------------------------ | -------------------- | ---------- |
| [Apache Kafka](stream-processing/kafka.md)          | High throughput, durability    | Event streaming      | ⭐⭐⭐⭐   |
| [Apache Pulsar](stream-processing/pulsar.md)        | Multi-tenancy, geo-replication | Enterprise messaging | ⭐⭐⭐⭐   |
| [Amazon Kinesis](stream-processing/kinesis.md)      | Managed service                | AWS ecosystem        | ⭐⭐⭐     |
| [Google Pub/Sub](stream-processing/pubsub.md)       | Serverless messaging           | GCP ecosystem        | ⭐⭐       |
| [Azure Event Hubs](stream-processing/event-hubs.md) | Big data streaming             | Azure ecosystem      | ⭐⭐⭐     |

#### **Stream Processing Frameworks**

| Framework                                                      | Language     | Strengths         | Complexity |
| -------------------------------------------------------------- | ------------ | ----------------- | ---------- |
| [Apache Spark Streaming](stream-processing/spark-streaming.md) | Scala/Python | Micro-batching    | ⭐⭐⭐     |
| [Apache Flink](stream-processing/flink.md)                     | Java/Scala   | True streaming    | ⭐⭐⭐⭐   |
| [Apache Storm](stream-processing/storm.md)                     | Java/Python  | Low latency       | ⭐⭐⭐     |
| [Kafka Streams](stream-processing/kafka-streams.md)            | Java         | Kafka integration | ⭐⭐⭐     |
| [Apache Beam](stream-processing/beam.md)                       | Java/Python  | Unified model     | ⭐⭐⭐⭐   |

### **📈 Big Data Technologies** - [big-data/](big-data/README.md)

#### **Distributed Computing**

| Technology                          | Purpose                     | Use Cases             | Adoption   |
| ----------------------------------- | --------------------------- | --------------------- | ---------- |
| [Apache Hadoop](big-data/hadoop.md) | Distributed storage/compute | Batch processing      | 📈 Legacy  |
| [Apache Spark](big-data/spark.md)   | In-memory processing        | Analytics, ML         | 🔥 High    |
| [Apache Hive](big-data/hive.md)     | SQL on Hadoop               | Data warehousing      | 📈 Medium  |
| [Apache HBase](big-data/hbase.md)   | NoSQL database              | Real-time access      | 📈 Medium  |
| [Presto/Trino](big-data/presto.md)  | Distributed SQL             | Interactive analytics | ⚡ Growing |

#### **Data Formats & Serialization**

| Format                                | Type              | Use Cases         | Performance  |
| ------------------------------------- | ----------------- | ----------------- | ------------ |
| [Apache Parquet](big-data/parquet.md) | Columnar          | Analytics         | 🔥 Excellent |
| [Apache Avro](big-data/avro.md)       | Row-based         | Schema evolution  | ⚡ Good      |
| [Apache ORC](big-data/orc.md)         | Columnar          | Hive optimization | ⚡ Good      |
| [Delta Lake](big-data/delta-lake.md)  | ACID transactions | Data lakes        | 🔥 Excellent |
| [Apache Iceberg](big-data/iceberg.md) | Table format      | Large datasets    | ⚡ Growing   |

### **☁️ Cloud Data Platforms** - [cloud-platforms/](cloud-platforms/README.md)

#### **AWS Data Services**

| Service                                                 | Purpose              | Use Cases            | Complexity |
| ------------------------------------------------------- | -------------------- | -------------------- | ---------- |
| [Amazon S3](cloud-platforms/aws-s3.md)                  | Object storage       | Data lake foundation | ⭐⭐       |
| [Amazon Redshift](cloud-platforms/redshift.md)          | Data warehouse       | Analytics            | ⭐⭐⭐     |
| [AWS Glue](cloud-platforms/glue.md)                     | ETL service          | Data integration     | ⭐⭐⭐     |
| [Amazon EMR](cloud-platforms/emr.md)                    | Managed Hadoop/Spark | Big data processing  | ⭐⭐⭐⭐   |
| [AWS Lake Formation](cloud-platforms/lake-formation.md) | Data lake setup      | Governance           | ⭐⭐⭐⭐   |

#### **Azure Data Services**

| Service                                               | Purpose            | Use Cases          | Complexity |
| ----------------------------------------------------- | ------------------ | ------------------ | ---------- |
| [Azure Data Lake](cloud-platforms/azure-data-lake.md) | Data lake storage  | Big data analytics | ⭐⭐⭐     |
| [Azure Synapse](cloud-platforms/synapse.md)           | Analytics platform | Unified analytics  | ⭐⭐⭐⭐   |
| [Azure Data Factory](cloud-platforms/data-factory.md) | Data integration   | ETL/ELT            | ⭐⭐⭐     |
| [Azure Databricks](cloud-platforms/databricks.md)     | Spark platform     | ML & analytics     | ⭐⭐⭐⭐   |

#### **GCP Data Services**

| Service                                       | Purpose                 | Use Cases              | Complexity |
| --------------------------------------------- | ----------------------- | ---------------------- | ---------- |
| [BigQuery](cloud-platforms/bigquery.md)       | Serverless warehouse    | Analytics              | ⭐⭐       |
| [Cloud Dataflow](cloud-platforms/dataflow.md) | Stream/batch processing | Apache Beam            | ⭐⭐⭐     |
| [Cloud Dataproc](cloud-platforms/dataproc.md) | Managed Hadoop/Spark    | Big data               | ⭐⭐⭐     |
| [Cloud Composer](cloud-platforms/composer.md) | Managed Airflow         | Workflow orchestration | ⭐⭐⭐     |

## 🎓 **Learning Path**

### **🚀 Beginner Level (0-2 years)**

#### **Phase 1: Data Fundamentals (2-3 months)**

1. **Data Concepts**

   - [Data Types and Structures](fundamentals/data-types.md)
   - [Database Fundamentals](fundamentals/databases.md)
   - [SQL Mastery](fundamentals/sql.md)
   - [Data Modeling Basics](fundamentals/data-modeling.md)

2. **Programming for Data**

   - [Python for Data Engineering](programming/python-data-eng.md)
   - [Working with APIs](programming/apis.md)
   - [File Formats and Parsing](programming/file-formats.md)
   - [Version Control for Data](programming/git-data.md)

3. **Basic ETL**
   - [ETL Concepts](etl/concepts.md)
   - [Data Extraction](etl/extraction.md)
   - [Data Transformation](etl/transformation.md)
   - [Data Loading](etl/loading.md)

#### **Phase 2: Pipeline Development (3-4 months)**

1. **Workflow Orchestration**

   - [Apache Airflow Basics](tools/airflow-basics.md)
   - [DAG Design Patterns](tools/dag-patterns.md)
   - [Scheduling and Dependencies](tools/scheduling.md)
   - [Monitoring and Alerting](tools/monitoring.md)

2. **Data Quality**

   - [Data Validation](quality/validation.md)
   - [Data Profiling](quality/profiling.md)
   - [Data Cleansing](quality/cleansing.md)
   - [Quality Metrics](quality/metrics.md)

3. **Cloud Basics**
   - [Cloud Storage](cloud/storage.md)
   - [Managed Databases](cloud/databases.md)
   - [Serverless Computing](cloud/serverless.md)
   - [Infrastructure as Code](cloud/iac.md)

#### **Phase 3: First Data Projects (2-3 months)**

1. **Practical Projects**
   - [Web Scraping Pipeline](projects/web-scraping.md)
   - [API Data Integration](projects/api-integration.md)
   - [CSV to Database ETL](projects/csv-etl.md)
   - [Data Quality Dashboard](projects/quality-dashboard.md)

### **⚡ Intermediate Level (2-5 years)**

#### **Phase 1: Advanced Pipelines (3-4 months)**

1. **Complex ETL/ELT**

   - [Advanced Transformations](advanced/transformations.md)
   - [Incremental Processing](advanced/incremental.md)
   - [Change Data Capture](advanced/cdc.md)
   - [Data Lineage Tracking](advanced/lineage.md)

2. **Stream Processing**

   - [Apache Kafka Fundamentals](streaming/kafka-fundamentals.md)
   - [Stream Processing Patterns](streaming/patterns.md)
   - [Real-time Analytics](streaming/real-time-analytics.md)
   - [Event-Driven Architecture](streaming/event-driven.md)

3. **Big Data Processing**
   - [Apache Spark Fundamentals](spark/fundamentals.md)
   - [Spark SQL and DataFrames](spark/sql-dataframes.md)
   - [Spark Streaming](spark/streaming.md)
   - [Performance Optimization](spark/optimization.md)

#### **Phase 2: Data Architecture (3-4 months)**

1. **Modern Data Stack**

   - [Data Warehouse Design](architecture/warehouse-design.md)
   - [Data Lake Architecture](architecture/lake-architecture.md)
   - [Lakehouse Implementation](architecture/lakehouse.md)
   - [Data Mesh Principles](architecture/data-mesh.md)

2. **Cloud Data Platforms**
   - [Multi-cloud Strategies](cloud/multi-cloud.md)
   - [Cost Optimization](cloud/cost-optimization.md)
   - [Security and Compliance](cloud/security.md)
   - [Disaster Recovery](cloud/disaster-recovery.md)

#### **Phase 3: Production Systems (2-3 months)**

1. **DataOps**

   - [CI/CD for Data](dataops/ci-cd.md)
   - [Testing Data Pipelines](dataops/testing.md)
   - [Environment Management](dataops/environments.md)
   - [Deployment Strategies](dataops/deployment.md)

2. **Monitoring & Observability**
   - [Pipeline Monitoring](monitoring/pipeline-monitoring.md)
   - [Data Observability](monitoring/data-observability.md)
   - [Alerting Strategies](monitoring/alerting.md)
   - [Performance Tuning](monitoring/performance.md)

### **🏆 Advanced Level (5+ years)**

#### **Phase 1: Enterprise Architecture (3-4 months)**

1. **Data Governance**

   - [Data Governance Frameworks](governance/frameworks.md)
   - [Data Cataloging](governance/cataloging.md)
   - [Privacy and Compliance](governance/privacy.md)
   - [Master Data Management](governance/mdm.md)

2. **Advanced Architectures**
   - [Real-time Data Platforms](architecture/real-time-platforms.md)
   - [Multi-tenant Data Systems](architecture/multi-tenant.md)
   - [Global Data Distribution](architecture/global-distribution.md)
   - [Edge Computing for Data](architecture/edge-computing.md)

#### **Phase 2: Leadership & Strategy (2-3 months)**

1. **Data Strategy**

   - [Data Strategy Development](leadership/data-strategy.md)
   - [Technology Roadmaps](leadership/roadmaps.md)
   - [Team Building](leadership/team-building.md)
   - [Stakeholder Management](leadership/stakeholders.md)

2. **Innovation & Research**
   - [Emerging Technologies](research/emerging-tech.md)
   - [ML Engineering Integration](research/ml-integration.md)
   - [Data Product Development](research/data-products.md)
   - [Open Source Contributions](research/open-source.md)

## 💡 **Practical Applications & Case Studies**

### **🎯 Real-World Data Engineering Projects**

#### **Complete ETL Pipeline with Apache Airflow**

```python
# Production-ready ETL pipeline with error handling and monitoring
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import pandas as pd
import logging
import boto3
from typing import Dict, Any

# DAG configuration
default_args = {
    'owner': 'data-engineering-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'max_active_runs': 1,
}

dag = DAG(
    'ecommerce_etl_pipeline',
    default_args=default_args,
    description='Complete ETL pipeline for e-commerce data',
    schedule_interval='@daily',
    catchup=False,
    tags=['etl', 'ecommerce', 'production']
)

class DataQualityError(Exception):
    """Custom exception for data quality issues"""
    pass

def extract_sales_data(**context) -> str:
    """Extract sales data from multiple sources"""
    execution_date = context['execution_date']
    date_str = execution_date.strftime('%Y-%m-%d')

    logging.info(f"Extracting sales data for {date_str}")

    # Extract from PostgreSQL
    postgres_hook = PostgresHook(postgres_conn_id='ecommerce_db')

    sales_query = """
    SELECT
        order_id,
        customer_id,
        product_id,
        quantity,
        unit_price,
        total_amount,
        order_date,
        status
    FROM orders
    WHERE DATE(order_date) = %s
    AND status IN ('completed', 'shipped')
    """

    sales_df = postgres_hook.get_pandas_df(sales_query, parameters=[date_str])

    # Extract customer data
    customer_query = """
    SELECT
        customer_id,
        first_name,
        last_name,
        email,
        registration_date,
        customer_segment,
        country,
        city
    FROM customers
    WHERE customer_id IN %s
    """

    if not sales_df.empty:
        customer_ids = tuple(sales_df['customer_id'].unique())
        customer_df = postgres_hook.get_pandas_df(
            customer_query,
            parameters=[customer_ids]
        )
    else:
        customer_df = pd.DataFrame()

    # Extract product data
    product_query = """
    SELECT
        product_id,
        product_name,
        category,
        brand,
        cost_price,
        list_price,
        supplier_id
    FROM products
    WHERE product_id IN %s
    """

    if not sales_df.empty:
        product_ids = tuple(sales_df['product_id'].unique())
        product_df = postgres_hook.get_pandas_df(
            product_query,
            parameters=[product_ids]
        )
    else:
        product_df = pd.DataFrame()

    # Save to S3 for intermediate storage
    s3_hook = S3Hook(aws_conn_id='aws_default')
    bucket_name = 'data-pipeline-staging'

    # Upload dataframes to S3
    for df_name, df in [('sales', sales_df), ('customers', customer_df), ('products', product_df)]:
        if not df.empty:
            csv_buffer = df.to_csv(index=False)
            s3_key = f"raw_data/{date_str}/{df_name}.csv"
            s3_hook.load_string(
                csv_buffer,
                key=s3_key,
                bucket_name=bucket_name,
                replace=True
            )
            logging.info(f"Uploaded {df_name} data to s3://{bucket_name}/{s3_key}")

    return f"s3://{bucket_name}/raw_data/{date_str}/"

def validate_data_quality(**context) -> bool:
    """Comprehensive data quality validation"""
    execution_date = context['execution_date']
    date_str = execution_date.strftime('%Y-%m-%d')

    logging.info(f"Validating data quality for {date_str}")

    s3_hook = S3Hook(aws_conn_id='aws_default')
    bucket_name = 'data-pipeline-staging'

    # Load data from S3
    sales_key = f"raw_data/{date_str}/sales.csv"
    customers_key = f"raw_data/{date_str}/customers.csv"
    products_key = f"raw_data/{date_str}/products.csv"

    try:
        sales_df = pd.read_csv(f"s3://{bucket_name}/{sales_key}")
        customers_df = pd.read_csv(f"s3://{bucket_name}/{customers_key}")
        products_df = pd.read_csv(f"s3://{bucket_name}/{products_key}")
    except Exception as e:
        raise DataQualityError(f"Failed to load data from S3: {str(e)}")

    quality_checks = []

    # Check 1: No null values in critical columns
    critical_sales_cols = ['order_id', 'customer_id', 'product_id', 'total_amount']
    for col in critical_sales_cols:
        null_count = sales_df[col].isnull().sum()
        if null_count > 0:
            quality_checks.append(f"Found {null_count} null values in {col}")

    # Check 2: Data types validation
    if not pd.api.types.is_numeric_dtype(sales_df['total_amount']):
        quality_checks.append("total_amount is not numeric")

    # Check 3: Business logic validation
    negative_amounts = sales_df[sales_df['total_amount'] < 0]
    if not negative_amounts.empty:
        quality_checks.append(f"Found {len(negative_amounts)} orders with negative amounts")

    # Check 4: Referential integrity
    missing_customers = set(sales_df['customer_id']) - set(customers_df['customer_id'])
    if missing_customers:
        quality_checks.append(f"Found {len(missing_customers)} orders with missing customer data")

    missing_products = set(sales_df['product_id']) - set(products_df['product_id'])
    if missing_products:
        quality_checks.append(f"Found {len(missing_products)} orders with missing product data")

    # Check 5: Volume validation (should have reasonable amount of data)
    if len(sales_df) == 0:
        quality_checks.append("No sales data found for the date")
    elif len(sales_df) < 10:  # Adjust threshold based on business
        quality_checks.append(f"Unusually low sales volume: {len(sales_df)} orders")

    if quality_checks:
        error_message = "Data quality issues found:\n" + "\n".join(quality_checks)
        raise DataQualityError(error_message)

    logging.info("All data quality checks passed")
    return True

def transform_data(**context) -> str:
    """Transform and enrich the data"""
    execution_date = context['execution_date']
    date_str = execution_date.strftime('%Y-%m-%d')

    logging.info(f"Transforming data for {date_str}")

    s3_hook = S3Hook(aws_conn_id='aws_default')
    bucket_name = 'data-pipeline-staging'

    # Load validated data
    sales_df = pd.read_csv(f"s3://{bucket_name}/raw_data/{date_str}/sales.csv")
    customers_df = pd.read_csv(f"s3://{bucket_name}/raw_data/{date_str}/customers.csv")
    products_df = pd.read_csv(f"s3://{bucket_name}/raw_data/{date_str}/products.csv")

    # Transform sales data
    sales_df['order_date'] = pd.to_datetime(sales_df['order_date'])
    sales_df['profit'] = sales_df['total_amount'] - (sales_df['quantity'] * products_df.set_index('product_id').loc[sales_df['product_id'], 'cost_price'].values)
    sales_df['profit_margin'] = sales_df['profit'] / sales_df['total_amount']

    # Join all data
    enriched_df = sales_df.merge(customers_df, on='customer_id', how='left')
    enriched_df = enriched_df.merge(products_df, on='product_id', how='left')

    # Add derived columns
    enriched_df['customer_full_name'] = enriched_df['first_name'] + ' ' + enriched_df['last_name']
    enriched_df['order_year'] = enriched_df['order_date'].dt.year
    enriched_df['order_month'] = enriched_df['order_date'].dt.month
    enriched_df['order_quarter'] = enriched_df['order_date'].dt.quarter

    # Create aggregated metrics
    daily_metrics = enriched_df.groupby('order_date').agg({
        'order_id': 'count',
        'total_amount': ['sum', 'mean'],
        'profit': 'sum',
        'customer_id': 'nunique'
    }).round(2)

    daily_metrics.columns = ['total_orders', 'total_revenue', 'avg_order_value', 'total_profit', 'unique_customers']
    daily_metrics = daily_metrics.reset_index()

    # Save transformed data
    transformed_key = f"transformed_data/{date_str}/enriched_sales.csv"
    metrics_key = f"transformed_data/{date_str}/daily_metrics.csv"

    s3_hook.load_string(
        enriched_df.to_csv(index=False),
        key=transformed_key,
        bucket_name=bucket_name,
        replace=True
    )

    s3_hook.load_string(
        daily_metrics.to_csv(index=False),
        key=metrics_key,
        bucket_name=bucket_name,
        replace=True
    )

    logging.info(f"Transformed data saved to S3")
    return f"s3://{bucket_name}/transformed_data/{date_str}/"

def load_to_warehouse(**context) -> bool:
    """Load transformed data to data warehouse"""
    execution_date = context['execution_date']
    date_str = execution_date.strftime('%Y-%m-%d')

    logging.info(f"Loading data to warehouse for {date_str}")

    # Load from S3
    s3_hook = S3Hook(aws_conn_id='aws_default')
    bucket_name = 'data-pipeline-staging'

    enriched_df = pd.read_csv(f"s3://{bucket_name}/transformed_data/{date_str}/enriched_sales.csv")
    metrics_df = pd.read_csv(f"s3://{bucket_name}/transformed_data/{date_str}/daily_metrics.csv")

    # Load to warehouse
    warehouse_hook = PostgresHook(postgres_conn_id='data_warehouse')

    # Insert enriched sales data
    enriched_df.to_sql(
        'fact_sales',
        warehouse_hook.get_sqlalchemy_engine(),
        if_exists='append',
        index=False,
        method='multi'
    )

    # Insert daily metrics
    metrics_df.to_sql(
        'daily_sales_metrics',
        warehouse_hook.get_sqlalchemy_engine(),
        if_exists='append',
        index=False,
        method='multi'
    )

    logging.info(f"Successfully loaded {len(enriched_df)} sales records and {len(metrics_df)} metric records")
    return True

def send_success_notification(**context):
    """Send success notification"""
    execution_date = context['execution_date']
    date_str = execution_date.strftime('%Y-%m-%d')

    # In production, you would send to Slack, email, etc.
    logging.info(f"ETL pipeline completed successfully for {date_str}")

# Define tasks
extract_task = PythonOperator(
    task_id='extract_sales_data',
    python_callable=extract_sales_data,
    dag=dag
)

validate_task = PythonOperator(
    task_id='validate_data_quality',
    python_callable=validate_data_quality,
    dag=dag
)

transform_task = PythonOperator(
    task_id='transform_data',
    python_callable=transform_data,
    dag=dag
)

# Create warehouse tables if they don't exist
create_tables_task = PostgresOperator(
    task_id='create_warehouse_tables',
    postgres_conn_id='data_warehouse',
    sql="""
    CREATE TABLE IF NOT EXISTS fact_sales (
        order_id VARCHAR(50),
        customer_id VARCHAR(50),
        product_id VARCHAR(50),
        quantity INTEGER,
        unit_price DECIMAL(10,2),
        total_amount DECIMAL(10,2),
        profit DECIMAL(10,2),
        profit_margin DECIMAL(5,4),
        order_date TIMESTAMP,
        customer_full_name VARCHAR(200),
        customer_segment VARCHAR(50),
        country VARCHAR(100),
        city VARCHAR(100),
        product_name VARCHAR(200),
        category VARCHAR(100),
        brand VARCHAR(100),
        order_year INTEGER,
        order_month INTEGER,
        order_quarter INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS daily_sales_metrics (
        order_date DATE,
        total_orders INTEGER,
        total_revenue DECIMAL(12,2),
        avg_order_value DECIMAL(10,2),
        total_profit DECIMAL(12,2),
        unique_customers INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """,
    dag=dag
)

load_task = PythonOperator(
    task_id='load_to_warehouse',
    python_callable=load_to_warehouse,
    dag=dag
)

notify_task = PythonOperator(
    task_id='send_success_notification',
    python_callable=send_success_notification,
    dag=dag
)

# Data quality monitoring task
quality_monitoring_task = BashOperator(
    task_id='run_data_quality_monitoring',
    bash_command="""
    python /opt/airflow/scripts/data_quality_monitoring.py \
        --date {{ ds }} \
        --table fact_sales \
        --warehouse-conn data_warehouse
    """,
    dag=dag
)

# Define task dependencies
extract_task >> validate_task >> transform_task >> create_tables_task >> load_task >> [notify_task, quality_monitoring_task]
```

## 📊 **Assessment & Practice**

### **🧪 Data Engineering Assessment Questions**

#### **Data Pipeline Fundamentals**

- [ ] Can you design ETL vs ELT pipelines appropriately?
- [ ] Do you understand data quality validation strategies?
- [ ] Can you implement error handling and recovery mechanisms?
- [ ] Do you know how to optimize pipeline performance?

#### **Big Data & Streaming**

- [ ] Can you work with Apache Spark for large-scale processing?
- [ ] Do you understand stream processing concepts and tools?
- [ ] Can you design real-time data architectures?
- [ ] Do you know how to handle late-arriving data?

#### **Cloud & Modern Stack**

- [ ] Can you architect data solutions on cloud platforms?
- [ ] Do you understand modern data stack components?
- [ ] Can you implement data governance and security?
- [ ] Do you know cost optimization strategies?

#### **Production Operations**

- [ ] Can you implement DataOps practices?
- [ ] Do you understand data observability and monitoring?
- [ ] Can you design disaster recovery strategies?
- [ ] Do you know how to scale data systems?

### **🏋️ Data Engineering Practice Projects**

#### **Beginner Projects**

1. **CSV to Database ETL**

   - Extract data from CSV files
   - Transform and validate data
   - Load to PostgreSQL database

2. **API Data Pipeline**
   - Extract data from REST APIs
   - Handle rate limiting and pagination
   - Store in data warehouse

#### **Intermediate Projects**

1. **Real-time Analytics Pipeline**

   - Stream processing with Kafka
   - Real-time transformations
   - Dashboard with live updates

2. **Data Lake Implementation**
   - Multi-source data ingestion
   - Data cataloging and governance
   - Analytics and ML integration

#### **Advanced Projects**

1. **Enterprise Data Platform**

   - Multi-tenant data architecture
   - Advanced governance and security
   - Self-service analytics

2. **Global Data Distribution**
   - Multi-region data replication
   - Edge computing integration
   - Compliance across jurisdictions

## 🔗 **Cross-References**

### **Related Knowledge Areas**

- **[Programming Fundamentals](../01-programming-fundamentals/README.md)** - Python, SQL, data structures
- **[Database Engineering](../04-database-engineering/README.md)** - Database design and optimization
- **[DevOps & Cloud](../05-devops-cloud/README.md)** - Infrastructure and deployment
- **[AI & Machine Learning](../07-ai-machine-learning/README.md)** - ML data pipelines
- **[Security](../06-security/README.md)** - Data security and privacy

### **Learning Dependencies**

```mermaid
graph TD
    A[SQL & Database Fundamentals] --> B[ETL/ELT Basics]
    B --> C[Pipeline Orchestration]

    C --> D[Stream Processing]
    C --> E[Big Data Technologies]

    D --> F[Real-time Architectures]
    E --> F

    C --> G[Cloud Data Platforms]
    G --> H[Modern Data Stack]

    F --> I[Enterprise Data Architecture]
    H --> I

    I --> J[Data Leadership & Strategy]
```

---

**📊 Master Data Engineering to build the robust data infrastructure that powers modern analytics and AI. From simple ETL pipelines to complex real-time systems, data engineers are the architects of the data-driven future!**

1. **DataOps**

   - [CI/CD for Data](dataops/ci-cd.md)
   - [Testing Data Pipelines](dataops/testing.md)
   - [Environment Management](dataops/environments.md)
   - [Deployment Strategies](dataops/deployment.md)

2. **Monitoring & Observability**
   - [Pipeline Monitoring](monitoring/pipeline-monitoring.md)
   - [Data Observability](monitoring/data-observability.md)
   - [Alerting Strategies](monitoring/alerting.md)
   - [Performance Tuning](monitoring/performance.md)

### **🏆 Advanced Level (5+ years)**

#### **Phase 1: Enterprise Architecture (3-4 months)**

1. **Data Governance**

   - [Data Governance Frameworks](governance/frameworks.md)
   - [Data Cataloging](governance/cataloging.md)
   - [Privacy and Compliance](governance/privacy.md)
   - [Master Data Management](governance/mdm.md)

2. **Advanced Architectures**
   - [Real-time Data Platforms](architecture/real-time-platforms.md)
   - [Multi-tenant Data Systems](architecture/multi-tenant.md)
   - [Global Data Distribution](architecture/global-distribution.md)
   - [Edge Computing for Data](architecture/edge-computing.md)

#### **Phase 2: Leadership & Strategy (2-3 months)**

1. **Data Strategy**

   - [Data Strategy Development](leadership/data-strategy.md)
   - [Technology Roadmaps](leadership/roadmaps.md)
   - [Team Building](leadership/team-building.md)
   - [Stakeholder Management](leadership/stakeholders.md)

2. **Innovation & Research**
   - [Emerging Technologies](research/emerging-tech.md)
   - [ML Engineering Integration](research/ml-integration.md)
   - [Data Product Development](research/data-products.md)
   - [Open Source Contributions](research/open-source.md)
