# ⚡ **QUICK REFERENCE & CHEAT SHEETS**

> **Instant access to essential IT knowledge - Flash cards, cheat sheets, and quick lookups for busy developers**

## 🎯 **Quick Navigation**

| 📚 **Category**                                  | 🔍 **Quick Access**                                                                                        |
| ------------------------------------------------ | ---------------------------------------------------------------------------------------------------------- |
| [💻 Programming](#programming-quick-reference)   | [Languages](#languages) • [Algorithms](#algorithms) • [Data Structures](#data-structures)                  |
| [🎨 Design](#design-quick-reference)             | [SOLID](#solid-principles) • [Patterns](#design-patterns) • [Clean Code](#clean-code)                      |
| [🏛️ Architecture](#architecture-quick-reference) | [Patterns](#architectural-patterns) • [Scalability](#scalability) • [Performance](#performance)            |
| [💾 Database](#database-quick-reference)         | [SQL](#sql-cheat-sheet) • [NoSQL](#nosql-cheat-sheet) • [Optimization](#database-optimization)             |
| [☁️ DevOps](#devops-quick-reference)             | [Docker](#docker) • [Kubernetes](#kubernetes) • [CI/CD](#cicd)                                             |
| [🔒 Security](#security-quick-reference)         | [Authentication](#authentication) • [Encryption](#encryption) • [Best Practices](#security-best-practices) |

---

## 💻 **Programming Quick Reference**

### **Languages**

#### **JavaScript/TypeScript Flash Cards**

**Q: What are the main differences between `let`, `const`, and `var`?**

<details>
<summary>Click to reveal answer</summary>

- **`var`**: Function-scoped, hoisted, can be redeclared
- **`let`**: Block-scoped, hoisted but not initialized, cannot be redeclared
- **`const`**: Block-scoped, hoisted but not initialized, cannot be reassigned

```javascript
// var - function scoped
function example() {
  if (true) {
    var x = 1; // accessible throughout function
  }
  console.log(x); // 1
}

// let - block scoped
function example2() {
  if (true) {
    let y = 1; // only accessible in this block
  }
  console.log(y); // ReferenceError
}

// const - cannot be reassigned
const z = 1;
z = 2; // TypeError
```

</details>

**Q: How does async/await work compared to Promises?**

<details>
<summary>Click to reveal answer</summary>

```javascript
// Promise chain
fetch("/api/data")
  .then((response) => response.json())
  .then((data) => console.log(data))
  .catch((error) => console.error(error));

// Async/await (cleaner syntax)
async function fetchData() {
  try {
    const response = await fetch("/api/data");
    const data = await response.json();
    console.log(data);
  } catch (error) {
    console.error(error);
  }
}
```

**Key Points:**

- `async` functions always return a Promise
- `await` can only be used inside `async` functions
- Error handling with try/catch instead of .catch()
</details>

**Q: What is the difference between `==` and `===`?**

<details>
<summary>Click to reveal answer</summary>

- **`==`**: Loose equality, performs type coercion
- **`===`**: Strict equality, no type coercion

```javascript
// Loose equality (==)
"5" == 5; // true (string converted to number)
null == undefined; // true
0 == false; // true

// Strict equality (===)
"5" === 5; // false (different types)
null === undefined; // false
0 === false; // false

// Always prefer === for predictable behavior
```

</details>

#### **Python Flash Cards**

**Q: What are Python's main data types?**

<details>
<summary>Click to reveal answer</summary>

```python
# Numeric types
int_num = 42
float_num = 3.14
complex_num = 3 + 4j

# Sequence types
string = "Hello"
list_data = [1, 2, 3]
tuple_data = (1, 2, 3)
range_data = range(10)

# Mapping type
dict_data = {"key": "value"}

# Set types
set_data = {1, 2, 3}
frozenset_data = frozenset([1, 2, 3])

# Boolean type
bool_data = True

# None type
none_data = None
```

</details>

**Q: What's the difference between lists and tuples?**

<details>
<summary>Click to reveal answer</summary>

| Feature         | List                 | Tuple                     |
| --------------- | -------------------- | ------------------------- |
| **Mutability**  | Mutable (can change) | Immutable (cannot change) |
| **Syntax**      | `[1, 2, 3]`          | `(1, 2, 3)`               |
| **Performance** | Slower               | Faster                    |
| **Use Case**    | Dynamic data         | Fixed data                |

```python
# List - mutable
my_list = [1, 2, 3]
my_list.append(4)  # Works
my_list[0] = 10    # Works

# Tuple - immutable
my_tuple = (1, 2, 3)
my_tuple.append(4)  # AttributeError
my_tuple[0] = 10    # TypeError
```

</details>

### **Algorithms**

#### **Big O Complexity Cheat Sheet**

| Algorithm         | Best       | Average    | Worst      | Space    |
| ----------------- | ---------- | ---------- | ---------- | -------- |
| **Bubble Sort**   | O(n)       | O(n²)      | O(n²)      | O(1)     |
| **Quick Sort**    | O(n log n) | O(n log n) | O(n²)      | O(log n) |
| **Merge Sort**    | O(n log n) | O(n log n) | O(n log n) | O(n)     |
| **Binary Search** | O(1)       | O(log n)   | O(log n)   | O(1)     |
| **Linear Search** | O(1)       | O(n)       | O(n)       | O(1)     |
| **Hash Table**    | O(1)       | O(1)       | O(n)       | O(n)     |

#### **Common Algorithm Patterns**

**Two Pointers Pattern**

```python
def two_sum_sorted(arr, target):
    left, right = 0, len(arr) - 1

    while left < right:
        current_sum = arr[left] + arr[right]
        if current_sum == target:
            return [left, right]
        elif current_sum < target:
            left += 1
        else:
            right -= 1

    return []
```

**Sliding Window Pattern**

```python
def max_sum_subarray(arr, k):
    if len(arr) < k:
        return None

    # Calculate sum of first window
    window_sum = sum(arr[:k])
    max_sum = window_sum

    # Slide the window
    for i in range(k, len(arr)):
        window_sum = window_sum - arr[i - k] + arr[i]
        max_sum = max(max_sum, window_sum)

    return max_sum
```

### **Data Structures**

#### **Data Structure Operations Cheat Sheet**

| Data Structure  | Access   | Search   | Insertion | Deletion | Space |
| --------------- | -------- | -------- | --------- | -------- | ----- |
| **Array**       | O(1)     | O(n)     | O(n)      | O(n)     | O(n)  |
| **Linked List** | O(n)     | O(n)     | O(1)      | O(1)     | O(n)  |
| **Stack**       | O(n)     | O(n)     | O(1)      | O(1)     | O(n)  |
| **Queue**       | O(n)     | O(n)     | O(1)      | O(1)     | O(n)  |
| **Hash Table**  | N/A      | O(1)     | O(1)      | O(1)     | O(n)  |
| **Binary Tree** | O(n)     | O(n)     | O(n)      | O(n)     | O(n)  |
| **BST**         | O(log n) | O(log n) | O(log n)  | O(log n) | O(n)  |

#### **When to Use Each Data Structure**

**Array/List**

- ✅ Random access needed
- ✅ Memory efficiency important
- ❌ Frequent insertions/deletions in middle

**Linked List**

- ✅ Frequent insertions/deletions
- ✅ Unknown size
- ❌ Random access needed

**Stack (LIFO)**

- ✅ Undo operations
- ✅ Function call management
- ✅ Expression evaluation

**Queue (FIFO)**

- ✅ Task scheduling
- ✅ Breadth-first search
- ✅ Buffer for data streams

**Hash Table/Map**

- ✅ Fast lookups
- ✅ Caching
- ✅ Counting frequencies

**Binary Search Tree**

- ✅ Sorted data
- ✅ Range queries
- ✅ Fast search, insert, delete

---

## 🎨 **Design Quick Reference**

### **SOLID Principles**

#### **SOLID Flash Cards**

**Q: What does the Single Responsibility Principle (SRP) mean?**

<details>
<summary>Click to reveal answer</summary>

**"A class should have only one reason to change"**

```typescript
// ❌ BAD: Multiple responsibilities
class User {
  saveToDatabase() {
    /* ... */
  }
  sendEmail() {
    /* ... */
  }
  validateData() {
    /* ... */
  }
  generateReport() {
    /* ... */
  }
}

// ✅ GOOD: Single responsibility
class User {
  constructor(private data: UserData) {}
}

class UserRepository {
  save(user: User) {
    /* ... */
  }
}

class EmailService {
  sendWelcomeEmail(user: User) {
    /* ... */
  }
}
```

**Benefits:**

- Easier to test
- Easier to maintain
- Reduced coupling
- Better reusability
</details>

**Q: What is the Open/Closed Principle (OCP)?**

<details>
<summary>Click to reveal answer</summary>

**"Open for extension, closed for modification"**

```typescript
// ❌ BAD: Must modify existing code
class PaymentProcessor {
  process(type: string, amount: number) {
    if (type === "credit") {
      // credit card logic
    } else if (type === "paypal") {
      // paypal logic
    }
    // Adding new payment method requires modification
  }
}

// ✅ GOOD: Extensible without modification
interface PaymentMethod {
  process(amount: number): void;
}

class CreditCardPayment implements PaymentMethod {
  process(amount: number) {
    /* ... */
  }
}

class PayPalPayment implements PaymentMethod {
  process(amount: number) {
    /* ... */
  }
}

class PaymentProcessor {
  constructor(private method: PaymentMethod) {}

  process(amount: number) {
    this.method.process(amount);
  }
}
```

</details>

### **Design Patterns**

#### **Gang of Four Patterns Quick Reference**

**Creational Patterns**

- **Singleton**: Ensure only one instance exists
- **Factory Method**: Create objects without specifying exact class
- **Abstract Factory**: Create families of related objects
- **Builder**: Construct complex objects step by step
- **Prototype**: Clone objects instead of creating new ones

**Structural Patterns**

- **Adapter**: Make incompatible interfaces work together
- **Bridge**: Separate abstraction from implementation
- **Composite**: Compose objects into tree structures
- **Decorator**: Add behavior to objects dynamically
- **Facade**: Provide simplified interface to complex subsystem
- **Flyweight**: Share common parts of objects to save memory
- **Proxy**: Provide placeholder for another object

**Behavioral Patterns**

- **Observer**: Define one-to-many dependency between objects
- **Strategy**: Define family of algorithms, make them interchangeable
- **Command**: Encapsulate requests as objects
- **State**: Allow object to alter behavior when internal state changes
- **Template Method**: Define skeleton of algorithm in base class
- **Chain of Responsibility**: Pass requests along chain of handlers
- **Iterator**: Provide way to access elements sequentially
- **Mediator**: Define how objects interact with each other
- **Memento**: Capture and restore object's internal state
- **Visitor**: Define new operations without changing object structure

#### **Most Common Patterns**

**Observer Pattern**

```typescript
interface Observer {
  update(data: any): void;
}

class Subject {
  private observers: Observer[] = [];

  subscribe(observer: Observer) {
    this.observers.push(observer);
  }

  notify(data: any) {
    this.observers.forEach((observer) => observer.update(data));
  }
}
```

**Strategy Pattern**

```typescript
interface SortStrategy {
  sort(data: number[]): number[];
}

class BubbleSort implements SortStrategy {
  sort(data: number[]): number[] {
    // Bubble sort implementation
    return data;
  }
}

class QuickSort implements SortStrategy {
  sort(data: number[]): number[] {
    // Quick sort implementation
    return data;
  }
}

class Sorter {
  constructor(private strategy: SortStrategy) {}

  sort(data: number[]): number[] {
    return this.strategy.sort(data);
  }
}
```

### **Clean Code**

#### **Clean Code Principles**

**Meaningful Names**

```typescript
// ❌ BAD
const d = new Date();
const u = users.filter((u) => u.a > 18);

// ✅ GOOD
const currentDate = new Date();
const adultUsers = users.filter((user) => user.age > 18);
```

**Small Functions**

```typescript
// ❌ BAD: Function does too much
function processUser(userData: any) {
  // Validate data (20 lines)
  // Save to database (15 lines)
  // Send email (10 lines)
  // Log activity (5 lines)
}

// ✅ GOOD: Small, focused functions
function validateUserData(userData: UserData): ValidationResult {
  // Validation logic only
}

function saveUser(userData: UserData): User {
  // Database logic only
}

function sendWelcomeEmail(user: User): void {
  // Email logic only
}
```

**Comments**

```typescript
// ❌ BAD: Obvious comment
// Increment i by 1
i++;

// ❌ BAD: Outdated comment
// Check if user is admin (but code checks for manager)
if (user.role === "manager") {
  // ...
}

// ✅ GOOD: Explains WHY, not WHAT
// Use exponential backoff to handle rate limiting
const delay = Math.pow(2, attempt) * 1000;

// ✅ GOOD: Explains business rule
// Users must be 18+ to access premium features
if (user.age >= 18) {
  // ...
}
```

---

## 🏛️ **Architecture Quick Reference**

### **Architectural Patterns**

#### **Common Architecture Styles**

**Layered Architecture**

```
┌─────────────────┐
│ Presentation    │ ← UI, Controllers
├─────────────────┤
│ Business Logic  │ ← Services, Domain
├─────────────────┤
│ Data Access     │ ← Repositories, DAOs
├─────────────────┤
│ Database        │ ← Persistence Layer
└─────────────────┘
```

**Clean Architecture**

```
┌─────────────────────────────────┐
│           Frameworks            │
├─────────────────────────────────┤
│        Interface Adapters       │
├─────────────────────────────────┤
│         Application             │
├─────────────────────────────────┤
│           Entities              │ ← Core Business Logic
└─────────────────────────────────┘
```

**Microservices Architecture**

```
┌─────────┐    ┌─────────┐    ┌─────────┐
│Service A│    │Service B│    │Service C│
│   DB    │    │   DB    │    │   DB    │
└─────────┘    └─────────┘    └─────────┘
     │              │              │
     └──────────────┼──────────────┘
                    │
            ┌─────────────┐
            │ API Gateway │
            └─────────────┘
```

### **Scalability Patterns**

#### **Horizontal vs Vertical Scaling**

**Vertical Scaling (Scale Up)**

- Add more power (CPU, RAM) to existing machine
- ✅ Simple to implement
- ❌ Limited by hardware constraints
- ❌ Single point of failure

**Horizontal Scaling (Scale Out)**

- Add more machines to the pool
- ✅ Unlimited scaling potential
- ✅ Better fault tolerance
- ❌ More complex to implement

#### **Load Balancing Strategies**

**Round Robin**

```
Request 1 → Server A
Request 2 → Server B
Request 3 → Server C
Request 4 → Server A (repeat)
```

**Least Connections**

```
Route to server with fewest active connections
```

**Weighted Round Robin**

```
Server A (weight: 3) gets 3x more requests than Server B (weight: 1)
```

### **Performance Optimization**

#### **Caching Strategies**

**Cache-Aside (Lazy Loading)**

```python
def get_user(user_id):
    # Check cache first
    user = cache.get(f"user:{user_id}")
    if user:
        return user

    # Cache miss - fetch from database
    user = database.get_user(user_id)
    cache.set(f"user:{user_id}", user, ttl=3600)
    return user
```

**Write-Through**

```python
def update_user(user_id, data):
    # Update database first
    database.update_user(user_id, data)

    # Then update cache
    cache.set(f"user:{user_id}", data, ttl=3600)
```

**Write-Behind (Write-Back)**

```python
def update_user(user_id, data):
    # Update cache immediately
    cache.set(f"user:{user_id}", data, ttl=3600)

    # Queue database update for later
    queue.add_task('update_user_db', user_id, data)
```

---

## 💾 **Database Quick Reference**

### **SQL Cheat Sheet**

#### **Basic Queries**

```sql
-- SELECT with conditions
SELECT name, email, age
FROM users
WHERE age >= 18
  AND status = 'active'
ORDER BY name ASC
LIMIT 10;

-- JOIN operations
SELECT u.name, p.title
FROM users u
INNER JOIN posts p ON u.id = p.user_id
WHERE u.active = true;

-- Aggregations
SELECT department,
       COUNT(*) as employee_count,
       AVG(salary) as avg_salary
FROM employees
GROUP BY department
HAVING COUNT(*) > 5;

-- Subqueries
SELECT name
FROM users
WHERE id IN (
    SELECT user_id
    FROM orders
    WHERE total > 1000
);
```

#### **Performance Tips**

**Indexing**

```sql
-- Create index for faster queries
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_date ON orders(user_id, created_at);

-- Composite index for multiple columns
CREATE INDEX idx_users_status_age ON users(status, age);
```

**Query Optimization**

```sql
-- ❌ BAD: SELECT *
SELECT * FROM users WHERE email = '<EMAIL>';

-- ✅ GOOD: Select only needed columns
SELECT id, name, email FROM users WHERE email = '<EMAIL>';

-- ❌ BAD: Function in WHERE clause
SELECT * FROM orders WHERE YEAR(created_at) = 2023;

-- ✅ GOOD: Range query
SELECT * FROM orders
WHERE created_at >= '2023-01-01'
  AND created_at < '2024-01-01';
```

### **NoSQL Cheat Sheet**

#### **MongoDB Operations**

```javascript
// Insert documents
db.users.insertOne({
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
  tags: ["developer", "javascript"],
});

// Find documents
db.users.find({
  age: { $gte: 18 },
  tags: { $in: ["developer"] },
});

// Update documents
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { age: 31 }, $push: { tags: "senior" } }
);

// Aggregation pipeline
db.users.aggregate([
  { $match: { age: { $gte: 18 } } },
  { $group: { _id: "$department", count: { $sum: 1 } } },
  { $sort: { count: -1 } },
]);
```

#### **Redis Commands**

```bash
# String operations
SET user:1:name "John Doe"
GET user:1:name
INCR user:1:visits

# Hash operations
HSET user:1 name "John" email "<EMAIL>"
HGET user:1 name
HGETALL user:1

# List operations
LPUSH queue:tasks "task1"
RPOP queue:tasks

# Set operations
SADD user:1:tags "developer" "javascript"
SMEMBERS user:1:tags

# Expiration
EXPIRE user:1:session 3600  # 1 hour
TTL user:1:session
```

---

## ☁️ **DevOps Quick Reference**

### **Docker**

#### **Essential Docker Commands**

```bash
# Build image
docker build -t myapp:latest .

# Run container
docker run -d -p 8080:80 --name myapp-container myapp:latest

# List containers
docker ps -a

# View logs
docker logs myapp-container

# Execute command in container
docker exec -it myapp-container bash

# Stop and remove
docker stop myapp-container
docker rm myapp-container

# Clean up
docker system prune -a
```

#### **Dockerfile Best Practices**

```dockerfile
# Use specific version tags
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files first (for better caching)
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["npm", "start"]
```

### **Kubernetes**

#### **Essential kubectl Commands**

```bash
# Get resources
kubectl get pods
kubectl get services
kubectl get deployments

# Describe resources
kubectl describe pod <pod-name>
kubectl describe service <service-name>

# Apply configuration
kubectl apply -f deployment.yaml

# Scale deployment
kubectl scale deployment myapp --replicas=3

# View logs
kubectl logs <pod-name>
kubectl logs -f <pod-name>  # Follow logs

# Port forwarding
kubectl port-forward pod/<pod-name> 8080:80

# Delete resources
kubectl delete pod <pod-name>
kubectl delete -f deployment.yaml
```

#### **Basic Kubernetes Manifests**

**Deployment**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
        - name: myapp
          image: myapp:latest
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: "64Mi"
              cpu: "250m"
            limits:
              memory: "128Mi"
              cpu: "500m"
```

**Service**

```yaml
apiVersion: v1
kind: Service
metadata:
  name: myapp-service
spec:
  selector:
    app: myapp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
```

### **CI/CD**

#### **GitHub Actions Example**

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Run linting
        run: npm run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3

      - name: Build Docker image
        run: docker build -t myapp:${{ github.sha }} .

      - name: Deploy to production
        run: |
          # Deployment commands here
          echo "Deploying to production..."
```

---

## 🔒 **Security Quick Reference**

### **Authentication & Authorization**

#### **JWT Token Structure**

```
Header.Payload.Signature

eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.
eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.
SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**JWT Implementation**

```javascript
// Generate JWT
const jwt = require("jsonwebtoken");

const token = jwt.sign(
  { userId: user.id, role: user.role },
  process.env.JWT_SECRET,
  { expiresIn: "1h" }
);

// Verify JWT
const decoded = jwt.verify(token, process.env.JWT_SECRET);
```

#### **Password Security**

```javascript
const bcrypt = require("bcrypt");

// Hash password
const saltRounds = 12;
const hashedPassword = await bcrypt.hash(password, saltRounds);

// Verify password
const isValid = await bcrypt.compare(password, hashedPassword);
```

### **Common Security Vulnerabilities**

#### **OWASP Top 10 Quick Reference**

1. **Injection** - SQL, NoSQL, OS injection
2. **Broken Authentication** - Session management flaws
3. **Sensitive Data Exposure** - Unencrypted data
4. **XML External Entities (XXE)** - XML parser vulnerabilities
5. **Broken Access Control** - Authorization flaws
6. **Security Misconfiguration** - Default configurations
7. **Cross-Site Scripting (XSS)** - Script injection
8. **Insecure Deserialization** - Object injection
9. **Known Vulnerabilities** - Outdated components
10. **Insufficient Logging** - Poor monitoring

#### **Input Validation**

```javascript
// ❌ BAD: No validation
app.post("/user", (req, res) => {
  const user = new User(req.body);
  user.save();
});

// ✅ GOOD: Proper validation
const Joi = require("joi");

const userSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  age: Joi.number().integer().min(0).max(120),
});

app.post("/user", (req, res) => {
  const { error, value } = userSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }

  const user = new User(value);
  user.save();
});
```

### **Security Best Practices**

#### **HTTPS Configuration**

```javascript
const express = require("express");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");

const app = express();

// Security headers
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// CORS configuration
app.use(
  cors({
    origin: process.env.ALLOWED_ORIGINS?.split(",") || "http://localhost:3000",
    credentials: true,
  })
);
```

---

**⚡ This quick reference provides instant access to the most essential knowledge across all IT domains. Bookmark this page for rapid lookups during development, interviews, and daily work!**

// ❌ BAD: Outdated comment
// Check if user is admin (but code checks for manager)
if (user.role === "manager") {
// ...
}

// ✅ GOOD: Explains WHY, not WHAT
// Use exponential backoff to handle rate limiting
const delay = Math.pow(2, attempt) \* 1000;

// ✅ GOOD: Explains business rule
// Users must be 18+ to access premium features
if (user.age >= 18) {
// ...
}

```

```
