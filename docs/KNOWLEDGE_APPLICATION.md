# 🧠 **K<PERSON><PERSON><PERSON>D<PERSON> APPLICATION FRAMEWORK**

> **Complete guide** để áp dụng toàn bộ kiến thức từ KNOWLEDGE_BASE.md vào mọi tính năng

## **🎯 Knowledge Integration Strategy**

### **📚 Knowledge Mapping Process**

```typescript
// 🧠 KNOWLEDGE: Knowledge Application Framework
interface KnowledgeApplication {
  // 🏗️ Architecture Knowledge → Implementation
  architecture: {
    source: "System Design principles from KNOWLEDGE_BASE.md";
    application: "Microservices + Event-Driven + DDD implementation";
    evidence: "Clean separation, SOLID principles, scalable design";
  };

  // 💾 Database Knowledge → Implementation
  database: {
    source: "Database Engineering principles";
    application: "PostgreSQL + Redis + Vector DB architecture";
    evidence: "ACID compliance, caching strategies, performance optimization";
  };

  // 🔐 Security Knowledge → Implementation
  security: {
    source: "Security principles (CIA Triad, Zero Trust)";
    application: "JWT + OAuth + mTLS + Input validation";
    evidence: "Defense in depth, least privilege, secure by design";
  };
}
```

## **🏗️ SOLID Principles Implementation**

### **1. Single Responsibility Principle (SRP)**

```typescript
// ❌ BAD: Multiple responsibilities
class UserService {
  createUser(data: UserData) {
    /* ... */
  }
  sendWelcomeEmail(user: User) {
    /* ... */
  }
  logUserActivity(user: User) {
    /* ... */
  }
  validateUserData(data: UserData) {
    /* ... */
  }
}

// ✅ GOOD: Single responsibility per class
class UserService {
  constructor(
    private userRepository: IUserRepository,
    private emailService: IEmailService,
    private activityLogger: IActivityLogger,
    private validator: IUserValidator
  ) {}

  async createUser(data: UserData): Promise<Result<User>> {
    // Validation
    const validationResult = this.validator.validate(data);
    if (validationResult.isFailure) {
      return Result.fail(validationResult.error);
    }

    // Create user
    const user = User.create(data);
    await this.userRepository.save(user);

    // Trigger side effects
    await this.emailService.sendWelcomeEmail(user);
    await this.activityLogger.logUserCreation(user);

    return Result.ok(user);
  }
}

// Separate responsibilities
class EmailService implements IEmailService {
  async sendWelcomeEmail(user: User): Promise<void> {
    // Only handles email sending
  }
}

class ActivityLogger implements IActivityLogger {
  async logUserCreation(user: User): Promise<void> {
    // Only handles activity logging
  }
}

class UserValidator implements IUserValidator {
  validate(data: UserData): ValidationResult {
    // Only handles validation logic
  }
}
```

### **2. Open/Closed Principle (OCP)**

```typescript
// ✅ GOOD: Open for extension, closed for modification
abstract class NotificationStrategy {
  abstract send(message: string, recipient: string): Promise<void>;
}

class EmailNotification extends NotificationStrategy {
  async send(message: string, recipient: string): Promise<void> {
    // Email implementation
  }
}

class SMSNotification extends NotificationStrategy {
  async send(message: string, recipient: string): Promise<void> {
    // SMS implementation
  }
}

class SlackNotification extends NotificationStrategy {
  async send(message: string, recipient: string): Promise<void> {
    // Slack implementation
  }
}

// NotificationService is closed for modification
class NotificationService {
  constructor(private strategies: NotificationStrategy[]) {}

  async notify(message: string, recipient: string): Promise<void> {
    // Can add new strategies without modifying this class
    for (const strategy of this.strategies) {
      await strategy.send(message, recipient);
    }
  }
}
```

### **3. Liskov Substitution Principle (LSP)**

```typescript
// ✅ GOOD: Subtypes must be substitutable for base types
abstract class Repository<T extends Entity> {
  abstract save(entity: T): Promise<void>;
  abstract findById(id: string): Promise<T | null>;
  abstract delete(id: string): Promise<void>;
}

class PostgresUserRepository extends Repository<User> {
  async save(user: User): Promise<void> {
    // PostgreSQL implementation
    // Behaves exactly as parent contract specifies
  }

  async findById(id: string): Promise<User | null> {
    // Returns User or null as contract specifies
  }

  async delete(id: string): Promise<void> {
    // Soft delete implementation
  }
}

class RedisUserRepository extends Repository<User> {
  async save(user: User): Promise<void> {
    // Redis implementation
    // Also behaves exactly as parent contract specifies
  }

  async findById(id: string): Promise<User | null> {
    // Returns User or null as contract specifies
  }

  async delete(id: string): Promise<void> {
    // Cache removal implementation
  }
}

// Can substitute any implementation
function processUser(repository: Repository<User>) {
  // Works with any implementation
  const user = await repository.findById("123");
  if (user) {
    await repository.save(user);
  }
}
```

### **4. Interface Segregation Principle (ISP)**

```typescript
// ❌ BAD: Fat interface
interface UserOperations {
  create(data: UserData): Promise<User>;
  update(id: string, data: Partial<UserData>): Promise<User>;
  delete(id: string): Promise<void>;
  sendEmail(id: string, message: string): Promise<void>;
  generateReport(id: string): Promise<Report>;
  updatePermissions(id: string, permissions: Permission[]): Promise<void>;
}

// ✅ GOOD: Segregated interfaces
interface UserRepository {
  create(data: UserData): Promise<User>;
  update(id: string, data: Partial<UserData>): Promise<User>;
  delete(id: string): Promise<void>;
}

interface UserEmailService {
  sendEmail(id: string, message: string): Promise<void>;
}

interface UserReportService {
  generateReport(id: string): Promise<Report>;
}

interface UserPermissionService {
  updatePermissions(id: string, permissions: Permission[]): Promise<void>;
}

// Clients only depend on what they need
class UserController {
  constructor(
    private userRepository: UserRepository,
    private emailService: UserEmailService
  ) {}
  // Only uses repository and email service
}

class UserReportController {
  constructor(private reportService: UserReportService) {}
  // Only uses report service
}
```

### **5. Dependency Inversion Principle (DIP)**

```typescript
// ❌ BAD: High-level depends on low-level
class UserService {
  private userRepository = new PostgresUserRepository(); // Concrete dependency
  private emailService = new SMTPEmailService(); // Concrete dependency

  async createUser(data: UserData): Promise<User> {
    // Tightly coupled to concrete implementations
  }
}

// ✅ GOOD: Depend on abstractions
interface IUserRepository {
  save(user: User): Promise<void>;
  findById(id: string): Promise<User | null>;
}

interface IEmailService {
  sendWelcomeEmail(user: User): Promise<void>;
}

class UserService {
  constructor(
    private userRepository: IUserRepository, // Abstraction
    private emailService: IEmailService // Abstraction
  ) {}

  async createUser(data: UserData): Promise<Result<User>> {
    const user = User.create(data);
    await this.userRepository.save(user);
    await this.emailService.sendWelcomeEmail(user);
    return Result.ok(user);
  }
}

// Dependency injection configuration
const userRepository = new PostgresUserRepository();
const emailService = new SMTPEmailService();
const userService = new UserService(userRepository, emailService);
```

## **🎨 Design Patterns Implementation**

### **1. Repository Pattern**

```typescript
// 🧠 KNOWLEDGE: Repository Pattern for Data Access Abstraction
interface ITaskRepository {
  save(task: Task): Promise<void>;
  findById(id: TaskId): Promise<Task | null>;
  findByUserId(userId: UserId): Promise<Task[]>;
  findByStatus(status: TaskStatus): Promise<Task[]>;
  delete(id: TaskId): Promise<void>;
}

class PostgresTaskRepository implements ITaskRepository {
  async save(task: Task): Promise<void> {
    const taskEntity = TaskMapper.toEntity(task);
    await this.entityManager.save(taskEntity);
  }

  async findById(id: TaskId): Promise<Task | null> {
    const entity = await this.entityManager.findOne(TaskEntity, {
      where: { id: id.value },
    });
    return entity ? TaskMapper.toDomain(entity) : null;
  }

  async findByUserId(userId: UserId): Promise<Task[]> {
    const entities = await this.entityManager.find(TaskEntity, {
      where: { userId: userId.value },
      order: { createdAt: "DESC" },
    });
    return entities.map(TaskMapper.toDomain);
  }
}
```

### **2. Factory Pattern**

```typescript
// 🧠 KNOWLEDGE: Factory Pattern for Object Creation
interface TaskFactory {
  createTask(type: TaskType, data: TaskData): Result<Task>;
}

class ConcreteTaskFactory implements TaskFactory {
  createTask(type: TaskType, data: TaskData): Result<Task> {
    switch (type) {
      case TaskType.PERSONAL:
        return PersonalTask.create(data);
      case TaskType.PROJECT:
        return ProjectTask.create(data);
      case TaskType.RECURRING:
        return RecurringTask.create(data);
      default:
        return Result.fail(`Unsupported task type: ${type}`);
    }
  }
}

// Usage
const factory = new ConcreteTaskFactory();
const taskResult = factory.createTask(TaskType.PERSONAL, taskData);
```

### **3. Observer Pattern (Domain Events)**

```typescript
// 🧠 KNOWLEDGE: Observer Pattern for Loose Coupling
interface DomainEvent {
  dateTimeOccurred: Date;
  aggregateId: string;
}

class TaskCompletedEvent implements DomainEvent {
  public readonly dateTimeOccurred: Date;
  public readonly aggregateId: string;
  public readonly taskId: TaskId;
  public readonly userId: UserId;

  constructor(taskId: TaskId, userId: UserId) {
    this.dateTimeOccurred = new Date();
    this.aggregateId = taskId.value;
    this.taskId = taskId;
    this.userId = userId;
  }
}

interface DomainEventHandler<T extends DomainEvent> {
  handle(event: T): Promise<void>;
}

class TaskCompletedEventHandler
  implements DomainEventHandler<TaskCompletedEvent>
{
  constructor(
    private notificationService: INotificationService,
    private analyticsService: IAnalyticsService
  ) {}

  async handle(event: TaskCompletedEvent): Promise<void> {
    // Send completion notification
    await this.notificationService.sendTaskCompletionNotification(
      event.userId,
      event.taskId
    );

    // Track analytics
    await this.analyticsService.trackTaskCompletion(event.taskId);
  }
}
```

### **4. Command Pattern (CQRS)**

```typescript
// 🧠 KNOWLEDGE: Command Pattern for CQRS Implementation
interface Command {
  readonly id: string;
  readonly timestamp: Date;
}

interface CommandHandler<T extends Command> {
  handle(command: T): Promise<Result<void>>;
}

class CreateTaskCommand implements Command {
  public readonly id: string;
  public readonly timestamp: Date;

  constructor(
    public readonly userId: UserId,
    public readonly title: string,
    public readonly description: string,
    public readonly priority: TaskPriority
  ) {
    this.id = uuidv4();
    this.timestamp = new Date();
  }
}

class CreateTaskCommandHandler implements CommandHandler<CreateTaskCommand> {
  constructor(
    private taskRepository: ITaskRepository,
    private eventPublisher: IEventPublisher
  ) {}

  async handle(command: CreateTaskCommand): Promise<Result<void>> {
    // Create task aggregate
    const taskResult = Task.create({
      userId: command.userId,
      title: command.title,
      description: command.description,
      priority: command.priority,
    });

    if (taskResult.isFailure) {
      return Result.fail(taskResult.error);
    }

    const task = taskResult.value;

    // Save to repository
    await this.taskRepository.save(task);

    // Publish domain events
    await this.eventPublisher.publishAll(task.domainEvents);

    return Result.ok();
  }
}
```

## **🧪 Test-Driven Development (TDD)**

### **TDD Workflow Implementation**

```typescript
// 🧠 KNOWLEDGE: TDD Red-Green-Refactor Cycle

// 🔴 RED: Write failing test first
describe("TaskService", () => {
  describe("createTask", () => {
    it("should create a task with valid data", async () => {
      // Arrange
      const taskData = {
        userId: new UserId("user-123"),
        title: "Learn TDD",
        description: "Practice Test-Driven Development",
        priority: TaskPriority.HIGH,
      };

      const mockRepository = createMockRepository();
      const taskService = new TaskService(mockRepository);

      // Act
      const result = await taskService.createTask(taskData);

      // Assert
      expect(result.isSuccess).toBe(true);
      expect(result.value.title).toBe("Learn TDD");
      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Learn TDD",
          userId: taskData.userId,
        })
      );
    });

    it("should fail when title is empty", async () => {
      // Arrange
      const invalidData = {
        userId: new UserId("user-123"),
        title: "", // Invalid empty title
        description: "Description",
        priority: TaskPriority.LOW,
      };

      const taskService = new TaskService(createMockRepository());

      // Act
      const result = await taskService.createTask(invalidData);

      // Assert
      expect(result.isFailure).toBe(true);
      expect(result.error).toContain("Title cannot be empty");
    });
  });
});

// 🟢 GREEN: Make test pass with minimal code
class TaskService {
  constructor(private taskRepository: ITaskRepository) {}

  async createTask(data: TaskData): Promise<Result<Task>> {
    // Validation
    if (!data.title || data.title.trim() === "") {
      return Result.fail("Title cannot be empty");
    }

    // Create task
    const task = Task.create(data);

    // Save
    await this.taskRepository.save(task);

    return Result.ok(task);
  }
}

// 🔵 REFACTOR: Improve code while keeping tests green
class TaskService {
  constructor(
    private taskRepository: ITaskRepository,
    private validator: ITaskValidator,
    private eventPublisher: IEventPublisher
  ) {}

  async createTask(data: TaskData): Promise<Result<Task>> {
    // Extract validation to separate service
    const validationResult = await this.validator.validate(data);
    if (validationResult.isFailure) {
      return Result.fail(validationResult.error);
    }

    // Use factory pattern for task creation
    const taskResult = Task.create(data);
    if (taskResult.isFailure) {
      return Result.fail(taskResult.error);
    }

    const task = taskResult.value;

    // Save with transaction
    await this.taskRepository.save(task);

    // Publish domain events
    await this.eventPublisher.publishAll(task.domainEvents);

    return Result.ok(task);
  }
}
```

## **🔐 Security Implementation**

### **Authentication & Authorization**

```typescript
// 🧠 KNOWLEDGE: Security by Design Implementation
interface AuthenticationService {
  authenticate(credentials: LoginCredentials): Promise<Result<AuthToken>>;
  validateToken(token: string): Promise<Result<UserClaims>>;
  refreshToken(refreshToken: string): Promise<Result<AuthToken>>;
}

class JWTAuthenticationService implements AuthenticationService {
  constructor(
    private userRepository: IUserRepository,
    private passwordService: IPasswordService,
    private tokenService: ITokenService
  ) {}

  async authenticate(
    credentials: LoginCredentials
  ): Promise<Result<AuthToken>> {
    // Input validation
    const validationResult = this.validateCredentials(credentials);
    if (validationResult.isFailure) {
      return Result.fail(validationResult.error);
    }

    // Find user
    const user = await this.userRepository.findByEmail(credentials.email);
    if (!user) {
      return Result.fail("Invalid credentials");
    }

    // Verify password
    const isValidPassword = await this.passwordService.verify(
      credentials.password,
      user.hashedPassword
    );

    if (!isValidPassword) {
      return Result.fail("Invalid credentials");
    }

    // Generate tokens
    const accessToken = this.tokenService.generateAccessToken(user);
    const refreshToken = this.tokenService.generateRefreshToken(user);

    return Result.ok({
      accessToken,
      refreshToken,
      expiresIn: 3600,
    });
  }

  private validateCredentials(credentials: LoginCredentials): Result<void> {
    // Email validation
    if (!this.isValidEmail(credentials.email)) {
      return Result.fail("Invalid email format");
    }

    // Password strength validation
    if (credentials.password.length < 8) {
      return Result.fail("Password must be at least 8 characters");
    }

    return Result.ok();
  }
}

// Authorization middleware
class AuthorizationMiddleware {
  constructor(private authService: AuthenticationService) {}

  authorize(requiredRole: Role) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const token = this.extractToken(req);
        if (!token) {
          return res.status(401).json({ error: "No token provided" });
        }

        const claimsResult = await this.authService.validateToken(token);
        if (claimsResult.isFailure) {
          return res.status(401).json({ error: "Invalid token" });
        }

        const claims = claimsResult.value;
        if (!this.hasRequiredRole(claims.roles, requiredRole)) {
          return res.status(403).json({ error: "Insufficient permissions" });
        }

        req.user = claims;
        next();
      } catch (error) {
        return res.status(500).json({ error: "Authentication error" });
      }
    };
  }
}
```

## **⚡ Performance Optimization**

### **Caching Strategy Implementation**

```typescript
// 🧠 KNOWLEDGE: Multi-Level Caching Strategy
interface CacheService<T> {
  get(key: string): Promise<T | null>;
  set(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

class RedisCacheService<T> implements CacheService<T> {
  constructor(private redisClient: Redis) {}

  async get(key: string): Promise<T | null> {
    const value = await this.redisClient.get(key);
    return value ? JSON.parse(value) : null;
  }

  async set(key: string, value: T, ttl: number = 3600): Promise<void> {
    await this.redisClient.setex(key, ttl, JSON.stringify(value));
  }

  async delete(key: string): Promise<void> {
    await this.redisClient.del(key);
  }

  async clear(): Promise<void> {
    await this.redisClient.flushall();
  }
}

// Caching decorator for repository
class CachedTaskRepository implements ITaskRepository {
  constructor(
    private repository: ITaskRepository,
    private cache: CacheService<Task>
  ) {}

  async findById(id: TaskId): Promise<Task | null> {
    // Try cache first
    const cacheKey = `task:${id.value}`;
    const cachedTask = await this.cache.get(cacheKey);

    if (cachedTask) {
      return cachedTask;
    }

    // Fallback to repository
    const task = await this.repository.findById(id);

    if (task) {
      // Cache for 1 hour
      await this.cache.set(cacheKey, task, 3600);
    }

    return task;
  }

  async save(task: Task): Promise<void> {
    await this.repository.save(task);

    // Update cache
    const cacheKey = `task:${task.id.value}`;
    await this.cache.set(cacheKey, task, 3600);
  }
}
```

### **Database Query Optimization**

```typescript
// 🧠 KNOWLEDGE: Query Optimization Techniques
class OptimizedTaskRepository implements ITaskRepository {
  // Use database indexes effectively
  async findTasksByUserWithPagination(
    userId: UserId,
    page: number,
    limit: number
  ): Promise<PaginatedResult<Task>> {
    // Optimized query with proper indexing
    const query = `
      SELECT t.*, u.name as user_name
      FROM tasks t
      INNER JOIN users u ON t.user_id = u.id
      WHERE t.user_id = $1 
        AND t.deleted_at IS NULL
      ORDER BY t.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const offset = (page - 1) * limit;
    const tasks = await this.db.query(query, [userId.value, limit, offset]);

    // Count query for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM tasks t
      WHERE t.user_id = $1 AND t.deleted_at IS NULL
    `;

    const countResult = await this.db.query(countQuery, [userId.value]);
    const total = parseInt(countResult[0].total);

    return new PaginatedResult(
      tasks.map(TaskMapper.toDomain),
      page,
      limit,
      total
    );
  }

  // Batch operations for performance
  async saveMany(tasks: Task[]): Promise<void> {
    const entities = tasks.map(TaskMapper.toEntity);

    await this.db.transaction(async (trx) => {
      // Batch insert for better performance
      await trx.batchInsert("tasks", entities, 100);
    });
  }
}
```

This framework ensures that every piece of knowledge from KNOWLEDGE_BASE.md is practically applied in the codebase with concrete examples and real implementations.
