# 🚀 Quick Start Guide

## 📋 Quick Start Checklist

### Prerequisites

- [ ] Docker & Docker Compose installed
- [ ] Kubernetes cluster (local: minikube/kind, cloud: EKS/GKE/AKS)
- [ ] Node.js 18+ and npm/yarn/pnpm
- [ ] Python 3.9+ and pip
- [ ] Go 1.19+ (optional, for performance services)
- [ ] Git with SSH keys configured

### Initial Setup (5 minutes)

```bash
# 1. Clone the repository
git clone https://github.com/your-org/enterprise-platform.git
cd enterprise-platform

# 2. Install root dependencies
npm install

# 3. Copy environment templates
cp .env.example .env
cp services/user-service/.env.example services/user-service/.env
cp services/ai-service/.env.example services/ai-service/.env

# 4. Start infrastructure services
docker-compose up -d postgres redis mongodb kafka qdrant

# 5. Wait for services to be ready (check with docker-compose ps)
npm run health-check

# 6. Run database migrations
npm run migration:run

# 7. Seed initial data
npm run seed

# 8. Start development services
npm run dev
```

### Alternative Setup Commands

```bash
# One-command setup (requires Docker, Node.js 18+, Python 3.9+)
./tools/scripts/setup.sh

# Alternative: Manual setup
npm install
docker-compose up -d

# Alternative: NPM-based setup
npm run setup

# Alternative verification commands:
npm run health-check
npm run dev
```

### Verification Steps

```bash
# Check all services are running
curl http://localhost:3000/health          # API Gateway
curl http://localhost:3002/health          # User Service
curl http://localhost:3003/health          # AI Service

# Check databases
docker exec postgres psql -U dev_user -d enterprise_platform -c "SELECT COUNT(*) FROM users;"
docker exec redis redis-cli ping
docker exec mongodb mongosh --eval "db.runCommand('ping')"

# Check message broker
docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list

# Check vector database
curl http://localhost:6333/collections
```

## 🎉 Access Points

- **API Gateway**: http://localhost:3000 ([Swagger Docs](http://localhost:3000/docs))
- **AI/ML Service**: http://localhost:8000 ([FastAPI Docs](http://localhost:8000/docs))
- **Grafana Dashboard**: http://localhost:3001 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Jaeger Tracing**: http://localhost:16686
- **Kibana Logs**: http://localhost:5601

## 🛠️ Development Environment

```bash
# Start with file watching and hot reload
npm run dev:watch

# Run with debug mode
npm run dev:debug

# Run specific service
cd services/user-service && npm run start:dev

# Run tests in watch mode
npm run test:watch
```

## 🔧 Docker Commands

### Sử Dụng Project Hiện Tại
```bash
# Development
./scripts/docker-commands.sh dev

# Production  
./scripts/docker-commands.sh prod

# Status check
./scripts/docker-commands.sh status
./scripts/docker-commands.sh health
```

### URLs
- **Dev App**: http://localhost:8000/health
- **Prod App**: http://localhost:8001/health  
- **Nginx**: http://localhost/health
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

## 📋 Setup Checklist Hoàn Chỉnh

### ✅ Files Cần Thiết
- [ ] `Dockerfile` (production)
- [ ] `Dockerfile.dev` (development)  
- [ ] `docker-compose.yml` (main config)
- [ ] `docker-compose.override.yml` (dev overrides)
- [ ] `nginx.conf` (load balancer)
- [ ] `.dockerignore` (build optimization)
- [ ] `scripts/docker-commands.sh` (management)
- [ ] `scripts/load-test.js` (performance test)
- [ ] `monitoring/prometheus.yml` (metrics)
- [ ] `monitoring/grafana/` (dashboards)

### ✅ Features Included
- [ ] Development environment với hot reload
- [ ] Production environment optimized
- [ ] Load balancer với Nginx
- [ ] Database services (PostgreSQL + Redis)
- [ ] Monitoring stack (Prometheus + Grafana)
- [ ] Health checks cho tất cả services
- [ ] Performance testing với k6
- [ ] Security best practices
- [ ] Resource limits và management
- [ ] Easy management scripts

### ✅ Testing Checklist
- [ ] `./scripts/docker-commands.sh dev` works
- [ ] `./scripts/docker-commands.sh prod` works
- [ ] Health endpoints respond correctly
- [ ] Monitoring dashboards accessible
- [ ] Database connections working
- [ ] Load balancer routing correctly
- [ ] Performance tests pass
- [ ] All containers have proper resource limits

## 🎯 Success Criteria

Khi setup thành công, bạn sẽ có:

✅ **Development environment** với hot reload  
✅ **Production environment** optimized và secure  
✅ **Load balancer** với rate limiting  
✅ **Database persistence** cho PostgreSQL và Redis  
✅ **Monitoring stack** với Grafana dashboards  
✅ **Health checks** cho tất cả services  
✅ **Performance testing** capabilities  
✅ **Easy management** với 17 commands  
✅ **Security best practices** implemented  
✅ **Scalable architecture** ready for production  

**Congratulations! Bạn đã có một Docker setup đỉnh cao! 🚀**