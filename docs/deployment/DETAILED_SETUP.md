# 🚀 Deployment Guide & Getting Started

## 📋 Quick Start Checklist

### Prerequisites

- [ ] Docker & Docker Compose installed
- [ ] Kubernetes cluster (local: minikube/kind, cloud: EKS/GKE/AKS)
- [ ] Node.js 18+ and npm/yarn/pnpm
- [ ] Python 3.9+ and pip
- [ ] Go 1.19+ (optional, for performance services)
- [ ] Git with SSH keys configured

### Initial Setup (5 minutes)

```bash
# 1. Clone the repository
git clone https://github.com/your-org/enterprise-platform.git
cd enterprise-platform

# 2. Install root dependencies
npm install

# 3. Copy environment templates
cp .env.example .env
cp services/user-service/.env.example services/user-service/.env
cp services/ai-service/.env.example services/ai-service/.env

# 4. Start infrastructure services
docker-compose up -d postgres redis mongodb kafka qdrant

# 5. Wait for services to be ready (check with docker-compose ps)
npm run health-check

# 6. Run database migrations
npm run migration:run

# 7. Seed initial data
npm run seed

# 8. Start development services
npm run dev
```

### Verification Steps

```bash
# Check all services are running
curl http://localhost:3000/health          # API Gateway
curl http://localhost:3002/health          # User Service
curl http://localhost:3003/health          # AI Service

# Check databases
docker exec postgres psql -U dev_user -d enterprise_platform -c "SELECT COUNT(*) FROM users;"
docker exec redis redis-cli ping
docker exec mongodb mongosh --eval "db.runCommand('ping')"

# Check message broker
docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list

# Check vector database
curl http://localhost:6333/collections
```

## 🏗️ Deployment Strategies

### 1. Development Environment

```bash
# Start with file watching and hot reload
npm run dev:watch

# Run with debug mode
npm run dev:debug

# Run specific service
cd services/user-service && npm run start:dev

# Run tests in watch mode
npm run test:watch
```

**Access Points:**

- API Gateway: http://localhost:3000
- User Service: http://localhost:3002
- AI Service: http://localhost:3003
- Admin Panel: http://localhost:3001
- Grafana: http://localhost:3001
- Prometheus: http://localhost:9090
- Jaeger: http://localhost:16686

### 2. Staging Environment

```bash
# Build all services
npm run build

# Deploy to staging Kubernetes cluster
kubectl config use-context staging-cluster
helm install enterprise-platform ./infrastructure/helm-charts/enterprise-platform \
  --namespace staging \
  --create-namespace \
  --values ./infrastructure/helm-charts/values-staging.yaml

# Verify deployment
kubectl get pods -n staging
kubectl get services -n staging
kubectl get ingress -n staging

# Run integration tests against staging
npm run test:integration:staging

# Run E2E tests
npm run test:e2e:staging
```

### 3. Production Environment

```bash
# Deploy with GitOps (ArgoCD)
kubectl apply -f infrastructure/argocd/applications/

# Or manual deployment with strict validation
kubectl config use-context production-cluster

# Deploy with blue-green strategy
helm upgrade enterprise-platform ./infrastructure/helm-charts/enterprise-platform \
  --namespace production \
  --values ./infrastructure/helm-charts/values-production.yaml \
  --wait \
  --atomic

# Verify health checks
kubectl get pods -n production -w
curl https://api.enterprise-platform.com/health

# Run smoke tests
npm run test:smoke:production

# Monitor deployment
kubectl logs -f deployment/api-gateway -n production
```

## 🔧 Service Configuration

### Environment Variables by Service

#### API Gateway

```bash
# Core Configuration
NODE_ENV=production
PORT=3000
API_VERSION=v1

# Database
DATABASE_URL=************************************/db
REDIS_URL=redis://redis:6379

# Security
JWT_SECRET=your-jwt-secret
CORS_ORIGIN=https://app.enterprise-platform.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
PROMETHEUS_ENDPOINT=http://prometheus:9090
```

#### User Service

```bash
# Service Configuration
SERVICE_NAME=user-service
PORT=3000

# Database
DATABASE_URL=************************************/users_db
REDIS_URL=redis://redis:6379

# Message Broker
KAFKA_BROKERS=kafka:9092
KAFKA_GROUP_ID=user-service-group

# External Services
EMAIL_SERVICE_URL=https://api.sendgrid.com
STORAGE_SERVICE_URL=https://s3.amazonaws.com
```

#### AI Service

```bash
# Service Configuration
SERVICE_NAME=ai-service
PORT=3000

# AI/ML Configuration
VECTOR_DB_URL=http://qdrant:6333
OPENAI_API_KEY=your-openai-key
HUGGINGFACE_API_KEY=your-hf-key

# Model Configuration
MODEL_CACHE_SIZE=1GB
INFERENCE_BATCH_SIZE=32
MAX_SEQUENCE_LENGTH=512

# Performance
WORKERS=4
MAX_CONCURRENT_REQUESTS=100
```

### Service Discovery & Load Balancing

```yaml
# Kubernetes Service Mesh (Istio)
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: api-gateway
spec:
  hosts:
    - api.enterprise-platform.com
  gateways:
    - enterprise-gateway
  http:
    - match:
        - uri:
            prefix: /api/v1/users
      route:
        - destination:
            host: user-service
            port:
              number: 80
          weight: 100
      fault:
        delay:
          percentage:
            value: 0.1
          fixedDelay: 5s
      retries:
        attempts: 3
        perTryTimeout: 2s
    - match:
        - uri:
            prefix: /api/v1/ai
      route:
        - destination:
            host: ai-service
            port:
              number: 80
          weight: 100
```

## 📊 Monitoring & Observability Setup

### Prometheus Configuration

```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: "kubernetes-pods"
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)

  - job_name: "api-gateway"
    static_configs:
      - targets: ["api-gateway:3000"]
    metrics_path: /metrics
    scrape_interval: 5s

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093
```

### Grafana Dashboards

```json
{
  "dashboard": {
    "title": "Enterprise Platform Overview",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{service}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

### Application Logging

```typescript
// libs/common-utils/src/logger.ts
import winston from "winston";
import { ElasticsearchTransport } from "winston-elasticsearch";

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: process.env.SERVICE_NAME || "unknown",
    version: process.env.SERVICE_VERSION || "1.0.0",
  },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    new ElasticsearchTransport({
      level: "info",
      clientOpts: {
        node: process.env.ELASTICSEARCH_URL || "http://elasticsearch:9200",
      },
      index: "enterprise-platform-logs",
    }),
  ],
});

export default logger;
```

## 🔒 Security Configuration

### SSL/TLS Setup

```bash
# Generate development certificates
mkdir -p ssl
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes

# Production: Use Let's Encrypt with cert-manager
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.8.0/cert-manager.yaml

# Configure ClusterIssuer
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

### Security Policies

```yaml
# Kubernetes Network Policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
  namespace: production
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-api-gateway-to-services
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: user-service
  policyTypes:
    - Ingress
  ingress:
    - from:
        - podSelector:
            matchLabels:
              app: api-gateway
      ports:
        - protocol: TCP
          port: 3000
```

### Secrets Management

```bash
# Using Kubernetes Secrets
kubectl create secret generic api-secrets \
  --from-literal=jwt-secret=your-super-secret-key \
  --from-literal=database-password=secure-password \
  -n production

# Using HashiCorp Vault (recommended for production)
vault kv put secret/enterprise-platform/production \
  jwt_secret=your-super-secret-key \
  database_password=secure-password \
  openai_api_key=your-openai-key
```

## 🧪 Testing Strategy

### Test Execution

```bash
# Run all tests
npm run test

# Run tests by type
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:performance
npm run test:security

# Run tests for specific service
cd services/user-service
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in CI mode
npm run test:ci
```

### Performance Testing

```bash
# Load testing with k6
k6 run tests/performance/load-test.js

# Stress testing
k6 run tests/performance/stress-test.js

# Spike testing
k6 run tests/performance/spike-test.js
```

### Security Testing

```bash
# SAST with SonarQube
sonar-scanner

# DAST with OWASP ZAP
zap-baseline.py -t http://localhost:3000

# Dependency scanning
npm audit --audit-level high
snyk test
```

## 📈 Scaling Guidelines

### Horizontal Pod Autoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 50
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
```

### Database Scaling

```bash
# PostgreSQL Read Replicas
kubectl apply -f infrastructure/kubernetes/postgresql-replica.yaml

# Redis Cluster
kubectl apply -f infrastructure/kubernetes/redis-cluster.yaml

# MongoDB Sharding
kubectl apply -f infrastructure/kubernetes/mongodb-sharded.yaml
```

## 🚨 Troubleshooting Guide

### Common Issues

#### Service Won't Start

```bash
# Check logs
kubectl logs deployment/user-service -n production

# Check resource limits
kubectl describe pod user-service-xxx -n production

# Check configuration
kubectl get configmap user-service-config -o yaml
```

#### Database Connection Issues

```bash
# Test database connectivity
kubectl run -it --rm debug --image=postgres:13 --restart=Never -- psql -h postgres -U user -d enterprise_platform

# Check secrets
kubectl get secret database-secret -o yaml | base64 -d
```

#### High Memory Usage

```bash
# Check memory usage
kubectl top pods -n production

# Enable memory profiling
NODE_OPTIONS="--max-old-space-size=2048 --inspect=0.0.0.0:9229"

# Analyze heap dumps
npm install -g heapdump
```

#### Performance Issues

```bash
# Check metrics
curl http://prometheus:9090/api/v1/query?query=rate(http_requests_total[5m])

# Profile application
npm install -g clinic
clinic doctor -- node dist/main.js
```

### Emergency Procedures

#### Rollback Deployment

```bash
# Quick rollback
kubectl rollout undo deployment/user-service -n production

# Rollback to specific revision
kubectl rollout undo deployment/user-service --to-revision=2 -n production
```

#### Scale Down in Emergency

```bash
# Scale to minimum replicas
kubectl scale deployment/user-service --replicas=1 -n production

# Disable autoscaling temporarily
kubectl patch hpa user-service-hpa -p '{"spec":{"maxReplicas":1}}'
```

#### Circuit Breaker Manual Trigger

```bash
# Enable circuit breaker via API
curl -X POST http://api-gateway:3000/admin/circuit-breaker/enable \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{"service": "user-service", "duration": "5m"}'
```

This deployment guide provides comprehensive instructions for running the enterprise platform in any environment, from local development to production Kubernetes clusters.
