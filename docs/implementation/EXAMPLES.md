# 🛠️ Implementation Examples & Boilerplate

## 🎯 Core Implementation Patterns

### 1. Domain Entity Example (Clean Architecture + DDD)

```typescript
// libs/domain-models/src/user/User.ts
import { Entity } from "../core/Entity";
import { Email } from "./Email";
import { Password } from "./Password";
import { UserId } from "./UserId";
import { UserRegisteredEvent } from "./events/UserRegisteredEvent";

export interface UserProps {
  email: Email;
  password: Password;
  firstName: string;
  lastName: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class User extends Entity<UserProps> {
  private constructor(props: UserProps, id?: UserId) {
    super(props, id);
  }

  public static create(
    props: Omit<UserProps, "createdAt" | "updatedAt" | "isActive">
  ): User {
    const user = new User({
      ...props,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Domain Event
    user.addDomainEvent(new UserRegisteredEvent(user.id, props.email.value));

    return user;
  }

  public static fromPersistence(props: UserProps & { id: string }): User {
    return new User(props, new UserId(props.id));
  }

  public updateEmail(newEmail: Email): void {
    if (this.props.email.equals(newEmail)) {
      return;
    }

    this.props.email = newEmail;
    this.props.updatedAt = new Date();
  }

  public deactivate(): void {
    this.props.isActive = false;
    this.props.updatedAt = new Date();
  }

  public changePassword(newPassword: Password): void {
    this.props.password = newPassword;
    this.props.updatedAt = new Date();
  }

  // Getters
  get email(): Email {
    return this.props.email;
  }
  get firstName(): string {
    return this.props.firstName;
  }
  get lastName(): string {
    return this.props.lastName;
  }
  get fullName(): string {
    return `${this.props.firstName} ${this.props.lastName}`;
  }
  get isActive(): boolean {
    return this.props.isActive;
  }
  get createdAt(): Date {
    return this.props.createdAt;
  }
  get updatedAt(): Date {
    return this.props.updatedAt;
  }
}
```

### 2. Value Object Example (Email with Validation)

```typescript
// libs/domain-models/src/user/Email.ts
import { ValueObject } from "../core/ValueObject";

interface EmailProps {
  value: string;
}

export class Email extends ValueObject<EmailProps> {
  private static readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  private constructor(props: EmailProps) {
    super(props);
  }

  public static create(email: string): Email {
    if (!this.isValid(email)) {
      throw new Error(`Invalid email format: ${email}`);
    }

    return new Email({ value: email.toLowerCase().trim() });
  }

  public static isValid(email: string): boolean {
    return this.EMAIL_REGEX.test(email) && email.length <= 254;
  }

  get value(): string {
    return this.props.value;
  }

  get domain(): string {
    return this.props.value.split("@")[1];
  }

  get localPart(): string {
    return this.props.value.split("@")[0];
  }
}
```

### 3. Repository Interface & Implementation

```typescript
// services/user-service/src/domain/repositories/IUserRepository.ts
import { User } from "../entities/User";
import { UserId } from "../value-objects/UserId";
import { Email } from "../value-objects/Email";

export interface IUserRepository {
  save(user: User): Promise<void>;
  findById(id: UserId): Promise<User | null>;
  findByEmail(email: Email): Promise<User | null>;
  findAll(page: number, limit: number): Promise<User[]>;
  delete(id: UserId): Promise<void>;
  exists(email: Email): Promise<boolean>;
}

// services/user-service/src/infrastructure/database/repositories/SqlUserRepository.ts
import { IUserRepository } from "../../../domain/repositories/IUserRepository";
import { User } from "../../../domain/entities/User";
import { UserId } from "../../../domain/value-objects/UserId";
import { Email } from "../../../domain/value-objects/Email";
import { UserModel } from "../models/UserModel";
import { UserMapper } from "../mappers/UserMapper";

export class SqlUserRepository implements IUserRepository {
  constructor(private userModel: UserModel) {}

  async save(user: User): Promise<void> {
    const userData = UserMapper.toPersistence(user);

    if (await this.userModel.findById(user.id.value)) {
      await this.userModel.update(user.id.value, userData);
    } else {
      await this.userModel.create(userData);
    }
  }

  async findById(id: UserId): Promise<User | null> {
    const userData = await this.userModel.findById(id.value);
    return userData ? UserMapper.toDomain(userData) : null;
  }

  async findByEmail(email: Email): Promise<User | null> {
    const userData = await this.userModel.findByEmail(email.value);
    return userData ? UserMapper.toDomain(userData) : null;
  }

  async findAll(page: number, limit: number): Promise<User[]> {
    const usersData = await this.userModel.findPaginated(page, limit);
    return usersData.map(UserMapper.toDomain);
  }

  async delete(id: UserId): Promise<void> {
    await this.userModel.softDelete(id.value);
  }

  async exists(email: Email): Promise<boolean> {
    return await this.userModel.existsByEmail(email.value);
  }
}
```

### 4. Use Case Implementation (CQRS Pattern)

```typescript
// services/user-service/src/application/commands/CreateUserCommand.ts
import { ICommand } from "../core/ICommand";

export class CreateUserCommand implements ICommand {
  constructor(
    public readonly email: string,
    public readonly password: string,
    public readonly firstName: string,
    public readonly lastName: string
  ) {}
}

// services/user-service/src/application/commands/CreateUserCommandHandler.ts
import { ICommandHandler } from "../core/ICommandHandler";
import { CreateUserCommand } from "./CreateUserCommand";
import { IUserRepository } from "../../domain/repositories/IUserRepository";
import { User } from "../../domain/entities/User";
import { Email } from "../../domain/value-objects/Email";
import { Password } from "../../domain/value-objects/Password";
import { IEventPublisher } from "../interfaces/IEventPublisher";
import { IPasswordHasher } from "../interfaces/IPasswordHasher";

export class CreateUserCommandHandler
  implements ICommandHandler<CreateUserCommand, void>
{
  constructor(
    private userRepository: IUserRepository,
    private passwordHasher: IPasswordHasher,
    private eventPublisher: IEventPublisher
  ) {}

  async handle(command: CreateUserCommand): Promise<void> {
    // 1. Validate business rules
    const email = Email.create(command.email);

    if (await this.userRepository.exists(email)) {
      throw new Error("User with this email already exists");
    }

    // 2. Create domain entities
    const hashedPassword = await this.passwordHasher.hash(command.password);
    const password = Password.create(hashedPassword);

    const user = User.create({
      email,
      password,
      firstName: command.firstName,
      lastName: command.lastName,
    });

    // 3. Persist
    await this.userRepository.save(user);

    // 4. Publish domain events
    for (const event of user.getDomainEvents()) {
      await this.eventPublisher.publish(event);
    }

    user.clearDomainEvents();
  }
}
```

### 5. API Controller (NestJS Implementation)

```typescript
// services/user-service/src/interface/http/controllers/UserController.ts
import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { CreateUserDto } from "../../application/dtos/requests/CreateUserDto";
import { UpdateUserDto } from "../../application/dtos/requests/UpdateUserDto";
import { UserResponseDto } from "../../application/dtos/responses/UserResponseDto";
import { CreateUserCommand } from "../../application/commands/CreateUserCommand";
import { UpdateUserCommand } from "../../application/commands/UpdateUserCommand";
import { GetUserQuery } from "../../application/queries/GetUserQuery";
import { ListUsersQuery } from "../../application/queries/ListUsersQuery";
import { CommandBus, QueryBus } from "@nestjs/cqrs";
import { JwtAuthGuard } from "../middleware/JwtAuthGuard";
import { RolesGuard } from "../middleware/RolesGuard";
import { Roles } from "../decorators/roles.decorator";

@ApiTags("Users")
@Controller("users")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new user" })
  @ApiResponse({ status: 201, description: "User created successfully" })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  @ApiResponse({ status: 409, description: "User already exists" })
  async createUser(@Body() createUserDto: CreateUserDto): Promise<void> {
    const command = new CreateUserCommand(
      createUserDto.email,
      createUserDto.password,
      createUserDto.firstName,
      createUserDto.lastName
    );

    await this.commandBus.execute(command);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get user by ID" })
  @ApiResponse({
    status: 200,
    description: "User found",
    type: UserResponseDto,
  })
  @ApiResponse({ status: 404, description: "User not found" })
  async getUserById(@Param("id") id: string): Promise<UserResponseDto> {
    const query = new GetUserQuery(id);
    return await this.queryBus.execute(query);
  }

  @Get()
  @ApiOperation({ summary: "List users with pagination" })
  @ApiResponse({ status: 200, description: "Users retrieved successfully" })
  async getUsers(
    @Query("page") page: number = 1,
    @Query("limit") limit: number = 10
  ): Promise<UserResponseDto[]> {
    const query = new ListUsersQuery(page, limit);
    return await this.queryBus.execute(query);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update user" })
  @ApiResponse({ status: 200, description: "User updated successfully" })
  @ApiResponse({ status: 404, description: "User not found" })
  async updateUser(
    @Param("id") id: string,
    @Body() updateUserDto: UpdateUserDto
  ): Promise<void> {
    const command = new UpdateUserCommand(
      id,
      updateUserDto.firstName,
      updateUserDto.lastName
    );

    await this.commandBus.execute(command);
  }

  @Delete(":id")
  @UseGuards(RolesGuard)
  @Roles("admin")
  @ApiOperation({ summary: "Delete user (Admin only)" })
  @ApiResponse({ status: 204, description: "User deleted successfully" })
  @ApiResponse({ status: 403, description: "Insufficient permissions" })
  @ApiResponse({ status: 404, description: "User not found" })
  async deleteUser(@Param("id") id: string): Promise<void> {
    const command = new DeleteUserCommand(id);
    await this.commandBus.execute(command);
  }
}
```

### 6. Testing Implementation

```typescript
// services/user-service/src/tests/unit/domain/entities/User.test.ts
import { User } from "../../../../src/domain/entities/User";
import { Email } from "../../../../src/domain/value-objects/Email";
import { Password } from "../../../../src/domain/value-objects/Password";
import { UserRegisteredEvent } from "../../../../src/domain/events/UserRegisteredEvent";

describe("User Entity", () => {
  const validProps = {
    email: Email.create("<EMAIL>"),
    password: Password.create("hashedPassword123"),
    firstName: "John",
    lastName: "Doe",
  };

  describe("create", () => {
    it("should create a new user with valid properties", () => {
      // Act
      const user = User.create(validProps);

      // Assert
      expect(user.email.value).toBe("<EMAIL>");
      expect(user.firstName).toBe("John");
      expect(user.lastName).toBe("Doe");
      expect(user.fullName).toBe("John Doe");
      expect(user.isActive).toBe(true);
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();
    });

    it("should add UserRegisteredEvent when user is created", () => {
      // Act
      const user = User.create(validProps);

      // Assert
      const domainEvents = user.getDomainEvents();
      expect(domainEvents).toHaveLength(1);
      expect(domainEvents[0]).toBeInstanceOf(UserRegisteredEvent);
    });
  });

  describe("updateEmail", () => {
    it("should update email and updatedAt timestamp", () => {
      // Arrange
      const user = User.create(validProps);
      const originalUpdatedAt = user.updatedAt;
      const newEmail = Email.create("<EMAIL>");

      // Wait to ensure timestamp difference
      setTimeout(() => {
        // Act
        user.updateEmail(newEmail);

        // Assert
        expect(user.email.value).toBe("<EMAIL>");
        expect(user.updatedAt.getTime()).toBeGreaterThan(
          originalUpdatedAt.getTime()
        );
      }, 1);
    });

    it("should not update if email is the same", () => {
      // Arrange
      const user = User.create(validProps);
      const originalUpdatedAt = user.updatedAt;

      // Act
      user.updateEmail(validProps.email);

      // Assert
      expect(user.updatedAt).toBe(originalUpdatedAt);
    });
  });

  describe("deactivate", () => {
    it("should deactivate user and update timestamp", () => {
      // Arrange
      const user = User.create(validProps);
      const originalUpdatedAt = user.updatedAt;

      setTimeout(() => {
        // Act
        user.deactivate();

        // Assert
        expect(user.isActive).toBe(false);
        expect(user.updatedAt.getTime()).toBeGreaterThan(
          originalUpdatedAt.getTime()
        );
      }, 1);
    });
  });
});

// Integration Test Example
// services/user-service/src/tests/integration/api/UserController.integration.test.ts
import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { JwtModule } from "@nestjs/jwt";
import request from "supertest";
import { UserModule } from "../../../src/modules/UserModule";
import { DatabaseTestModule } from "../../fixtures/DatabaseTestModule";
import { JwtTestHelper } from "../../fixtures/JwtTestHelper";

describe("UserController (Integration)", () => {
  let app: INestApplication;
  let jwtHelper: JwtTestHelper;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        DatabaseTestModule,
        UserModule,
        JwtModule.register({
          secret: "test-secret",
          signOptions: { expiresIn: "1h" },
        }),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtHelper = new JwtTestHelper(moduleFixture);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe("POST /users", () => {
    it("should create a new user", async () => {
      // Arrange
      const createUserDto = {
        email: "<EMAIL>",
        password: "SecurePassword123!",
        firstName: "John",
        lastName: "Doe",
      };

      // Act & Assert
      await request(app.getHttpServer())
        .post("/users")
        .send(createUserDto)
        .expect(201);
    });

    it("should return 400 for invalid email", async () => {
      // Arrange
      const createUserDto = {
        email: "invalid-email",
        password: "SecurePassword123!",
        firstName: "John",
        lastName: "Doe",
      };

      // Act & Assert
      await request(app.getHttpServer())
        .post("/users")
        .send(createUserDto)
        .expect(400);
    });

    it("should return 409 for duplicate email", async () => {
      // Arrange
      const createUserDto = {
        email: "<EMAIL>",
        password: "SecurePassword123!",
        firstName: "John",
        lastName: "Doe",
      };

      // Create user first time
      await request(app.getHttpServer())
        .post("/users")
        .send(createUserDto)
        .expect(201);

      // Act & Assert - Try to create again
      await request(app.getHttpServer())
        .post("/users")
        .send(createUserDto)
        .expect(409);
    });
  });

  describe("GET /users/:id", () => {
    it("should return user by ID", async () => {
      // Arrange
      const token = await jwtHelper.generateValidToken();
      const userId = "existing-user-id";

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty("id", userId);
      expect(response.body).toHaveProperty("email");
      expect(response.body).toHaveProperty("firstName");
      expect(response.body).toHaveProperty("lastName");
    });

    it("should return 401 without valid token", async () => {
      // Act & Assert
      await request(app.getHttpServer()).get("/users/some-id").expect(401);
    });

    it("should return 404 for non-existent user", async () => {
      // Arrange
      const token = await jwtHelper.generateValidToken();
      const nonExistentId = "non-existent-id";

      // Act & Assert
      await request(app.getHttpServer())
        .get(`/users/${nonExistentId}`)
        .set("Authorization", `Bearer ${token}`)
        .expect(404);
    });
  });
});
```

### 7. Docker & Kubernetes Configuration

```yaml
# services/user-service/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine as production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

USER nestjs

EXPOSE 3000

CMD ["node", "dist/main.js"]
```

```yaml
# infrastructure/kubernetes/user-service/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: enterprise-platform
  labels:
    app: user-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
      version: v1
  template:
    metadata:
      labels:
        app: user-service
        version: v1
    spec:
      serviceAccountName: user-service
      containers:
        - name: user-service
          image: enterprise-platform/user-service:latest
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: NODE_ENV
              value: "production"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: user-service-secrets
                  key: database-url
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: user-service-secrets
                  key: jwt-secret
            - name: REDIS_URL
              valueFrom:
                configMapKeyRef:
                  name: user-service-config
                  key: redis-url
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health/ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1001
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
      imagePullSecrets:
        - name: registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: enterprise-platform
  labels:
    app: user-service
spec:
  selector:
    app: user-service
  ports:
    - port: 80
      targetPort: 3000
      protocol: TCP
      name: http
  type: ClusterIP
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: user-service
  namespace: enterprise-platform
spec:
  hosts:
    - user-service
  http:
    - match:
        - headers:
            x-api-version:
              exact: v1
      route:
        - destination:
            host: user-service
            subset: v1
          weight: 100
      fault:
        delay:
          percentage:
            value: 0.1
          fixedDelay: 5s
      retries:
        attempts: 3
        perTryTimeout: 2s
```

### 8. CI/CD Pipeline

```yaml
# .github/workflows/user-service-ci.yml
name: User Service CI/CD

on:
  push:
    branches: [main, develop]
    paths: ["services/user-service/**"]
  pull_request:
    branches: [main]
    paths: ["services/user-service/**"]

env:
  SERVICE_NAME: user-service
  REGISTRY: ghcr.io
  IMAGE_NAME: enterprise-platform/user-service

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: user_service_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:6-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: services/user-service/package-lock.json

      - name: Install dependencies
        working-directory: services/user-service
        run: npm ci

      - name: Run linting
        working-directory: services/user-service
        run: npm run lint

      - name: Run type checking
        working-directory: services/user-service
        run: npm run type-check

      - name: Run unit tests
        working-directory: services/user-service
        run: npm run test:unit

      - name: Run integration tests
        working-directory: services/user-service
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/user_service_test
          REDIS_URL: redis://localhost:6379

      - name: Run security audit
        working-directory: services/user-service
        run: npm audit --audit-level high

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: services/user-service/coverage/lcov.info
          flags: user-service

  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run SAST with CodeQL
        uses: github/codeql-action/analyze@v2
        with:
          languages: typescript

      - name: Run dependency vulnerability scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --file=services/user-service/package.json

  build-and-push:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: services/user-service
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: Deploy to staging
        uses: azure/k8s-deploy@v1
        with:
          manifests: |
            infrastructure/kubernetes/user-service/
          images: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

      - name: Run E2E tests
        run: |
          # Run E2E tests against staging environment
          npm run test:e2e:staging

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Deploy to production
        uses: azure/k8s-deploy@v1
        with:
          manifests: |
            infrastructure/kubernetes/user-service/
          images: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

      - name: Run smoke tests
        run: |
          # Run critical smoke tests in production
          npm run test:smoke:production
```

This implementation demonstrates enterprise-grade patterns including Clean Architecture, SOLID principles, comprehensive testing, security-by-design, and complete CI/CD pipeline with multiple environments.
