Tuyệt vời! Dưới đây là phiên bản "Flashcard học nhanh" để bạn dễ dàng ôn luyện các thông tin quan trọng từ cuốn sách "Dive Into Design Patterns":

---

### Flashcard học nhanh: Dive Into Design Patterns

#### Chương 1: <PERSON><PERSON> bản về OOP

- **Mặt trước:** OOP là gì và mối quan hệ với lớp và đối tượng?
- **Mặt sau:** **Lập trình hướng đối tượng (OOP)** tổ chức dữ liệu và hành vi vào các **đối tượng**, đư<PERSON>c tạo ra từ các **lớp** (bản thiết kế) do lập trình viên định nghĩa. Một **đối tượng** là một thể hiện của một **lớp**, chia sẻ các thu<PERSON><PERSON> (trường) và hành vi (ph<PERSON><PERSON><PERSON> thức) chung.

- **Mặt trước:** <PERSON><PERSON> thống phân cấp lớp và Kế thừa là gì?
- **Mặt sau:** **Hệ thống phân cấp lớp** tổ chức các lớp theo mối quan hệ cha-con. **Lớp con** kế thừa các thuộc tính và hành vi từ **lớp cha**. Lớp con có thể **ghi đè (override)** các phương thức kế thừa để thay thế hoặc bổ sung chức năng.

#### Chương 2: Các trụ cột của OOP

- **Mặt trước:** Kể tên 4 trụ cột của OOP.
- **Mặt sau:** Bốn khái niệm nền tảng của OOP là **Abstraction (Trừu tượng hóa)**, **Encapsulation (Đóng gói)**, **Inheritance (Kế thừa)** và **Polymorphism (Đa hình)**.

- **Mặt trước:** Abstraction là gì?
- **Mặt sau:** **Abstraction** là mô hình hóa các đối tượng thực tế bằng cách chỉ tập trung vào các thuộc tính và hành vi **liên quan** đến một ngữ cảnh cụ thể, bỏ qua các chi tiết không cần thiết.

- **Mặt trước:** Encapsulation là gì?
- **Mặt sau:** **Encapsulation** là việc che giấu các hoạt động bên trong của một đối tượng và chỉ để lộ một **giao diện đơn giản** để tương tác, đảm bảo tương tác trực tiếp.

- **Mặt trước:** Polymorphism là gì?
- **Mặt sau:** **Polymorphism** cho phép chương trình xác định lớp cụ thể của một đối tượng và gọi các phương thức của nó mà không cần biết chính xác kiểu của nó trước, cho phép hành vi đối tượng linh hoạt.

#### Chương 3: Các mối quan hệ giữa các đối tượng

- **Mặt trước:** Association, Dependency, Composition, Aggregation trong UML là gì?
- **Mặt sau:**
  - **Association:** Một đối tượng sử dụng hoặc tương tác với đối tượng khác.
  - **Dependency:** Dạng yếu hơn của association, không có liên kết vĩnh viễn, thường khi một đối tượng sử dụng đối tượng khác làm tham số.
  - **Composition:** Mối quan hệ "toàn thể-bộ phận" mạnh mẽ, thành phần không thể tồn tại độc lập khỏi đối tượng chứa.
  - **Aggregation:** Dạng lỏng lẻo hơn của composition, đối tượng giữ tham chiếu nhưng không kiểm soát vòng đời, thành phần có thể tồn tại độc lập.

#### Chương 4: Mẫu thiết kế là gì?

- **Mặt trước:** Design Pattern là gì và khác gì Algorithm?
- **Mặt sau:** **Design Patterns** là các giải pháp điển hình, có thể tùy chỉnh cho các vấn đề thiết kế phần mềm lặp đi lặp lại. Khác với **Algorithm** cung cấp hành động chính xác, pattern là mô tả cấp cao hơn của giải pháp.

- **Mặt trước:** Ai đã phổ biến Design Patterns trong lập trình?
- **Mặt sau:** Khái niệm này ban đầu từ **Christopher Alexander** (kiến trúc), được phổ biến trong lập trình bởi "Gang of Four" (**Erich Gamma, John Vlissides, Ralph Johnson, và Richard Helm**) qua cuốn sách "Design Patterns: Elements of Reusable Object-Oriented Software".

#### Chương 5: Tại sao nên học mẫu thiết kế?

- **Mặt trước:** Lợi ích của việc học Design Patterns?
- **Mặt sau:**
  - **Toolkit of Solutions:** Cung cấp các giải pháp đã được kiểm nghiệm cho các vấn đề thiết kế.
  - **Common Language:** Thiết lập một vốn từ vựng chung giữa các thành viên trong nhóm, cải thiện giao tiếp.

#### Chương 6: Đặc điểm của thiết kế tốt

- **Mặt trước:** Code Reuse và Extensibility là gì và tại sao quan trọng?
- **Mặt sau:**
  - **Code Reuse (Tái sử dụng mã):** Chiến lược quan trọng để giảm chi phí phát triển và tăng tốc thời gian đưa sản phẩm ra thị trường.
  - **Extensibility (Khả năng mở rộng):** Quan trọng vì thay đổi là điều không thể tránh khỏi trong lập trình; giúp ứng dụng thích nghi với yêu cầu và xu hướng mới.

#### Chương 7: Đóng gói những gì thay đổi

- **Mặt trước:** Nguyên tắc "Encapsulate What Varies" là gì?
- **Mặt sau:** Nguyên tắc này khuyên bạn nên xác định và **tách biệt các phần của ứng dụng có thể thay đổi** khỏi các phần cố định để giảm thiểu tác động của những thay đổi đó.

#### Chương 8: Lập trình theo giao diện, không phải triển khai

- **Mặt trước:** Nguyên tắc "Program to an Interface, not an Implementation" là gì?
- **Mặt sau:** Nhấn mạnh lập trình dựa trên một **giao diện (abstraction)** thay vì một **triển khai cụ thể**, nhằm đạt được sự linh hoạt và khả năng mở rộng trong thiết kế.

- **Mặt trước:** Lợi ích của lập trình theo giao diện?
- **Mặt sau:** Giúp thiết kế linh hoạt, cho phép **mở rộng mà không làm hỏng mã hiện có**. Nó giảm sự ghép nối chặt chẽ và cho phép sử dụng **đa hình**.

#### Chương 9: Ưu tiên Composition hơn Inheritance

- **Mặt trước:** Tại sao nên "Favor Composition Over Inheritance"?
- **Mặt sau:** Kế thừa có thể dẫn đến **hạn chế giao diện, phá vỡ đóng gói, ghép nối chặt chẽ, và hệ thống phân cấp phức tạp**. **Composition** (tổng hợp) ưu tiên mối quan hệ "có một" ("has a") thay vì "là một" ("is a"), cho phép linh hoạt hơn và tránh các nhược điểm của kế thừa.

#### Chương 10: Nguyên tắc Đơn trách nhiệm (SRP)

- **Mặt trước:** Nguyên tắc SRP là gì?
- **Mặt sau:** Một lớp chỉ nên có **một lý do để thay đổi**, tập trung vào một phần chức năng duy nhất. Mục tiêu chính là **giảm độ phức tạp** và tăng khả năng bảo trì.

#### Chương 11: Nguyên tắc Đóng/Mở (OCP)

- **Mặt trước:** Nguyên tắc OCP là gì?
- **Mặt sau:** Các lớp nên **mở để mở rộng nhưng đóng để sửa đổi**. Điều này ngăn chặn mã hiện có bị hỏng khi thêm tính năng mới. Bạn nên tạo lớp con để mở rộng thay vì sửa trực tiếp lớp gốc.

#### Chương 12: Nguyên tắc Thay thế Liskov (LSP)

- **Mặt trước:** Nguyên tắc LSP là gì?
- **Mặt sau:** Các đối tượng của một lớp con nên có thể **thay thế cho các đối tượng của lớp cha** mà không làm thay đổi tính đúng đắn của chương trình. Nguyên tắc này có các hướng dẫn về **tương thích kiểu tham số/kiểu trả về**, xử lý ngoại lệ, và bảo toàn tiền/hậu điều kiện, bất biến.

#### Chương 13: Nguyên tắc Phân tách Giao diện (ISP)

- **Mặt trước:** Nguyên tắc ISP là gì?
- **Mặt sau:** Khách hàng không nên bị buộc phải phụ thuộc vào các phương thức mà họ không sử dụng. Nên tạo các **giao diện hẹp** và cụ thể để các lớp khách hàng chỉ triển khai các hành vi thực sự cần.

#### Chương 14: Nguyên tắc Đảo ngược Phụ thuộc (DIP)

- **Mặt trước:** Nguyên tắc DIP là gì?
- **Mặt sau:** Các lớp cấp cao không nên phụ thuộc vào các lớp cấp thấp. **Cả hai nên phụ thuộc vào các abstraction (giao diện)**. Điều này đảo ngược hướng phụ thuộc truyền thống, làm cho thiết kế linh hoạt và dễ bảo trì hơn.

---

### Flashcard học nhanh: Các mẫu thiết kế Creational

#### Chương 15: Factory Method

- **Mặt trước:** Factory Method là gì?
- **Mặt sau:** Một **creational design pattern** cung cấp một giao diện để tạo đối tượng trong một lớp cha, cho phép các lớp con quyết định loại đối tượng cụ thể sẽ được tạo.

- **Mặt trước:** Vấn đề Factory Method giải quyết?
- **Mặt sau:** Giảm **ghép nối chặt chẽ** giữa mã và các lớp sản phẩm cụ thể, làm cho việc thêm các loại đối tượng mới trở nên dễ dàng hơn.

#### Chương 16: Abstract Factory

- **Mặt trước:** Abstract Factory là gì?
- **Mặt sau:** Một **creational design pattern** cho phép tạo ra các **họ đối tượng liên quan** mà không cần chỉ rõ các lớp cụ thể của chúng.

- **Mặt trước:** Lợi ích của Abstract Factory?
- **Mặt sau:** Đảm bảo **khả năng tương thích sản phẩm**, giảm ghép nối chặt chẽ giữa sản phẩm và mã khách hàng, và hỗ trợ tuân thủ SRP, OCP.

#### Chương 17: Builder

- **Mặt trước:** Builder là gì và vấn đề giải quyết?
- **Mặt sau:** Một **creational design pattern** cho phép **xây dựng từng bước các đối tượng phức tạp**. Nó giải quyết các vấn đề liên quan đến hàm tạo phức tạp với nhiều tham số tùy chọn hoặc sự cần thiết của nhiều lớp con cho mỗi cấu hình.

- **Mặt trước:** Vai trò của Director trong Builder?
- **Mặt sau:** Lớp **Director** tổ chức chuỗi các bước xây dựng, đơn giản hóa tương tác của khách hàng với builder.

#### Chương 18: Prototype

- **Mặt trước:** Prototype là gì và vấn đề giải quyết?
- **Mặt sau:** Một **creational design pattern** cho phép **sao chép các đối tượng hiện có** mà không cần mã phụ thuộc vào lớp của chúng. Nó giải quyết khó khăn khi sao chép đối tượng "từ bên ngoài" do các trường riêng tư và sự phụ thuộc vào các lớp cụ thể.

- **Mặt trước:** Giải pháp của Prototype?
- **Mặt sau:** Ủy quyền quá trình sao chép cho chính các đối tượng thông qua một **giao diện chung** (thường có phương thức `clone`).

#### Chương 19: Singleton

- **Mặt trước:** Singleton là gì và vấn đề giải quyết?
- **Mặt sau:** Một **creational design pattern** đảm bảo một lớp chỉ có **một thể hiện duy nhất** và cung cấp một **điểm truy cập toàn cầu** đến thể hiện đó. Thường dùng để kiểm soát quyền truy cập vào tài nguyên dùng chung (như cơ sở dữ liệu). _Lưu ý: Vi phạm Nguyên tắc Đơn trách nhiệm_.

- **Mặt trước:** Cách triển khai Singleton?
- **Mặt sau:**
  - Hàm tạo riêng tư.
  - Phương thức tạo tĩnh (`getInstance()`) để khởi tạo và trả về thể hiện duy nhất (lazy initialization).

---

### Flashcard học nhanh: Các mẫu thiết kế Structural

#### Chương 20: Adapter

- **Mặt trước:** Adapter là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** cho phép **hợp tác giữa các đối tượng có giao diện không tương thích**. Nó giải quyết vấn đề sử dụng thư viện bên thứ ba với giao diện không tương thích mà không cần sửa đổi mã hiện có.

- **Mặt trước:** Giải pháp của Adapter?
- **Mặt sau:** Tạo một **adapter** đóng vai trò trung gian, chuyển đổi dữ liệu giữa các định dạng/giao diện không tương thích.

#### Chương 21: Bridge

- **Mặt trước:** Bridge là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** tách một lớp lớn thành hai hệ thống phân cấp riêng biệt: **abstraction (trừu tượng)** và **implementation (triển khai)**, cho phép phát triển độc lập. Nó giải quyết "bùng nổ lớp" khi mở rộng hệ thống phân cấp theo nhiều chiều.

- **Mặt trước:** Giải pháp của Bridge?
- **Mặt sau:** Chuyển từ kế thừa sang **composition**. Một lớp trừu tượng tham chiếu đến một đối tượng triển khai, cho phép cả hai phát triển độc lập.

#### Chương 22: Composite

- **Mặt trước:** Composite là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** cho phép **tổng hợp các đối tượng thành cấu trúc cây**, cho phép chúng được xử lý như các đối tượng cá thể hoặc nhóm một cách thống nhất. Nó hữu ích khi mô hình cốt lõi có cấu trúc cây (ví dụ: đơn hàng lồng sản phẩm và hộp).

- **Mặt trước:** Giải pháp của Composite?
- **Mặt sau:** Cung cấp một **giao diện chung (Component Interface)** cho cả phần tử đơn giản (Leaf) và phần tử phức tạp (Container/Composite), cho phép xử lý đệ quy và thống nhất.

#### Chương 23: Decorator

- **Mặt trước:** Decorator là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** cho phép **thêm các hành vi mới vào đối tượng** bằng cách bọc chúng trong các đối tượng đặc biệt (decorators). Nó giải quyết "bùng nổ số lượng lớp con" khi cần nhiều tổ hợp hành vi.

- **Mặt trước:** Giải pháp của Decorator?
- **Mặt sau:** Sử dụng **composition** thay vì kế thừa. Các decorator bọc đối tượng gốc, thêm chức năng động mà không làm thay đổi lớp gốc.

#### Chương 24: Facade

- **Mặt trước:** Facade là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** giúp **đơn giản hóa tương tác với các thư viện hoặc framework phức tạp** bằng cách cung cấp một giao diện rõ ràng. Nó giải quyết sự phức tạp khi phải quản lý nhiều đối tượng và sự phụ thuộc trong một hệ thống con.

- **Mặt trước:** Giải pháp của Facade?
- **Mặt sau:** Một lớp **facade** cung cấp một giao diện đơn giản hóa cho hệ thống con phức tạp, chỉ hiển thị các tính năng cần thiết cho khách hàng.

#### Chương 25: Flyweight

- **Mặt trước:** Flyweight là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** giúp **tối ưu hóa việc sử dụng bộ nhớ** bằng cách chia sẻ các phần chung của trạng thái giữa nhiều đối tượng. Nó giải quyết vấn đề tiêu thụ RAM cao do tạo quá nhiều đối tượng tương tự (ví dụ: hạt trong game).

- **Mặt trước:** Trạng thái Intrinsic và Extrinsic trong Flyweight?
- **Mặt sau:** **Intrinsic state (nội tại)** là dữ liệu chung, không đổi, nằm trong đối tượng. **Extrinsic state (bên ngoài)** là dữ liệu duy nhất, thay đổi theo ngữ cảnh, được truyền vào phương thức.

#### Chương 26: Proxy

- **Mặt trước:** Proxy là gì và vấn đề giải quyết?
- **Mặt sau:** Một **structural design pattern** đóng vai trò là **thế thân (substitute)** cho một đối tượng khác, kiểm soát quyền truy cập và cho phép tiền/hậu xử lý yêu cầu. Nó giải quyết vấn đề kiểm soát quyền truy cập vào đối tượng tiêu tốn tài nguyên, thực hiện khởi tạo lười (lazy initialization).

- **Mặt trước:** Giải pháp của Proxy?
- **Mặt sau:** Tạo một **lớp proxy mới chia sẻ cùng giao diện** với đối tượng dịch vụ gốc. Proxy ủy quyền yêu cầu, thêm chức năng mà khách hàng không biết.

---

### Flashcard học nhanh: Các mẫu thiết kế Behavioral

#### Chương 27: Chain of Responsibility (CoR)

- **Mặt trước:** Chain of Responsibility là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** cho phép **chuyển các yêu cầu qua một chuỗi các bộ xử lý (handlers)**. Mỗi handler có thể xử lý hoặc chuyển tiếp yêu cầu. Nó giải quyết vấn đề mã trở nên phức tạp và khó bảo trì khi nhiều kiểm tra phải thực hiện tuần tự.

- **Mặt trước:** Giải pháp của CoR?
- **Mặt sau:** Đóng gói mỗi hành vi vào các đối tượng **handler** độc lập, liên kết chúng trong một chuỗi.

#### Chương 28: Command

- **Mặt trước:** Command là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** chuyển đổi một yêu cầu thành một **đối tượng độc lập**, chứa tất cả thông tin cần thiết. Nó cho phép tham số hóa phương thức, trì hoãn/xếp hàng yêu cầu, và hỗ trợ thao tác hoàn tác. Nó giải quyết vấn đề GUI code bị **ghép nối chặt chẽ** với logic nghiệp vụ và trùng lặp mã.

- **Mặt trước:** Giải pháp của Command?
- **Mặt sau:** Tách biệt lớp GUI và logic nghiệp vụ. Đóng gói chi tiết yêu cầu vào các **lớp command riêng biệt**, mỗi command triển khai một giao diện thực thi.

#### Chương 29: Iterator

- **Mặt trước:** Iterator là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** cho phép **duyệt qua các phần tử của một collection** mà không để lộ cấu trúc bên dưới của nó. Nó giải quyết vấn đề khó khăn khi truy cập các phần tử trong cấu trúc dữ liệu phức tạp mà không làm lộn xộn chức năng chính của collection.

- **Mặt trước:** Giải pháp của Iterator?
- **Mặt sau:** Trích xuất hành vi duyệt vào một **đối tượng iterator riêng biệt**. Iterator này đóng gói chi tiết duyệt, cho phép nhiều iterator duyệt cùng một collection độc lập.

#### Chương 30: Mediator

- **Mặt trước:** Mediator là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** giúp **đơn giản hóa giao tiếp giữa các đối tượng** bằng cách hạn chế tương tác trực tiếp và thực thi sự hợp tác thông qua một đối tượng **mediator**. Nó giải quyết vấn đề các **phụ thuộc phức tạp** giữa các phần tử giao diện người dùng, làm cho các lớp khó tái sử dụng.

- **Mặt trước:** Giải pháp của Mediator?
- **Mặt sau:** Thúc đẩy giao tiếp gián tiếp giữa các thành phần thông qua một **mediator**, giảm sự phụ thuộc giữa chúng, tăng khả năng tái sử dụng.

#### Chương 31: Memento

- **Mặt trước:** Memento là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** cho phép **lưu và khôi phục trạng thái trước đó của một đối tượng** mà không để lộ chi tiết triển khai của nó. Nó giải quyết thách thức trong việc ghi lại trạng thái của một đối tượng do các hạn chế đóng gói.

- **Mặt trước:** Giải pháp của Memento?
- **Mặt sau:** Cho phép chính đối tượng (**originator**) tạo các snapshot của riêng nó, được lưu trữ trong một **memento**. Chỉ originator mới có thể sửa đổi memento.

#### Chương 32: Observer

- **Mặt trước:** Observer là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** cho phép một **cơ chế đăng ký** để thông báo cho nhiều đối tượng về các sự kiện xảy ra trong đối tượng mà chúng đang quan sát (publisher). Nó giải quyết vấn đề cần thông báo cho nhiều đối tượng về sự thay đổi trạng thái mà không cần biết trước người nhận cụ thể.

- **Mặt trước:** Giải pháp của Observer?
- **Mặt sau:** **Publisher** duy trì danh sách các **Subscribers**. Subscribers có thể tham gia hoặc rời khỏi danh sách. Publisher thông báo cho tất cả subscribers đã đăng ký về các sự kiện.

#### Chương 33: State

- **Mặt trước:** State là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** cho phép một đối tượng **thay đổi hành vi của nó dựa trên trạng thái nội bộ**, khiến nó dường như đã thay đổi lớp. Nó giải quyết vấn đề triển khai máy trạng thái bằng các toán tử điều kiện (if/switch) dẫn đến mã phức tạp, khó bảo trì khi số lượng trạng thái tăng.

- **Mặt trước:** Giải pháp của State?
- **Mặt sau:** Tạo **các lớp riêng biệt cho mỗi trạng thái có thể có** của một đối tượng, đóng gói các hành vi cụ thể của trạng thái. Đối tượng chính (context) giữ tham chiếu đến đối tượng trạng thái hiện tại và ủy quyền các tác vụ.

#### Chương 34: Strategy

- **Mặt trước:** Strategy là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** định nghĩa một **họ các thuật toán**, đóng gói mỗi thuật toán trong một lớp riêng biệt và làm cho chúng **có thể hoán đổi cho nhau**. Nó giải quyết vấn đề phức tạp và bảo trì khi một lớp triển khai nhiều thuật toán trực tiếp.

- **Mặt trước:** Giải pháp của Strategy?
- **Mặt sau:** Trích xuất các thuật toán vào các **lớp riêng biệt** gọi là **strategies**. Lớp chính (context) ủy quyền công việc cho các đối tượng strategy này, giữ context độc lập với các strategy cụ thể.

#### Chương 35: Template Method

- **Mặt trước:** Template Method là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** phác thảo **khung xương của một thuật toán trong một lớp cha**, cho phép các lớp con ghi đè các bước cụ thể mà không làm thay đổi cấu trúc tổng thể. Nó giải quyết vấn đề mã trùng lặp giữa các lớp xử lý định dạng tài liệu khác nhau.

- **Mặt trước:** Giải pháp của Template Method?
- **Mặt sau:** Phân tách thuật toán thành các **bước riêng biệt** được đóng gói trong các phương thức. Một **phương thức template** điều phối các lệnh gọi này, trong khi các lớp con triển khai các bước trừu tượng hoặc ghi đè các triển khai mặc định.

#### Chương 36: Visitor

- **Mặt trước:** Visitor là gì và vấn đề giải quyết?
- **Mặt sau:** Một **behavioral design pattern** cho phép **tách các thuật toán khỏi các đối tượng mà chúng hoạt động trên**, nâng cao tính mô đun và linh hoạt của mã. Nó giải quyết vấn đề phải sửa đổi các lớp hiện có để thêm các phương thức mới khi có yêu cầu mới.

- **Mặt trước:** Giải pháp của Visitor?
- **Mặt sau:** Tạo một **lớp visitor riêng biệt** để đóng gói hành vi mới, cho phép thêm chức năng mà không làm thay đổi các lớp đối tượng hiện có. Mẫu này sử dụng kỹ thuật **Double Dispatch**.
