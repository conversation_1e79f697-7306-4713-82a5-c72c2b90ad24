Dựa trên bản tóm tắt bạn cung cấp, đ<PERSON><PERSON> là tổng hợp các kh<PERSON>i niệm chính đư<PERSON>c tổ chức theo chư<PERSON>, gi<PERSON><PERSON> bạn dễ dàng nắm bắt thông tin và các khái niệm quan trọng:

### Tổng Quan Về "Dive Into Design Patterns" c<PERSON><PERSON>hvet<PERSON> sách "Dive Into Design Patterns" c<PERSON><PERSON> <PERSON>hvets là một hướng dẫn thiết yếu để nắm vững các nguyên tắc thiết kế phần mềm, biến các lý thuyết trừu tượng thành những hiểu biết thực tế, có thể hành động được. <PERSON><PERSON><PERSON> gi<PERSON> Shvets là một kỹ sư phần mềm giàu kinh nghiệm, nổi tiếng với khả năng đơn giản hóa các khái niệm thiết kế phức tạp. <PERSON><PERSON><PERSON><PERSON> sách này trang bị cho độc giả các công cụ để tạo ra mã sạch, dễ bảo trì và hiệu quả.

---

### Tóm Tắt Theo Chương

**Chương 1: Các Khái Niệm Cơ Bản Về OOP (Basics of OOP)**

- **Lập trình hướng đối tượng (OOP)** là một mô hình tổ chức dữ liệu và hành vi thành các **đối tượng** được tạo từ các **lớp** – bản thiết kế do lập trình viên định nghĩa.
- **Đối tượng** là các thể hiện cụ thể của một lớp (ví dụ: Oscar là một đối tượng của lớp Cat). **Lớp** định nghĩa các thuộc tính chung (trường/fields) và hành vi (phương thức/methods) của các đối tượng.
- **Hệ thống phân cấp lớp (Class Hierarchies)** tổ chức nhiều lớp thành mối quan hệ cha-con. Các lớp con (subclasses) như Cat và Dog kế thừa các thuộc tính từ lớp cha (superclass) như Animal, đồng thời định nghĩa các đặc điểm riêng.
- **Ghi đè phương thức (Method Overriding)** cho phép các lớp con thay thế hoặc bổ sung chức năng cho các phương thức được kế thừa từ lớp cha.
- **Điểm mấu chốt**: Việc **hiểu rõ mối quan hệ giữa các lớp và đối tượng là rất quan trọng** để thiết kế OOP hiệu quả.

**Chương 2: Các Trụ Cột Của OOP (Pillars of OOP)**
OOP được xây dựng trên bốn khái niệm nền tảng:

- **Trừu tượng hóa (Abstraction)**: Mô hình hóa các đối tượng trong thế giới thực bằng cách tập trung vào các thuộc tính và hành vi **liên quan**, bỏ qua các chi tiết không cần thiết.
  - _Ví dụ_: Trong một ứng dụng du lịch, lớp Airplane chỉ cần hiển thị số hiệu chuyến bay, điểm đến, thời gian khởi hành thay vì chi tiết động cơ.
- **Đóng gói (Encapsulation)**: Ẩn các hoạt động nội bộ của một đối tượng và chỉ để lộ một **giao diện đơn giản** để tương tác.
  - _Ví dụ_: Khởi động động cơ ô tô chỉ cần bật chìa khóa hoặc nhấn nút, che đi các thao tác phức tạp bên trong.
- **Kế thừa (Inheritance)**: Cho phép tạo các lớp mới bằng cách **mở rộng các lớp hiện có**, thúc đẩy tái sử dụng mã. Lớp con kế thừa thuộc tính và phương thức từ lớp cha.
- **Đa hình (Polymorphism)**: Cho phép một chương trình xác định lớp cụ thể của một đối tượng và gọi các phương thức của nó mà **không cần biết chính xác kiểu của nó từ trước**.
  - _Ví dụ_: Gọi phương thức `makeSound` trên một đối tượng Animal (có thể là Cat hoặc Dog) sẽ tạo ra âm thanh phù hợp với lớp thực tế của đối tượng đó.
- **Điểm mấu chốt**: **Trừu tượng hóa giúp bạn tập trung vào các khía cạnh thiết yếu** khi thiết kế hệ thống phần mềm.

**Chương 3: Quan Hệ Giữa Các Đối Tượng (Relations Between Objects)**
Các mối quan hệ trong UML (Unified Modeling Language):

- **Liên kết (Association)**: Một mối quan hệ mà một đối tượng **sử dụng hoặc tương tác** với đối tượng khác. Có thể là hai chiều.
- **Phụ thuộc (Dependency)**: Một dạng liên kết yếu hơn, không có liên kết vĩnh viễn giữa các đối tượng. Phát sinh khi một đối tượng sử dụng đối tượng khác như một tham số phương thức hoặc khởi tạo nó.
- **Kết tập (Composition)**: Biểu thị mối quan hệ "toàn thể-bộ phận" nơi một đối tượng chứa được cấu thành từ một hoặc nhiều thành phần. **Sự tồn tại của thành phần phụ thuộc vào đối tượng chứa**.
- **Tập hợp (Aggregation)**: Một phiên bản lỏng lẻo hơn của kết tập, nơi một đối tượng giữ tham chiếu đến một đối tượng khác mà **không kiểm soát vòng đời của nó**. Các thành phần có thể tồn tại độc lập và là một phần của nhiều đối tượng chứa.

**Chương 4: Mẫu Thiết Kế Là Gì? (What's a Design Pattern?)**

- **Mẫu thiết kế (Design patterns)** là các giải pháp điển hình cho các vấn đề thường gặp trong thiết kế phần mềm, đóng vai trò là **bản thiết kế có thể tùy chỉnh** cho các vấn đề thiết kế lặp đi lặp lại.
- **Khác biệt với thuật toán**: Thuật toán cung cấp các hành động chính xác để đạt được mục tiêu, trong khi mẫu thiết kế là các khái niệm cấp cao hơn, yêu cầu điều chỉnh để phù hợp với ngữ cảnh cụ thể.
- **Các phần của một mẫu**: Bao gồm **Intent** (vấn đề và giải pháp), **Motivation** (ngữ cảnh và bản chất vấn đề), **Structure** (mối quan hệ giữa các thành phần), và **Code Example** (ví dụ thực tế).
- **Phân loại**: Được phân loại theo mục đích, bao gồm **Mẫu Creational (Khởi tạo)** (tập trung vào tạo đối tượng), **Mẫu Structural (Cấu trúc)** (chi tiết cách lắp ráp đối tượng), và **Mẫu Behavioral (Hành vi)** (giải quyết giao tiếp và gán trách nhiệm).
  - _Giải thích quan trọng_: Việc phân loại này có thể làm giảm tính linh hoạt và bản chất lai của các giải pháp trong thực tế. Một số mẫu có thể pha trộn các khía cạnh của nhiều loại.
- **Nguồn gốc**: Khái niệm này xuất phát từ công trình của Christopher Alexander trong thiết kế đô thị, được phổ biến trong lập trình bởi "Gang of Four" (Erich Gamma, John Vlissides, Ralph Johnson, và Richard Helm) với cuốn sách "Design Patterns: Elements of Reusable Object-Oriented Software".
- **Điểm mấu chốt**: **Hiểu cấu trúc chính của các mẫu thiết kế là rất quan trọng** để thiết kế phần mềm hiệu quả.

**Chương 5: Tại Sao Nên Học Các Mẫu? (Why Should I Learn Patterns?)**

- **Lợi ích**:
  - **Bộ công cụ giải pháp**: Cung cấp các giải pháp đã được kiểm nghiệm cho các vấn đề thiết kế phần mềm phổ biến, nâng cao khả năng giải quyết vấn đề thông qua các nguyên tắc thiết kế hướng đối tượng.
  - **Ngôn ngữ chung**: Thiết lập một **ngôn ngữ chung** giữa các thành viên trong nhóm, cải thiện giao tiếp và sự rõ ràng.
- **Điểm mấu chốt**: **Hiểu các Mẫu Thiết kế như một Bộ công cụ**.
  - _Giải thích quan trọng_: Mặc dù học mẫu thiết kế cải thiện kỹ năng giải quyết vấn đề và giao tiếp, việc **chỉ dựa vào chúng có thể dẫn đến tự mãn và thiếu đổi mới**; lập trình viên hiệu quả nhất điều chỉnh các phương pháp theo ngữ cảnh độc đáo của họ.

**Chương 6: Đặc Điểm Của Thiết Kế Tốt (Features of Good Design)**

- **Tái sử dụng mã (Code Reuse)**: Một chiến lược quan trọng để giảm chi phí phát triển và tăng tốc thời gian đưa ra thị trường.
  - Thách thức: Ghép nối chặt chẽ giữa các thành phần, phụ thuộc vào các lớp cụ thể thay vì giao diện, các thao tác được mã hóa cứng.
  - Mẫu thiết kế có thể nâng cao tính linh hoạt của thành phần, giúp tái sử dụng dễ quản lý hơn.
- **Khả năng mở rộng (Extensibility)**: Thay đổi là khía cạnh không thể tránh khỏi của lập trình. Các nhà phát triển thường phải đối mặt với các yêu cầu điều chỉnh do yêu cầu thay đổi, xu hướng thị trường hoặc sự hiểu biết về vấn đề được cải thiện.
- **Điểm mấu chốt**: Hầu hết các mẫu thiết kế trong cuốn sách này đều dựa trên các nguyên tắc thiết kế tốt.

**Chương 7: Đóng Gói Những Gì Thay Đổi (Encapsulate What Varies)**

- **Tổng quan**: Nguyên tắc này tập trung vào việc xác định các khía cạnh của một ứng dụng có thể thay đổi và **tách chúng khỏi các phần cố định** để giảm thiểu tác động của những thay đổi đó.
- **Ví dụ tương tự**: Giống như một con tàu được chia thành các khoang độc lập để chịu đựng các quả thủy lôi (biểu tượng cho các thay đổi trong chương trình). Bằng cách cô lập các phần biến đổi vào các mô-đun, toàn bộ mã cơ sở không bị ảnh hưởng.
- **Đóng gói ở cấp độ phương thức**: Di chuyển logic tính thuế vào một phương thức riêng (`getTaxRate`) để cô lập các thay đổi liên quan đến thuế.
- **Đóng gói ở cấp độ lớp**: Tách các hành vi và logic liên quan vào các lớp mới để tạo ra một thiết kế rõ ràng và đơn giản hơn, giúp quản lý độ phức tạp và cải thiện khả năng bảo trì.
- **Điểm mấu chốt**: **Đóng gói các yếu tố thay đổi có thể nâng cao khả năng bảo trì mã**, nhưng cũng có thể dẫn đến **over-engineering (quá kỹ thuật)**.

**Chương 8: Lập Trình Theo Giao Diện, Không Theo Triển Khai (Program to an Interface, not an Implementation)**

- **Tổng quan**: Nguyên tắc này nhấn mạnh việc lập trình theo giao diện thay vì triển khai cụ thể, ủng hộ tính linh hoạt và khả năng mở rộng trong thiết kế.
- **Lợi ích của tính linh hoạt**: Một thiết kế được coi là linh hoạt nếu nó có thể được mở rộng mà không làm hỏng mã hiện có.
- **Thiết lập cộng tác**:
  1.  Xác định các phương thức một đối tượng yêu cầu từ đối tượng khác.
  2.  Tạo một giao diện hoặc lớp trừu tượng mới cho các phương thức này.
  3.  Làm cho lớp phụ thuộc triển khai giao diện này.
  4.  Làm cho lớp thứ hai phụ thuộc vào giao diện thay vì lớp cụ thể.
- **Ví dụ**: Lớp Company được tách khỏi các lớp nhân viên cụ thể bằng cách sử dụng giao diện Employee, tận dụng đa hình để đối xử thống nhất với các đối tượng nhân viên khác nhau.
- **Ứng dụng Mẫu Thiết kế**: Sự chuyển đổi này là một ví dụ của **mẫu Factory Method**, minh họa cách các nguyên tắc thiết kế nâng cao khả năng bảo trì và khả năng mở rộng của mã.
- **Điểm mấu chốt**: **Lập trình theo giao diện giúp tăng cường tính linh hoạt và khả năng thích ứng** trong thiết kế phần mềm.

**Chương 9: Ưu Tiên Kết Tập Hơn Kế Thừa (Favor Composition Over Inheritance)**

- **Kế thừa** là phương pháp phổ biến để tái sử dụng mã, nhưng có nhiều **nhược điểm**:
  - **Hạn chế giao diện**: Lớp con không thể giảm giao diện của lớp cha, phải triển khai tất cả các phương thức trừu tượng, ngay cả khi không sử dụng.
  - **Vấn đề đóng gói**: Kế thừa có thể phá vỡ đóng gói vì lớp con có quyền truy cập vào các chi tiết lớp cha.
  - **Ghép nối chặt chẽ**: Lớp con bị ghép nối chặt chẽ với lớp cha; mọi thay đổi trong lớp cha có thể làm hỏng chức năng của lớp con.
  - **Hệ thống phân cấp phức tạp**: Có thể tạo ra các hệ thống phân cấp kế thừa song song phức tạp.
- **Kết tập (Composition)** là một giải pháp thay thế, nhấn mạnh mối quan hệ "có một" (ví dụ: một chiếc ô tô có một động cơ) thay vì mối quan hệ "là một". Kết tập có thể liên quan đến tập hợp, nơi một đối tượng tham chiếu đến đối tượng khác mà không quản lý vòng đời của nó.
- **Lợi ích của kết tập**: Linh hoạt hơn để thay đổi hoặc thay thế hành vi trong thời gian chạy, cải thiện khả năng bảo trì bằng cách giảm sự phụ thuộc và ghép nối giữa các lớp.

**Chương 10: Nguyên Tắc Trách Nhiệm Đơn Lẻ (Single Responsibility Principle)**

- **Nguyên tắc**: Một lớp chỉ nên có **một lý do để thay đổi**, tập trung vào một phần duy nhất của chức năng phần mềm.
- **Mục tiêu**: Giảm độ phức tạp, đặc biệt khi chương trình phát triển lớn hơn.
- **Hậu quả của việc vi phạm**: Thay đổi ở một khu vực có thể yêu cầu thay đổi ở các khu vực khác, tăng nguy cơ các tác dụng phụ không mong muốn.
- **Ví dụ**: Một lớp `Employee` quản lý cả dữ liệu nhân viên và tạo báo cáo bảng chấm công vi phạm nguyên tắc này. **Giải pháp tái cấu trúc** là tách chức năng báo cáo thành một lớp riêng để nâng cao khả năng bảo trì.
- **Điểm mấu chốt**: **Tập trung vào việc duy trì tính gắn kết cao trong các lớp** để nâng cao khả năng bảo trì phần mềm.
  - _Giải thích quan trọng_: Nguyên tắc này có thể **đơn giản hóa quá mức thiết kế lớp**, dẫn đến việc tạo ra quá nhiều lớp nhỏ gây phức tạp cho kiến trúc hệ thống.

**Chương 11: Nguyên Tắc Mở/Đóng (Open/Closed Principle)**

- **Nguyên tắc**: Các lớp nên **mở để mở rộng nhưng đóng để sửa đổi**. Điều này ngăn chặn việc phá vỡ mã hiện có khi thêm các tính năng mới.
- **Định nghĩa "Mở"**: Một lớp được coi là mở nếu nó có thể được mở rộng thông qua các lớp con, có thể thêm các phương thức mới hoặc ghi đè hành vi.
- **Định nghĩa "Đóng"**: Một lớp được coi là đóng khi nó được định nghĩa đầy đủ với một giao diện ổn định và sẽ không thay đổi trong tương lai.
- **Hiểu nguyên tắc**: Một lớp có thể đồng thời mở để mở rộng và đóng để sửa đổi. Thay đổi trực tiếp mã của một lớp có thể rủi ro phá vỡ chức năng hiện có.
- **Lưu ý quan trọng**: Nguyên tắc này không áp dụng cho tất cả các thay đổi; **lỗi nên được sửa trực tiếp trong lớp gốc** mà không cần tạo lớp con.
- **Giải pháp sử dụng mẫu Strategy**: Trích xuất các phương thức vận chuyển vào các lớp riêng biệt với một giao diện chung để thêm các phương thức mới mà không thay đổi lớp `Order` hiện có.
- **Điểm mấu chốt**: Nguyên tắc này thúc đẩy khả năng mở rộng trong khi giảm thiểu rủi ro trong mã hiện có.
  - _Giải thích quan trọng_: Việc áp dụng thực tế có thể dẫn đến **phức tạp và quá kỹ thuật**, với sự gia tăng quá mức các lớp.

**Chương 12: Nguyên Tắc Thay Thế Liskov (Liskov Substitution Principle)**

- **Nguyên tắc**: Các đối tượng của một lớp con nên có thể **thay thế cho các đối tượng của lớp cha** mà không làm thay đổi tính đúng đắn của chương trình.
- **Các nguyên tắc chính**:
  1.  **Tương thích kiểu tham số**: Các tham số phương thức của lớp con phải khớp hoặc trừu tượng hơn so với tham số của lớp cha.
  2.  **Tương thích kiểu trả về**: Kiểu trả về của phương thức lớp con phải khớp hoặc là một kiểu con của kiểu trả về của phương thức lớp cha.
  3.  **Xử lý ngoại lệ**: Phương thức lớp con không được ném ra các ngoại lệ mà phương thức lớp cha không ném.
  4.  **Bảo toàn điều kiện tiên quyết**: Lớp con không được tăng cường điều kiện tiên quyết của phương thức lớp cha.
  5.  **Bảo toàn điều kiện hậu quyết**: Lớp con không được làm suy yếu điều kiện hậu quyết của phương thức lớp cha.
  6.  **Bảo toàn bất biến**: Các điều kiện bất biến của lớp cha phải được bảo toàn trong lớp con.
  7.  **Tính toàn vẹn của trường riêng tư**: Lớp con không được thay đổi các trường riêng tư từ lớp cha thông qua reflection.
- **Ví dụ vi phạm**: Một lớp con `ReadOnlyDocument` ném ra ngoại lệ không chính xác trong phương thức `save` bị ghi đè.
- **Cách giải quyết**: Tái thiết kế hệ thống phân cấp bằng cách biến `ReadOnlyDocument` thành lớp cơ sở để đảm bảo hành vi lớp con đúng.

**Chương 13: Nguyên Tắc Tách Giao Diện (Interface Segregation Principle)**

- **Nguyên tắc**: **Client không nên bị buộc phải phụ thuộc vào các phương thức mà họ không sử dụng**. Nên tạo các giao diện hẹp để đảm bảo rằng các lớp client chỉ triển khai các hành vi cần thiết.
- **Chuyển đổi các giao diện "fat"**: Biến các giao diện lớn thành các giao diện chi tiết và cụ thể hơn.
- **Mối quan hệ với kế thừa lớp**: Một lớp có thể triển khai nhiều giao diện cùng lúc, cho phép chia nhỏ giao diện thành các phiên bản tinh chỉnh.
- **Ví dụ**: Trong thư viện tích hợp ứng dụng với các nhà cung cấp đám mây khác nhau, các giao diện rộng ban đầu chứa các phương thức không liên quan đến các nhà cung cấp khác. Giải pháp là chia nhỏ giao diện.
- **Lưu ý thận trọng**: Tránh chia giao diện quá mức vì có thể dẫn đến **tăng độ phức tạp** trong mã.
- **Điểm mấu chốt**: Nguyên tắc này nhấn mạnh nhu cầu về **các giao diện chuyên biệt**.
  - _Giải thích quan trọng_: Việc tạo ra nhiều giao diện cấp vi mô có thể giới thiệu **độ phức tạp không mong muốn** trong kiến trúc microservices.

**Chương 14: Nguyên Tắc Đảo Ngược Phụ Thuộc (Dependency Inversion Principle)**

- **Mục tiêu**: Nâng cao thiết kế phần mềm bằng cách **tách biệt mối quan tâm giữa các lớp cấp cao và cấp thấp**.
- **Khái niệm cốt lõi**:
  - **Lớp cấp cao**: Chứa logic nghiệp vụ phức tạp và điều phối hành động với các lớp cấp thấp.
  - **Lớp cấp thấp**: Xử lý các thao tác cơ bản như truy cập đĩa hoặc quản lý cơ sở dữ liệu.
- **Các vấn đề với thiết kế truyền thống**: Các lớp cấp cao thường phụ thuộc quá mức vào các triển khai cấp thấp.
- **Nguyên tắc hướng dẫn**:
  1.  **Định nghĩa giao diện**: Tạo giao diện cho các thao tác cấp thấp bằng thuật ngữ nghiệp vụ.
  2.  **Phụ thuộc vào giao diện**: Các lớp cấp cao nên sử dụng các giao diện này thay vì các lớp cấp thấp cụ thể.
  3.  **Triển khai bởi các lớp cấp thấp**: Các lớp cấp thấp triển khai các giao diện đã định nghĩa, **đảo ngược hướng phụ thuộc** ban đầu.
- **Mối quan hệ với nguyên tắc Mở/Đóng**: DIP thường tương quan với nguyên tắc Mở/Đóng, cho phép thêm vào các lớp cấp thấp mà không sửa đổi mã hiện có.
- **Ví dụ minh họa**: Lớp báo cáo ngân sách cấp cao ban đầu phụ thuộc trực tiếp vào lớp cơ sở dữ liệu cấp thấp. Sau khi điều chỉnh, lớp báo cáo hoạt động độc lập với các chi tiết cấp thấp.
- **Điểm mấu chốt**: **Nhấn mạnh Giao diện hơn các Lớp Cụ thể**.

---

### Mẫu Thiết Kế Creational (Khởi tạo)

**Chương 15: Factory Method**

- **Tổng quan**: Là một mẫu thiết kế khởi tạo, cung cấp một **giao diện để tạo đối tượng trong một lớp cha** trong khi cho phép các lớp con quyết định loại đối tượng chính xác sẽ được tạo.
- **Vấn đề**: Mã hiện tại được ghép nối chặt chẽ với các lớp cụ thể, khiến việc thêm các loại đối tượng mới (ví dụ: vận chuyển bằng đường biển ngoài đường bộ) trở nên phức tạp.
- **Giải pháp**: Thay thế các lệnh gọi xây dựng trực tiếp bằng một **phương thức factory**, cho phép tạo đối tượng trong khi vẫn duy trì sự tách biệt các mối quan tâm. Các lớp con có thể ghi đè phương thức factory này.
- **Cấu trúc**: `Product` (giao diện chung), `Concrete Products` (triển khai), `Creator` (khai báo phương thức factory), `Concrete Creators` (thực hiện phương thức factory).
- **Khả năng ứng dụng**: Khi bạn không biết trước các kiểu và phụ thuộc chính xác của đối tượng.
- **Ưu điểm**: Tránh ghép nối chặt chẽ, tuân thủ SRP và OCP.
- **Nhược điểm**: Có thể tăng độ phức tạp của mã.

**Chương 16: Abstract Factory**

- **Tổng quan**: Là một mẫu thiết kế khởi tạo, cho phép **tạo ra các họ đối tượng liên quan mà không cần chỉ định các lớp cụ thể của chúng**.
- **Vấn đề**: Cần tạo các sản phẩm liên quan (ghế, sofa, bàn cà phê) theo nhiều phong cách (hiện đại, Victoria) và đảm bảo chúng phù hợp về phong cách, đồng thời hệ thống phải linh hoạt khi thêm sản phẩm/họ mới.
- **Giải pháp**:
  1.  Khai báo giao diện riêng biệt cho từng loại sản phẩm.
  2.  Tạo giao diện Abstract Factory với các phương thức tạo.
  3.  Triển khai Concrete Factories cho mỗi biến thể sản phẩm.
  4.  Mã client tương tác với các giao diện trừu tượng thay vì các lớp cụ thể.
- **Cấu trúc**: `Abstract Products`, `Concrete Products`, `Abstract Factory`, `Concrete Factories`, `Client`.
- **Khả năng ứng dụng**: Khi xử lý các họ sản phẩm mà các lớp cụ thể nên được ẩn khỏi client để dễ mở rộng.
- **Ưu điểm**: Đảm bảo tương thích sản phẩm, giảm ghép nối chặt chẽ, hỗ trợ SRP và OCP.
- **Nhược điểm**: Có thể tăng độ phức tạp của mã.
- **Quan hệ**: Thường phát triển từ Factory Method để linh hoạt hơn.

**Chương 17: Builder**

- **Tổng quan**: Là một mẫu thiết kế khởi tạo, cho phép **xây dựng đối tượng phức tạp từng bước**. Cung cấp cách tạo các cấu hình khác nhau của một đối tượng mà không gây nhầm lẫn từ nhiều tham số hoặc lớp con.
- **Vấn đề**: Tạo đối tượng phức tạp thường dẫn đến các hàm tạo phức tạp hoặc nhiều lớp con cho mọi cấu hình có thể.
- **Giải pháp**: Tách mã xây dựng đối tượng thành các **đối tượng builder** riêng biệt. Các builder này thực hiện một loạt các bước đã định nghĩa để xây dựng đối tượng.
- **Director (Giám đốc)**: Một lớp director có thể tổ chức trình tự các bước xây dựng, giúp client tương tác với builder đơn giản hơn.
- **Cấu trúc**: `Builder Interface`, `Concrete Builders`, `Products`, `Director Class`, `Client`.
- **Khả năng ứng dụng**: Loại bỏ các hàm tạo cồng kềnh với nhiều tham số tùy chọn, tạo các biểu diễn đa dạng của sản phẩm.
- **Ưu điểm**: Cho phép xây dựng từng bước, thúc đẩy tái sử dụng, hỗ trợ SRP.
- **Nhược điểm**: Tăng độ phức tạp tổng thể của mã do tạo ra nhiều lớp hơn.
- **Quan hệ**: Có thể phát triển từ Factory Method, có thể làm việc cùng với Composite cho cây cấu trúc.

**Chương 18: Prototype**

- **Tổng quan**: Là một mẫu thiết kế khởi tạo, cho phép **sao chép các đối tượng hiện có mà không cần mã phụ thuộc vào lớp của chúng**.
- **Vấn đề**: Sao chép chính xác một đối tượng truyền thống đòi hỏi kiến thức về lớp của nó và quyền truy cập vào các trường của nó, điều này có thể có vấn đề do các trường riêng tư và sự phụ thuộc vào các lớp cụ thể.
- **Giải pháp**: Giao phó quá trình sao chép cho chính các đối tượng thông qua một **giao diện chung** cho phép sao chép mà không cần ghép nối lớp. Giao diện thường có một phương thức `clone` duy nhất.
- **Ví dụ tương tự trong thế giới thực**: Phân bào nguyên nhiễm, nơi một tế bào gốc chủ động tạo ra các bản sao chính xác.
- **Cấu trúc**: `Prototype Interface` (khai báo phương thức sao chép), `Concrete Prototype Class` (thực hiện phương thức sao chép), `Client` (sử dụng giao diện prototype để tạo bản sao).
- **Khả năng ứng dụng**: Khi mã của bạn không nên phụ thuộc vào các lớp cụ thể của đối tượng bạn cần sao chép.
- **Ưu điểm**: Giảm ghép nối, loại bỏ mã khởi tạo lặp lại, tiện lợi cho đối tượng phức tạp.
- **Nhược điểm**: Sao chép đối tượng phức tạp với tham chiếu vòng có thể khó khăn.

**Chương 19: Singleton**

- **Tổng quan**: Là một mẫu thiết kế khởi tạo, đảm bảo một lớp **chỉ có một thể hiện duy nhất** và cung cấp một **điểm truy cập toàn cầu** đến thể hiện đó.
- **Vấn đề**:
  1.  Kiểm soát thể hiện duy nhất: Đảm bảo chỉ có một thể hiện của một lớp, thường được sử dụng để điều chỉnh quyền truy cập vào tài nguyên dùng chung như cơ sở dữ liệu.
  2.  Điểm truy cập toàn cầu: Cung cấp một điểm truy cập an toàn, không giống như các biến toàn cục có thể bị sửa đổi ở bất cứ đâu.
  - _Lưu ý quan trọng_: **Vi phạm Nguyên tắc Trách nhiệm Đơn lẻ (SRP)** vì nó giải quyết hai vấn đề cùng lúc.
- **Giải pháp**:
  - Tạo một hàm tạo riêng tư để ngăn việc khởi tạo bên ngoài.
  - Triển khai một phương thức tạo tĩnh để khởi tạo đối tượng và trả về nó cho tất cả các lệnh gọi tiếp theo.
- **Ví dụ tương tự trong thế giới thực**: Chính phủ, là một thực thể duy nhất đại diện cho quyền lực, có thể truy cập toàn cầu.
- **Cấu trúc**: Lớp Singleton khai báo một phương thức tĩnh `getInstance` để trả về thể hiện duy nhất của nó trong khi ẩn hàm tạo.
- **Khả năng ứng dụng**: Khi một lớp trong chương trình của bạn chỉ nên có một thể hiện duy nhất cho tất cả các client (ví dụ: kết nối cơ sở dữ liệu).
- **Ưu điểm**: Đảm bảo lớp có một thể hiện duy nhất, cung cấp truy cập toàn cầu, khởi tạo theo yêu cầu (lazy initialization).
- **Nhược điểm**: Vi phạm SRP, có thể chỉ ra quan hệ thiết kế kém, yêu cầu xử lý đặc biệt trong môi trường đa luồng, kiểm thử phức tạp.
- **Quan hệ**: Abstract Factories, Builders, và Prototypes có thể được triển khai dưới dạng Singletons.

---

### Mẫu Thiết Kế Structural (Cấu trúc)

**Chương 20: Adapter**

- **Tổng quan**: Là một mẫu thiết kế cấu trúc, cho phép **cộng tác giữa các đối tượng có giao diện không tương thích**.
- **Vấn đề**: Ứng dụng giám sát thị trường chứng khoán cần tích hợp thư viện phân tích của bên thứ ba chỉ chấp nhận dữ liệu ở định dạng JSON, trong khi ứng dụng hoạt động với XML. Giao diện không tương thích.
- **Giải pháp**: Tạo một **adapter** cho phép chuyển đổi giữa hai định dạng mà không thay đổi các đối tượng hiện có. Adapter dịch dữ liệu để đối tượng được bọc không biết về việc chuyển đổi.
- **Ví dụ tương tự trong thế giới thực**: Bộ chuyển đổi phích cắm điện cho phép các thiết bị kết nối với các tiêu chuẩn ổ cắm khác nhau.
- **Cấu trúc**:
  1.  **Object Adapter**: `Client`, `Client Interface`, `Service` (lớp hữu ích với giao diện không tương thích), `Adapter` (triển khai giao diện client và bao bọc đối tượng service, dịch các lệnh gọi).
  2.  **Class Adapter**: Sử dụng kế thừa để thích nghi giao diện (trong các ngôn ngữ hỗ trợ đa kế thừa).
- **Khả năng ứng dụng**: Khi bạn muốn sử dụng một lớp hiện có với giao diện không tương thích.
- **Ưu điểm**: Tuân thủ SRP (tách chuyển đổi giao diện khỏi logic nghiệp vụ), OCP (cho phép adapter mới không sửa đổi mã client).
- **Nhược điểm**: Tăng độ phức tạp của mã với các giao diện và lớp bổ sung.
- **Quan hệ**: Khác với Bridge (thiết kế ngay từ đầu), Decorator (tăng cường không thay đổi giao diện), Proxy (cung cấp cùng giao diện).
- **Điểm mấu chốt**: **Giới hạn ứng dụng của mẫu Adapter**; có thể dẫn đến **tăng độ phức tạp và thách thức bảo trì**.

**Chương 21: Bridge**

- **Tổng quan**: Là một mẫu thiết kế cấu trúc, **tách một lớp lớn hoặc một tập hợp các lớp liên quan thành hai hệ thống phân cấp riêng biệt**: trừu tượng hóa và triển khai, cho phép phát triển độc lập.
- **Vấn đề**: Khi cố gắng mở rộng một hệ thống phân cấp lớp (ví dụ: lớp Shape hình học với các lớp con Circle và Square) để bao gồm các thuộc tính bổ sung như màu sắc, độ phức tạp tăng theo cấp số nhân. Mỗi sự kết hợp mới đòi hỏi tạo ra nhiều lớp con, dẫn đến cấu trúc lớp cồng kềnh.
- **Giải pháp**: Mẫu Bridge giải quyết vấn đề này bằng cách chuyển từ kế thừa sang **kết tập**. Bằng cách tạo một hệ thống phân cấp lớp riêng biệt cho một chiều (ví dụ: màu sắc), lớp gốc tham chiếu đến hệ thống phân cấp mới thông qua một cây cầu (bridge), cho phép mở rộng độc lập mà không thay đổi cấu trúc lớp cốt lõi.
- **Khái niệm chính**:
  - **Trừu tượng hóa (Abstraction)**: Lớp điều khiển cấp cao, có thể là GUI.
  - **Triển khai (Implementation)**: Mã cơ bản hoặc API. Hai lớp này có thể phát triển độc lập.
- **Cấu trúc**: `Abstraction` (cung cấp logic điều khiển thông qua tham chiếu đến đối tượng triển khai), `Implementation` (khai báo giao diện chung cho các triển khai cụ thể).
- **Khả năng ứng dụng**: Khi bạn muốn phân chia và tổ chức một lớp đơn khối có nhiều biến thể chức năng.
- **Ưu điểm**: Có thể giới thiệu các trừu tượng và triển khai mới một cách độc lập.
- **Nhược điểm**: Có thể làm phức tạp mã một cách không cần thiết nếu lớp đã gắn kết cao.

**Chương 22: Composite**

- **Tổng quan**: Còn được gọi là Object Tree, là một mẫu thiết kế cấu trúc cho phép **ghép các đối tượng thành cấu trúc cây**, cho phép chúng được coi là các đối tượng riêng lẻ.
- **Vấn đề**: Khi mô hình cốt lõi có thể được biểu diễn dưới dạng cây (ví dụ: đơn hàng có thể chứa cả sản phẩm và hộp, mỗi hộp có thể chứa thêm sản phẩm hoặc hộp khác). Việc tính toán tổng giá sẽ phức tạp.
- **Giải pháp**: Cung cấp một **giao diện chung** để xử lý Sản phẩm và Hộp, cho phép tính toán giá đệ quy mà không cần quan tâm đến các lớp cụ thể của các đối tượng liên quan.
- **Ví dụ tương tự trong thế giới thực**: Hệ thống cấp bậc quân sự, nơi các lệnh được đưa ra ở cấp cao hơn và được truyền xuống các cấp thấp hơn.
- **Cấu trúc**: `Component Interface` (khai báo các thao tác chung), `Leaf` (đại diện cho các phần tử cuối), `Container (Composite)` (có thể chứa các leaf hoặc container khác), `Client` (tương tác với tất cả các phần tử qua giao diện component).
- **Khả năng ứng dụng**: Khi triển khai cấu trúc giống cây, cho phép xử lý thống nhất các phần tử phức tạp và đơn giản.
- **Ưu điểm**: Đơn giản hóa làm việc với cấu trúc phức tạp, tuân thủ OCP (cho phép các loại phần tử mới không sửa đổi mã hiện có).
- **Nhược điểm**: Tìm kiếm một giao diện chung có thể dẫn đến **quá khái quát hóa**, làm phức tạp việc hiểu.
- **Quan hệ**: Builder để xây dựng cây Composite, Iterator để duyệt cấu trúc, Visitor để thực thi các thao tác.
- **Điểm mấu chốt**: Mẫu Composite có thể **đơn giản hóa quá mức các hệ thống phân cấp phức tạp** và che khuất những khác biệt quan trọng.

**Chương 23: Decorator**

- **Tổng quan**: Là một mẫu thiết kế cấu trúc, cho phép **thêm các hành vi mới vào đối tượng bằng cách bọc chúng trong các đối tượng đặc biệt (decorators) chứa các hành vi mong muốn**.
- **Vấn đề**: Một lớp Notifier cơ bản để gửi cảnh báo email dẫn đến bùng nổ tổ hợp các lớp con khi có yêu cầu các loại thông báo bổ sung (SMS, Facebook, Slack).
- **Giải pháp**: Sử dụng **kết tập (composition)** thay vì kế thừa, mẫu Decorator cho phép kết hợp hành vi động bằng cách bọc lớp Notifier bằng các decorator.
- **Cấu trúc**: `Component` (khai báo giao diện), `Concrete Component` (thực hiện hành vi cơ sở), `Base Decorator` (chứa tham chiếu đến đối tượng được bọc), `Concrete Decorators` (mở rộng base decorator để thêm hành vi cụ thể), `Client` (tương tác với giao diện component).
- **Ví dụ giả mã**: Dữ liệu được bọc vào các decorator để mã hóa và nén, thay đổi các thao tác đọc/ghi mà không thay đổi logic lớp cốt lõi.
- **Khả năng ứng dụng**: Khi cần thêm hành vi bổ sung trong thời gian chạy mà không thay đổi mã hiện có; lý tưởng khi mở rộng thông qua kế thừa là không thực tế.
- **Ưu điểm**: Mở rộng hành vi động mà không cần lớp con, kết hợp nhiều hành vi thông qua xếp chồng, tuân thủ SRP.
- **Nhược điểm**: Khó khăn khi loại bỏ decorator khỏi một chồng, phức tạp trong việc đảm bảo thứ tự decorator, thiết lập ban đầu có thể tạo ra mã cấu hình rối rắm.
- **Quan hệ**: Adapter (thay đổi giao diện), Proxy (quản lý vòng đời), Composite (tổng hợp kết quả).

**Chương 24: Facade**

- **Tổng quan**: Là một mẫu thiết kế cấu trúc, đơn giản hóa tương tác với các thư viện hoặc framework phức tạp bằng cách **cung cấp một giao diện rõ ràng**.
- **Vấn đề**: Các thư viện phức tạp yêu cầu quản lý đáng kể nhiều đối tượng và phụ thuộc, khiến mã khó hiểu và bảo trì.
- **Giải pháp**: Một lớp Facade cung cấp một **giao diện đơn giản hóa** cho một hệ thống con phức tạp, chỉ hiển thị các tính năng mà client cần. Nó đóng gói sự phức tạp, cho phép tương tác dễ dàng hơn.
- **Ví dụ tương tự trong thế giới thực**: Đặt hàng qua điện thoại minh họa một facade nơi người điều hành cung cấp giao diện đơn giản cho nhiều dịch vụ cửa hàng.
- **Cấu trúc**: `Facade` (truy cập tiện lợi và điều phối yêu cầu), `Additional Facade` (ngăn chặn phức tạp), `Complex Subsystem` (gồm nhiều đối tượng), `Client` (tương tác với hệ thống con thông qua facade).
- **Ưu điểm**: Cô lập mã của bạn khỏi sự phức tạp của hệ thống con.
- **Nhược điểm**: Tiềm năng tạo ra "god object" nếu facade quá lớn.
- **Quan hệ**: Adapter (thay đổi giao diện), Mediator (tập trung giao tiếp), Singleton (một facade có thể là một singleton).

**Chương 25: Flyweight**

- **Giới thiệu**: Là một mẫu thiết kế cấu trúc, **tối ưu hóa việc sử dụng bộ nhớ bằng cách chia sẻ các phần chung của trạng thái giữa nhiều đối tượng**, cho phép sử dụng RAM hiệu quả hơn.
- **Vấn đề**: Trong một trò chơi điện tử với hệ thống hạt thực tế, việc tạo quá nhiều đối tượng (như hạt) dẫn đến tiêu thụ RAM cao, gây ra sự cố trên các máy yếu hơn.
- **Giải pháp**: Phân biệt giữa trạng thái **nội tại (intrinsic state)** và **ngoại tại (extrinsic state)** của đối tượng. Trạng thái nội tại (dữ liệu chung như màu sắc và sprite) nằm trong đối tượng, trong khi trạng thái ngoại tại (dữ liệu duy nhất như tọa độ và chuyển động) được truyền cho các phương thức và lưu trữ bên ngoài.
- **Flyweight và tính bất biến**: Đối tượng Flyweight nên **bất biến**, chỉ được khởi tạo một lần qua hàm tạo.
- **Flyweight Factory**: Một phương thức factory có thể được thiết lập để quản lý và tái sử dụng các đối tượng flyweight, ngăn chặn việc tạo trực tiếp bởi client.
- **Cấu trúc**: `Flyweight Class` (chứa trạng thái nội tại), `Context Class` (chứa trạng thái ngoại tại), `Client` (quản lý trạng thái ngoại tại và tương tác với flyweight), `Flyweight Factory` (quản lý tạo và truy xuất flyweight).
- **Khả năng ứng dụng**: Khi xử lý số lượng lớn các đối tượng tương tự tiêu thụ nhiều RAM, đặc biệt nếu chúng chia sẻ trạng thái trùng lặp.
- **Ưu điểm**: Tiết kiệm bộ nhớ đáng kể.
- **Nhược điểm**: Tăng độ phức tạp và chi phí tiềm ẩn từ việc tính toán lại trạng thái ngoại tại.
- **Quan hệ**: Có thể tối ưu hóa bộ nhớ trong cấu trúc Composite, tương phản với Facade, khác với Singleton ở chỗ cho phép nhiều thể hiện.

**Chương 26: Proxy**

- **Tổng quan**: Là một mẫu thiết kế cấu trúc, đóng vai trò là **thế thân hoặc giữ chỗ cho một đối tượng khác**, kiểm soát quyền truy cập vào đối tượng gốc và cho phép tiền xử lý hoặc hậu xử lý các yêu cầu.
- **Vấn đề**: Kiểm soát quyền truy cập vào một đối tượng, đặc biệt là các đối tượng tiêu tốn tài nguyên. Khởi tạo lười (lazy initialization) truyền thống có thể dẫn đến trùng lặp mã.
- **Giải pháp**: Tạo một **lớp proxy mới chia sẻ cùng giao diện với đối tượng dịch vụ gốc**. Lớp proxy này ủy quyền các yêu cầu cho đối tượng dịch vụ gốc, cho phép các chức năng bổ sung như khởi tạo lười và bộ nhớ đệm mà client không nhận biết.
- **Ví dụ tương tự trong thế giới thực**: Thẻ tín dụng hoạt động như một proxy cho tài khoản ngân hàng, cho phép giao dịch mà không cần mang tiền mặt.
- **Cấu trúc**: `Service Interface`, `Service` (thực hiện logic nghiệp vụ), `Proxy` (giữ tham chiếu đến đối tượng service và quản lý vòng đời của nó), `Client` (tương tác với cả service và proxy qua cùng giao diện).
- **Khả năng ứng dụng**: **Lazy Initialization** (trì hoãn tạo đối tượng nặng), **Access Control** (hạn chế truy cập), **Local Execution of Remote Services**, **Logging Requests**, **Caching Results**, **Smart Reference** (theo dõi và hủy bỏ đối tượng nặng).
- **Ưu điểm**: Kiểm soát đối tượng service mà client không nhận biết, quản lý vòng đời service, thêm proxy mới mà không sửa đổi mã hiện có.
- **Nhược điểm**: Tăng độ phức tạp với nhiều lớp hơn, tiềm năng chậm trễ trong phản hồi của service.
- **Quan hệ**: Adapter (cung cấp giao diện khác), Facade (đệm phức tạp), Decorator (chia sẻ cấu trúc nhưng khác ý định và quản lý vòng đời).

---

### Mẫu Thiết Kế Behavioral (Hành vi)

**Chương 27: Chain of Responsibility**

- **Tổng quan**: Là một mẫu thiết kế hành vi, cho phép **truyền các yêu cầu thông qua một chuỗi các handler**. Mỗi handler có thể chọn xử lý yêu cầu hoặc chuyển tiếp nó cho handler tiếp theo trong chuỗi.
- **Vấn đề**: Trong hệ thống đặt hàng trực tuyến, cần thực hiện nhiều kiểm tra tuần tự. Khi thêm các kiểm tra mới, mã trở nên phức tạp và khó bảo trì.
- **Giải pháp**: Đóng gói mỗi hành vi vào các đối tượng độc lập gọi là **handler**. Mỗi handler được liên kết trong một chuỗi, nơi nó có thể xử lý yêu cầu và sau đó chọn chuyển tiếp hoặc chấm dứt xử lý.
- **Ví dụ tương tự trong thế giới thực**: Cuộc gọi hỗ trợ kỹ thuật, nơi yêu cầu đi qua nhiều cấp độ nhà điều hành.
- **Cấu trúc**: `Handler` (khai báo giao diện chung), `Base Handler`.
- **Khả năng ứng dụng**: Khi chương trình của bạn dự kiến xử lý các loại yêu cầu khác nhau theo nhiều cách, nhưng các loại yêu cầu và trình tự chính xác không được biết trước.
- **Ưu điểm**: Tuân thủ SRP (tách rời các lớp gọi thao tác khỏi các lớp thực hiện thao tác), OCP (dễ dàng thêm handler mới).
- **Nhược điểm**: Yêu cầu có thể không được xử lý nếu không tìm thấy handler phù hợp, quản lý chuỗi phức tạp.

**Chương 28: Command**

- **Tổng quan**: Là một mẫu thiết kế hành vi, biến một yêu cầu thành một **đối tượng độc lập**, chứa tất cả thông tin cần thiết. Điều này cho phép tham số hóa phương thức, trì hoãn hoặc xếp hàng yêu cầu, và hỗ trợ các thao tác có thể hoàn tác.
- **Vấn đề**: Trong ứng dụng soạn thảo văn bản, các nút cho các thao tác khác nhau được tạo từ lớp Button cơ sở. Sử dụng nhiều lớp con để xử lý hành vi nhấp nút khác nhau dẫn đến phức tạp và ghép nối chặt chẽ giữa mã GUI và logic nghiệp vụ.
- **Giải pháp**: Tách biệt các mối quan tâm của lớp GUI và logic nghiệp vụ. Đóng gói chi tiết yêu cầu vào các **lớp command** riêng biệt. Mỗi lớp command triển khai một giao diện với một phương thức thực thi, cho phép xử lý lệnh linh hoạt.
- **Ví dụ tương tự trong thế giới thực**: Quy trình đặt món ăn tại nhà hàng, nơi phiếu gọi món của người phục vụ hoạt động như một lệnh.
- **Cấu trúc**: `Sender (Invoker)` (khởi tạo yêu cầu), `Command Interface` (khai báo phương thức thực thi), `Concrete Commands` (thực hiện yêu cầu cụ thể), `Receiver` (chứa logic nghiệp vụ), `Client` (tạo và cấu hình đối tượng command).
- **Khả năng ứng dụng**: Khi bạn muốn tham số hóa đối tượng bằng các thao tác, xếp hàng, lên lịch hoặc thực thi từ xa các thao tác, triển khai các thao tác có thể hoàn tác.
- **Ưu điểm**: Tuân thủ SRP và OCP, hỗ trợ chức năng hoàn tác/làm lại, cho phép các thao tác trì hoãn.
- **Nhược điểm**: Có thể làm phức tạp cấu trúc mã bằng cách thêm các lớp giữa các thành phần.
- **Quan hệ**: Có thể sử dụng cùng với Chain of Responsibility và Mediator, làm việc cùng với Memento cho chức năng hoàn tác.

**Chương 29: Iterator**

- **Tổng quan**: Là một mẫu thiết kế hành vi, cho phép **duyệt qua các phần tử của một tập hợp mà không làm lộ cách biểu diễn cơ bản của nó** (như danh sách, stack, cây).
- **Vấn đề**: Cần truy cập các phần tử tập hợp mà không lặp lại truy cập vào cùng các phần tử. Các cấu trúc phức tạp (như cây) có thể yêu cầu các phương pháp duyệt khác nhau mà không làm lộn xộn chức năng chính của tập hợp.
- **Giải pháp**: Trích xuất hành vi duyệt vào một **đối tượng iterator riêng biệt**. Iterator này đóng gói chi tiết duyệt, cho phép nhiều iterator duyệt cùng một tập hợp một cách độc lập. Tất cả các iterator tuân thủ cùng một giao diện.
- **Ví dụ tương tự trong thế giới thực**: Điều hướng một thành phố như Rome bằng ứng dụng bản đồ hoặc hướng dẫn viên địa phương.
- **Cấu trúc**: `Iterator Interface` (khai báo các thao tác duyệt), `Concrete Iterators` (thực hiện các thuật toán duyệt cụ thể), `Collection Interface` (định nghĩa các phương thức để lấy iterator), `Concrete Collections` (trả về thể hiện của các lớp iterator cụ thể), `Client` (làm việc với các tập hợp và iterator thông qua giao diện của chúng).
- **Khả năng ứng dụng**: Để ẩn đi sự phức tạp của cấu trúc dữ liệu của tập hợp khỏi client, giảm trùng lặp mã duyệt.
- **Ưu điểm**: Nâng cao SRP (tách logic duyệt), tuân thủ OCP (cho phép mở rộng), hỗ trợ lặp song song và tiếp tục lặp trì hoãn.
- **Nhược điểm**: Có thể là quá mức cần thiết cho các tập hợp đơn giản, có thể kém hiệu quả hơn so với truy cập trực tiếp.
- **Quan hệ**: Có thể làm việc cùng với Composite để duyệt cây, Factory Method để trả về các loại iterator khác nhau.

**Chương 30: Mediator**

- **Tổng quan**: Là một mẫu thiết kế hành vi, đơn giản hóa giao tiếp giữa các đối tượng bằng cách **hạn chế tương tác trực tiếp và thực thi cộng tác thông qua một đối tượng mediator**.
- **Vấn đề**: Sự phụ thuộc lẫn nhau phức tạp trong các phần tử giao diện người dùng có thể dẫn đến hỗn loạn khi ứng dụng phát triển. Các điều khiển biểu mẫu trong hộp thoại có thể tương tác theo những cách không thể đoán trước.
- **Giải pháp**: Thúc đẩy giao tiếp gián tiếp giữa các thành phần thông qua một mediator, giảm sự phụ thuộc giữa chúng. Điều này thúc đẩy khả năng tái sử dụng và dễ sửa đổi.
- **Ví dụ tương tự trong thế giới thực**: Kiểm soát không lưu, nơi các phi công giao tiếp thông qua một tháp điều khiển thay vì trực tiếp với nhau.
- **Cấu trúc**: `Components` (chứa logic nghiệp vụ với tham chiếu đến giao diện mediator), `Mediator Interface` (khai báo các phương thức giao tiếp), `Concrete Mediators`.
- **Khả năng ứng dụng**: Khi khó thay đổi một số lớp vì chúng bị ghép nối chặt chẽ với nhiều lớp khác.
- **Ưu điểm**: Nâng cao khả năng tái sử dụng thành phần, giảm sự phụ thuộc giữa các thành phần.
- **Nhược điểm**: Mediator có thể trở thành một "God Object" nếu nó tập trung quá nhiều logic.
- **Quan hệ**: Liên quan đến Observer và Facade.

**Chương 31: Memento**

- **Tổng quan**: Là một mẫu thiết kế hành vi, cho phép **lưu và khôi phục trạng thái trước đó của một đối tượng mà không làm lộ chi tiết triển khai của nó**.
- **Vấn đề**: Trong ứng dụng soạn thảo văn bản, người dùng muốn có khả năng hoàn tác các hành động. Thách thức phát sinh khi cố gắng ghi lại trạng thái của một đối tượng do các hạn chế đóng gói.
- **Giải pháp**: Mẫu Memento giải quyết các vấn đề này bằng cách để chính đối tượng, được gọi là **originator**, tạo ra các ảnh chụp nhanh của riêng nó, được lưu trữ trong một **memento**. Điều này hạn chế quyền truy cập vào trạng thái được giữ trong memento, chỉ cho phép originator sửa đổi nó.
- **Cấu trúc và triển khai**:
  1.  **Originator**: Tạo và khôi phục các ảnh chụp nhanh trạng thái của nó.
  2.  **Memento**: Đại diện cho một ảnh chụp nhanh trạng thái của originator; được thiết kế để **bất biến**.
  3.  **Caretaker**: Quản lý khi nào cần lưu và khôi phục trạng thái của originator.
- **Khả năng ứng dụng**: Để tạo các ảnh chụp nhanh nhằm khôi phục trạng thái trước đó của một đối tượng. Lý tưởng cho các tình huống mà việc truy cập trực tiếp vào các trường vi phạm đóng gói.
- **Ưu điểm**: Bảo toàn đóng gói trong khi cho phép quản lý trạng thái, đơn giản hóa thiết kế của originator.
- **Nhược điểm**: Tiềm năng sử dụng bộ nhớ cao nếu memento được tạo quá thường xuyên, caretaker phải quản lý vòng đời của memento.
- **Quan hệ**: Có thể kết hợp với Command cho chức năng hoàn tác, làm việc cùng với Iterator để nắm bắt trạng thái lặp.
- **Điểm mấu chốt**: **Hiểu tầm quan trọng của đóng gói trong quản lý trạng thái**.

**Chương 32: Observer**

- **Tổng quan**: Là một mẫu thiết kế hành vi, cho phép một **cơ chế đăng ký để thông báo cho nhiều đối tượng về các sự kiện xảy ra trong đối tượng mà chúng đang quan sát**.
- **Vấn đề**: Trong các tình huống mà Khách hàng muốn được thông báo về tình trạng sẵn có của một sản phẩm cụ thể tại Cửa hàng, việc truy cập thường xuyên có thể lãng phí thời gian, trong khi thông báo liên tục từ Cửa hàng có thể làm ngập khách hàng với thông tin không mong muốn.
- **Giải pháp**: Tạo một **Publisher** (chủ đề) duy trì danh sách các **Subscriber**. Subscriber có thể tham gia hoặc rời khỏi danh sách này theo sở thích của họ. Publisher thông báo cho tất cả các subscriber đã đăng ký về các sự kiện quan trọng.
- **Cấu trúc**: `Publisher` (quản lý danh sách subscriber), `Subscriber Interface` (định nghĩa phương thức nhận thông báo), `Concrete Subscribers` (thực hiện hành động cụ thể), `Contextual Information` (có thể được truyền qua), `Client` (tạo và đăng ký subscriber).
- **Khả năng ứng dụng**: Khi thay đổi trạng thái trong một đối tượng yêu cầu thay đổi trong các đối tượng khác, và các đối tượng nhận cụ thể không được biết trước.
- **Ưu điểm**: Tuân thủ OCP (cho phép các lớp subscriber mới không sửa đổi mã hiện có), hỗ trợ quan hệ động giữa các đối tượng.
- **Nhược điểm**: Thông báo có thể không xảy ra theo thứ tự có thể dự đoán được.
- **Quan hệ**: Liên quan đến Mediator, Chain of Responsibility, và Command, nhưng nhấn mạnh các đăng ký động.

**Chương 33: State**

- **Tổng quan**: Là một mẫu thiết kế hành vi, cho phép một đối tượng **thay đổi hành vi của nó dựa trên trạng thái nội bộ của nó**, khiến nó dường như đã thay đổi lớp của nó. Khái niệm này liên quan chặt chẽ đến Finite-State Machine.
- **Vấn đề**: Triển khai máy trạng thái bằng các toán tử điều kiện (như if hoặc switch) có thể dẫn đến mã phức tạp và khó bảo trì khi số lượng trạng thái tăng lên.
- **Giải pháp**: Đề xuất tạo các **lớp riêng biệt cho mỗi trạng thái có thể có của một đối tượng**, đóng gói các hành vi cụ thể của trạng thái. Đối tượng chính (context) giữ tham chiếu đến đối tượng trạng thái hiện tại và ủy quyền các tác vụ liên quan đến trạng thái cho nó.
- **Ví dụ tương tự trong thế giới thực**: Các nút của điện thoại thông minh hoạt động khác nhau tùy thuộc vào trạng thái của thiết bị (mở khóa, khóa, pin yếu).
- **Cấu trúc**: `Context` (duy trì tham chiếu đến đối tượng trạng thái hiện tại và ủy quyền các tác vụ cho nó).
- **Khả năng ứng dụng**: Khi một đối tượng hành xử khác nhau tùy thuộc vào trạng thái hiện tại của nó, số lượng trạng thái lớn và mã trạng thái cụ thể thay đổi thường xuyên.
- **Ưu điểm**: Tuân thủ SRP (tách biệt mã liên quan đến các trạng thái cụ thể), OCP (cho phép thêm trạng thái mới), giảm trùng lặp mã.
- **Nhược điểm**: Có thể là quá mức cần thiết nếu máy trạng thái chỉ có ít trạng thái hoặc ít thay đổi.
- **Quan hệ**: Một phần mở rộng của Strategy nhưng cho phép thay đổi hành vi có trạng thái.

**Chương 34: Strategy**

- **Giới thiệu**: Là một mẫu thiết kế hành vi, định nghĩa một **họ các thuật toán, đóng gói mỗi thuật toán trong một lớp riêng biệt và làm cho chúng có thể hoán đổi cho nhau**.
- **Vấn đề**: Ứng dụng điều hướng ban đầu hỗ trợ lộ trình ô tô nhưng mở rộng để bao gồm các tùy chọn đi bộ, giao thông công cộng, đạp xe... dẫn đến mã cơ sở cồng kềnh và các vấn đề bảo trì.
- **Giải pháp**: Đề xuất **trích xuất các thuật toán thành các lớp riêng biệt** được gọi là strategy, cho phép lớp chính (context) ủy quyền công việc cho các đối tượng strategy này.
- **Ví dụ tương tự trong thế giới thực**: Chọn phương tiện di chuyển đến sân bay (xe buýt, taxi, xe đạp) đại diện cho các strategy khác nhau.
- **Cấu trúc**: `Context` (giữ tham chiếu đến một strategy), `Strategy Interface` (định nghĩa phương thức thực thi thuật toán), `Concrete Strategies` (thực hiện các phiên bản cụ thể của thuật toán), `Execution Method`, `Client` (tạo và gán strategy cụ thể cho context).
- **Khả năng ứng dụng**: Khi bạn cần chuyển đổi giữa nhiều thuật toán trong thời gian chạy, giúp hợp nhất các lớp tương tự, cô lập logic nghiệp vụ khỏi chi tiết triển khai thuật toán.
- **Ưu điểm**: Cho phép hoán đổi thuật toán trong thời gian chạy, cô lập chi tiết triển khai, thay thế kế thừa bằng kết tập, tuân thủ OCP.
- **Nhược điểm**: Thêm độ phức tạp nếu chỉ có ít thuật toán, yêu cầu client chọn strategy phù hợp.
- **Quan hệ**: Bridge, State, Strategy có cấu trúc tương tự nhưng giải quyết các vấn đề khác nhau. Command (tham số hóa thao tác), Decorator (thay đổi giao diện đối tượng), Template Method (dựa vào kế thừa).

**Chương 35: Template Method**

- **Tổng quan**: Là một mẫu thiết kế hành vi, **vạch ra bộ khung của một thuật toán trong một lớp cha**, cho phép các lớp con ghi đè các bước cụ thể mà không làm thay đổi cấu trúc tổng thể.
- **Vấn đề**: Trong ứng dụng khai thác dữ liệu xử lý các định dạng tài liệu khác nhau (PDF, DOC, CSV), mã trùng lặp xuất hiện giữa các lớp do sự tương đồng trong logic xử lý và phân tích dữ liệu.
- **Giải pháp**: Đề xuất chia nhỏ thuật toán thành các **bước riêng biệt, được đóng gói trong các phương thức**. Một phương thức template điều phối các lệnh gọi này, trong khi các lớp con triển khai các bước trừu tượng hoặc ghi đè các triển khai mặc định tùy chọn.
- **Các bước chính**: Tạo lớp cơ sở với phương thức template thực hiện một loạt các bước, định nghĩa một số bước là trừu tượng, thêm các hook làm điểm mở rộng.
- **Ví dụ tương tự trong thế giới thực**: Trong thiết kế kiến trúc, một kế hoạch xây dựng tiêu chuẩn cho phép sửa đổi cho nhu cầu của từng khách hàng trong khi vẫn duy trì tính toàn vẹn cấu trúc.
- **Cấu trúc**: `Abstract Class` (khai báo các phương thức đại diện cho các bước thuật toán và chứa phương thức template), `Concrete Classes` (thực hiện tất cả các bước trừu tượng nhưng không thể thay đổi phương thức template).
- **Khả năng ứng dụng**: Khi một thuật toán đơn khối có thể được phân tách thành các bước đơn giản hơn, riêng lẻ. Lý tưởng cho các lớp có thuật toán gần giống hệt nhau.
- **Ưu điểm**: Linh hoạt cho client để ghi đè các phần cụ thể của thuật toán, giảm trùng lặp mã.
- **Nhược điểm**: Client có thể bị hạn chế bởi framework của thuật toán, tiềm năng vi phạm LSP, tăng độ phức tạp bảo trì với nhiều bước hơn.
- **Quan hệ**: Factory Method là một chuyên biệt hóa của Template Method. Khác với Strategy Pattern (tập trung vào thay đổi hành vi động thông qua kết tập).

**Chương 36: Visitor**

- **Tổng quan**: Là một mẫu thiết kế hành vi, cho phép **tách biệt các thuật toán khỏi các đối tượng mà chúng hoạt động**, nâng cao tính mô-đun và linh hoạt của mã.
- **Vấn đề**: Khi cần xuất đồ thị thông tin địa lý sang XML, việc sửa đổi các lớp node hiện có để thêm phương thức xuất tiềm ẩn rủi ro. Lo ngại về lỗi tiềm ẩn và các yêu cầu trong tương lai cho các định dạng xuất khác.
- **Giải pháp**: Tạo một **lớp visitor riêng biệt để đóng gói hành vi mới**, cho phép thêm chức năng xuất XML mà không thay đổi các lớp node hiện có.
- **Double Dispatch**: Để thực thi phương thức visitor phù hợp mà không cần các điều kiện rườm rà, mẫu Visitor sử dụng kỹ thuật Double Dispatch. Đối tượng có thể chấp nhận visitor và xác định phương thức nào nên được thực thi.
- **Ví dụ tương tự trong thế giới thực**: Một đại lý bảo hiểm có thể đưa ra các chính sách khác nhau cho các tổ chức khác nhau dựa trên nhu cầu cụ thể của họ.
- **Khả năng ứng dụng**: Khi bạn có cấu trúc đối tượng phức tạp như cây Composite và muốn thực hiện các thao tác trên các phần tử khác nhau mà không sửa đổi chúng.
- **Ưu điểm**: Tuân thủ OCP (cho phép thêm hoạt động mới không sửa đổi lớp hiện có), SRP (hợp nhất nhiều hoạt động vào một lớp visitor duy nhất).
- **Nhược điểm**: Cần cập nhật tất cả các lớp visitor mỗi khi một lớp phần tử mới được thêm hoặc xóa, visitor có thể không có quyền truy cập trực tiếp vào các trường hoặc phương thức riêng tư của lớp phần tử.

---

Hy vọng bản tóm tắt chi tiết theo chương này sẽ giúp bạn hiểu rõ hơn về nội dung của cuốn sách "Dive Into Design Patterns".
