Đ<PERSON><PERSON> là tài liệu học tập toà<PERSON>, chắ<PERSON> lọc tinh hoa từ quyển sách "Dive Into Design Patterns" c<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> thiết kế để trở thành nguồn tham khảo nền tảng và "gối đầu giườ<PERSON>" cho sự nghiệp lập trình phần mềm của bạn. Tài liệu này tập trung vào các khái niệm cốt lõi, tư duy thiết kế sâu sắc và các nguyên tắc quan trọng nhất.

---

# Tinh Hoa Tư Duy Thiết Kế Phần Mềm: Từ Nền Tảng Đến Các Mẫu Thiết Kế Hiện Đại

## Mục Lục Tổng Quan

1.  **Lời Giới Thiệu**
2.  **Nền Tảng Tư Duy Lập Trình <PERSON>ướng Đố<PERSON> (OOP)**
    - <PERSON><PERSON><PERSON>ản của OOP
    - Bốn Trụ <PERSON>t của OOP
    - <PERSON><PERSON>ệ Gi<PERSON>a Các <PERSON>i Tượng
3.  **Các Nguyên Tắc Thiết Kế Phần Mềm Cốt Lõi**
    - Đặc Trưng của Một Thiết Kế Tốt
    - Encapsulate What Varies (Đóng Gói Phần Thay Đổi)
    - Program to an Interface, Not an Implementation (Lập Trình Theo Giao Diện, Không Theo Thực Thi Cụ Thể)
    - Favor Composition Over Inheritance (Ưu Tiên Thành Phần Hơn Kế Thừa)
    - SOLID Principles:
      - Single Responsibility Principle (SRP)
      - Open/Closed Principle (OCP)
      - Liskov Substitution Principle (LSP)
      - Interface Segregation Principle (ISP)
      - Dependency Inversion Principle (DIP)
4.  **Tổng Hợp Các Design Pattern Quan Trọng**
    - Nhóm Creational Patterns (Các Mẫu Khởi Tạo)
      - Factory Method
      - Abstract Factory
      - Builder
      - Prototype
      - Singleton
    - Nhóm Structural Patterns (Các Mẫu Cấu Trúc)
      - Adapter
      - Bridge
      - Composite
      - Decorator
      - Facade
      - Flyweight
      - Proxy
    - Nhóm Behavioral Patterns (Các Mẫu Hành Vi)
      - Chain of Responsibility
      - Command
      - Iterator
      - Mediator
      - Memento
      - Observer
      - State
      - Strategy
      - Template Method
      - Visitor
5.  **Tư Duy & Triết Lý Phần Mềm Đích Thực**

---

## 1. Lời Giới Thiệu

"Dive Into Design Patterns" của Alexander Shvets là một kim chỉ nam thiết yếu đưa bạn vào thế giới phức tạp của kiến trúc phần mềm, biến các lý thuyết trừu tượng thành những hiểu biết thực tế, có thể hành động được. Cuốn sách này không chỉ cung cấp các giải pháp đã được kiểm chứng cho các vấn đề thiết kế phổ biến mà còn giúp bạn hình thành một tư duy linh hoạt và sáng tạo trong lập trình. Mục tiêu của tài liệu này là chắt lọc những "tinh hoa" đó, giúp bạn có một nền tảng vững chắc để xây dựng và refactoring các hệ thống phần mềm thanh lịch và bền bỉ.

---

## 2. Nền Tảng Tư Duy Lập Trình Hướng Đối Tượng (OOP)

OOP là một **mô hình lập trình tổ chức dữ liệu và hành vi thành các "đối tượng"**, được tạo ra từ các "lớp" (class) – những bản thiết kế do lập trình viên định nghĩa.

### Các Khái Niệm Cơ Bản của OOP

- **Đối Tượng và Lớp (Objects and Classes):** Một **lớp** là một bản thiết kế định nghĩa cấu trúc cho các đối tượng. **Đối tượng** là các thể hiện cụ thể của lớp, gói gọn các mảnh dữ liệu (trường - fields) và hành vi liên quan đến dữ liệu đó (phương thức - methods). Ví dụ, Oscar là một đối tượng thuộc lớp Cat, chia sẻ các thuộc tính (tên, tuổi) và hành vi (thở, ăn) chung của loài mèo.
- **Phân Cấp Lớp (Class Hierarchies):** Các ứng dụng thường chứa nhiều lớp được tổ chức thành các hệ thống phân cấp. Một **lớp cha (superclass)** như `Animal` có thể bao gồm các thuộc tính và hành vi chung cho các **lớp con (subclass)** như `Cat` và `Dog`, cho phép chúng kế thừa các đặc tính đồng thời định nghĩa các phương thức độc đáo của riêng mình.
- **Ghi Đè Phương Thức (Method Overriding):** Các lớp con có khả năng ghi đè các phương thức được kế thừa từ lớp cha để thay thế hoàn toàn hành vi mặc định hoặc bổ sung thêm chức năng.

### Bốn Trụ Cột của OOP

OOP được xây dựng trên bốn khái niệm nền tảng chính:

- **Trừu Tượng (Abstraction):** Là quá trình **mô hình hóa các đối tượng trong thế giới thực bằng cách tập trung vào các thuộc tính và hành vi có liên quan** trong một ngữ cảnh cụ thể, bỏ qua các chi tiết không cần thiết. Ví dụ, trong ứng dụng đặt vé máy bay, lớp `Airplane` chỉ cần thông tin về chỗ ngồi, không cần chi tiết động cơ. **Key Point:** Trừu tượng hóa giúp bạn tập trung vào các khía cạnh thiết yếu khi thiết kế hệ thống phần mềm.
- **Đóng Gói (Encapsulation):** Đề cập đến việc **ẩn giấu các hoạt động bên trong của một đối tượng và chỉ phơi bày một giao diện đơn giản để tương tác**. Việc khởi động động cơ ô tô chỉ yêu cầu xoay chìa khóa hoặc nhấn nút, che giấu các thao tác phức tạp bên trong. Nguyên tắc này đảm bảo rằng tương tác với các đối tượng vẫn đơn giản và trực tiếp.
- **Kế Thừa (Inheritance):** Cho phép tạo các lớp mới bằng cách mở rộng các lớp hiện có, thúc đẩy việc **tái sử dụng mã nguồn**. Một lớp con có thể kế thừa các thuộc tính và phương thức từ một lớp cha và thêm chức năng bổ sung mà không cần lặp lại mã. Tuy nhiên, các lớp con phải duy trì cùng giao diện với lớp cha và triển khai tất cả các phương thức trừu tượng, ngay cả khi chúng có vẻ không liên quan.
- **Đa Hình (Polymorphism):** Cho phép chương trình xác định lớp cụ thể của một đối tượng và gọi các phương thức của nó mà không cần biết chính xác kiểu của nó trước. Ví dụ, nếu một túi chứa cả mèo và chó, một phương thức để tạo âm thanh có thể được gọi trên một tham chiếu `Animal`, và âm thanh chính xác sẽ được tạo ra dựa trên lớp thực tế của đối tượng. Điều này cho phép các đối tượng hành xử như thể chúng là các thể hiện của các lớp hoặc giao diện tương thích, bất kể danh tính thực sự của chúng.

### Quan Hệ Giữa Các Đối Tượng

Trong OOP, các lớp không tồn tại độc lập mà tương tác với nhau thông qua nhiều loại quan hệ:

- **Association (Kết Hợp):** Là mối quan hệ mà một đối tượng sử dụng hoặc tương tác với một đối tượng khác. Biểu thị bằng mũi tên trong UML.
- **Dependency (Phụ Thuộc):** Một dạng yếu hơn của kết hợp, không ngụ ý liên kết vĩnh viễn giữa các đối tượng. Thường xảy ra khi một đối tượng sử dụng đối tượng khác làm tham số phương thức hoặc khởi tạo nó. Thay đổi trong định nghĩa của một đối tượng có thể yêu cầu sửa đổi ở đối tượng khác.
- **Composition (Thành Phần):** Biểu thị mối quan hệ "toàn thể-bộ phận", nơi một đối tượng chứa được cấu thành từ một hoặc nhiều thành phần. **Sự tồn tại của thành phần phụ thuộc vào đối tượng chứa**.
- **Aggregation (Tập Hợp):** Là một phiên bản lỏng lẻo hơn của thành phần, nơi một đối tượng giữ một tham chiếu đến một đối tượng khác mà không kiểm soát vòng đời của nó. **Các thành phần có thể tồn tại độc lập với đối tượng chứa** và có thể là một phần của nhiều đối tượng chứa.

---

## 3. Các Nguyên Tắc Thiết Kế Phần Mềm Cốt Lõi

Các mẫu thiết kế (design patterns) là những giải pháp điển hình cho các vấn đề thường gặp trong thiết kế phần mềm, đóng vai trò như những bản thiết kế có thể tùy chỉnh cho các vấn đề thiết kế lặp đi lặp lại. Không giống như thuật toán (cung cấp các hành động chính xác để đạt được mục tiêu), mẫu thiết kế là các khái niệm cấp cao cần được điều chỉnh cho phù hợp với từng ngữ cảnh cụ thể.

### Đặc Trưng của Một Thiết Kế Tốt

Trước khi đi sâu vào các mẫu thiết kế, điều cần thiết là phải khám phá các đặc điểm của kiến trúc phần mềm hiệu quả, tập trung vào các mục tiêu cần theo đuổi và những cạm bẫy cần tránh.

- **Tái Sử Dụng Mã (Code Reuse):** Là một chiến lược quan trọng để giảm chi phí phát triển và tăng tốc thời gian đưa sản phẩm ra thị trường. Các vấn đề như **kết nối chặt chẽ (tight coupling)** giữa các thành phần, phụ thuộc vào các lớp cụ thể thay vì giao diện, và các hoạt động được mã hóa cứng cản trở tính linh hoạt và làm phức tạp việc tái sử dụng. Việc triển khai các mẫu thiết kế có thể nâng cao tính linh hoạt của thành phần, giúp việc tái sử dụng dễ quản lý hơn.
- **Khả Năng Mở Rộng (Extensibility):** Thay đổi là một khía cạnh không thể tránh khỏi của lập trình. Nhà phát triển thường xuyên phải đối mặt với các yêu cầu điều chỉnh do yêu cầu phát triển, xu hướng thị trường hoặc sự hiểu biết về vấn đề được cải thiện theo thời gian.

### Encapsulate What Varies (Đóng Gói Phần Thay Đổi)

- **Tổng quan:** Nguyên tắc này tập trung vào việc **xác định các khía cạnh của một ứng dụng có thể thay đổi và tách chúng khỏi các phần cố định** để giảm thiểu tác động của những thay đổi đó.
- **Tư duy:** Hãy tưởng tượng chương trình của bạn là một con tàu, và những thay đổi là những quả thủy lôi đáng sợ ẩn dưới nước. Khi bị thủy lôi tấn công, con tàu chìm. Biết điều này, bạn có thể chia thân tàu thành các khoang độc lập có thể được niêm phong an toàn để hạn chế thiệt hại cho một khoang duy nhất. Bằng cách cô lập các phần thay đổi vào các module, mã nguồn tổng thể vẫn không bị ảnh hưởng, giúp việc triển khai và kiểm thử sửa đổi dễ dàng hơn.
- **Ưu điểm:** Giúp quản lý độ phức tạp và cải thiện khả năng bảo trì trong thiết kế phần mềm. Khi các thành phần có khả năng thay đổi được cô lập, bạn có thể thực hiện các sửa đổi nhanh hơn mà không cần kiểm thử rộng rãi trên toàn bộ mã nguồn.
- **Nhược điểm/Anti-pattern:** Việc cô lập quá mức có thể dẫn đến một kiến trúc phức tạp và khó hiểu, thay vì nâng cao nó. Nguyên tắc này nên được áp dụng một cách thận trọng, có tính đến những nhược điểm tiềm ẩn của việc **thiết kế quá mức (over-engineering)**.

### Program to an Interface, Not an Implementation (Lập Trình Theo Giao Diện, Không Theo Thực Thi Cụ Thể)

- **Tổng quan:** Nguyên tắc này nhấn mạnh việc **lập trình theo một giao diện (interface) thay vì một triển khai cụ thể (concrete implementation)**, ủng hộ tính linh hoạt và khả năng mở rộng trong thiết kế. **Key Point:** "Phụ thuộc vào các khái niệm trừu tượng, không phải vào các lớp cụ thể".
- **Tư duy:** Một thiết kế được coi là linh hoạt nếu nó có thể được mở rộng mà không làm hỏng mã hiện có. Để thúc đẩy sự hợp tác giữa các lớp, hãy xác định các phương thức mà một đối tượng yêu cầu từ đối tượng khác, tạo một giao diện hoặc lớp trừu tượng mới cho các phương thức này, sau đó làm cho lớp phụ thuộc triển khai giao diện này.
- **Ưu điểm:** Mặc dù những thay đổi ban đầu có vẻ làm phức tạp mã nguồn, nhưng chúng đặt nền móng cho các tiện ích và khả năng thích ứng tiềm năng sẽ mang lại lợi ích cho người dùng mã trong tương lai. Lớp `Company` trở nên độc lập với các lớp nhân viên khác nhau, cho phép mở rộng và giới thiệu các loại công ty và nhân viên mới trong khi vẫn tái sử dụng một phần của lớp cơ sở. **Key Point:** Lập trình theo giao diện giúp tăng tính linh hoạt và khả năng thích ứng trong thiết kế phần mềm.
- **Nhược điểm:** Sau khi trích xuất giao diện, mã có thể trở nên phức tạp hơn vì nó yêu cầu cấu trúc bổ sung cho việc định nghĩa và triển khai các giao diện khác nhau, mang lại các lớp trừu tượng trước đây không có.

### Favor Composition Over Inheritance (Ưu Tiên Thành Phần Hơn Kế Thừa)

- **Tổng quan:** Kế thừa là một phương pháp phổ biến để tái sử dụng mã giữa các lớp. Tuy nhiên, nó đi kèm với một số hạn chế. **Composition** là một giải pháp thay thế, nhấn mạnh mối quan hệ "có một" (has a) thay vì "là một" (is a).
- **Hạn chế của Kế thừa:**
  - **Hạn chế giao diện:** Lớp con không thể giảm giao diện của lớp cha, buộc phải triển khai tất cả các phương thức trừu tượng, ngay cả khi không sử dụng.
  - **Vấn đề đóng gói:** Kế thừa có thể phá vỡ tính đóng gói, vì việc lớp con truy cập vào các chi tiết của lớp cha làm ảnh hưởng đến việc ẩn dữ liệu nội bộ.
  - **Kết nối chặt chẽ:** Các lớp con được kết nối chặt chẽ với các lớp cha, dẫn đến khả năng chức năng của lớp con bị hỏng khi lớp cha thay đổi.
  - **Phân cấp phức tạp:** Tái sử dụng mã thông qua kế thừa có thể tạo ra các hệ thống phân cấp kế thừa song song phức tạp, làm tăng độ khó quản lý lớp.
- **Ưu điểm của Composition:** Cho phép một lớp ủy quyền các hành vi nhất định cho các lớp khác, thúc đẩy tính linh hoạt và khả năng thích ứng mà không gặp phải nhược điểm của việc kết nối chặt chẽ. Giúp ngăn chặn sự bùng nổ của các lớp con.

### SOLID Principles

SOLID là bộ 5 nguyên tắc thiết kế quan trọng, giúp tạo ra phần mềm dễ hiểu, linh hoạt và bảo trì.

#### Single Responsibility Principle (SRP - Nguyên Tắc Đơn Nhiệm)

- **Bản chất:** Một lớp **chỉ nên có một lý do để thay đổi**, tập trung vào một phần chức năng duy nhất của phần mềm, được đóng gói hoàn toàn trong lớp.
- **Mục tiêu:** Giảm độ phức tạp, đặc biệt khi chương trình phát triển về kích thước.
- **Tư duy:** Nếu một lớp làm quá nhiều việc, bạn phải thay đổi nó mỗi khi một trong những việc đó thay đổi. Nếu bạn gặp khó khăn trong việc duy trì sự tập trung vào các khía cạnh cụ thể của chương trình, hãy xem xét liệu đã đến lúc refactor các lớp dựa trên nguyên tắc đơn nhiệm hay chưa. **Key Point:** Tập trung vào việc duy trì tính cố kết cao trong các lớp để nâng cao khả năng bảo trì phần mềm.
- **Nhược điểm/Anti-pattern:** Việc tuân thủ SRP có thể dẫn đến sự gia tăng quá mức số lượng lớp, gây phức tạp cho kiến trúc hệ thống thay vì đơn giản hóa nó. Nó có thể vô tình tạo ra sự gián tiếp không cần thiết, làm cho mối quan hệ và tương tác giữa các lớp khó theo dõi hơn. Hơn nữa, SRP có thể không tính đến các tình huống mà nhiều trách nhiệm tự nhiên xung đột trong một lớp duy nhất dựa trên logic nghiệp vụ.

#### Open/Closed Principle (OCP - Nguyên Tắc Mở/Đóng)

- **Bản chất:** Các lớp **nên mở để mở rộng nhưng đóng để sửa đổi**. Nguyên tắc này nhằm ngăn chặn mã hiện có bị hỏng khi các tính năng mới được triển khai.
- **Khái niệm "Mở":** Một lớp "mở" nếu bạn có thể mở rộng nó, tạo một lớp con và thêm các phương thức mới hoặc ghi đè hành vi.
- **Khái niệm "Đóng":** Một lớp "đóng" (hoặc hoàn chỉnh) khi nó được định nghĩa đầy đủ và sẵn sàng để các lớp khác sử dụng, với một giao diện rõ ràng sẽ không thay đổi trong tương lai.
- **Tư duy:** Thay đổi trực tiếp mã của một lớp có thể gây rủi ro làm hỏng chức năng hiện có, đặc biệt nếu nó đã được phát triển và kiểm thử. Thay vào đó, người ta nên tạo các lớp con để sửa đổi hành vi mà không ảnh hưởng đến lớp gốc. **Lưu ý:** Nguyên tắc này không áp dụng cho tất cả các thay đổi. Nếu một lỗi được xác định, nó nên được sửa trực tiếp trong lớp mà không cần tạo lớp con.
- **Ưu điểm:** Cho phép thêm các tính năng mới vào một lớp mà không làm thay đổi mã hiện có, bảo vệ tính toàn vẹn và ngăn ngừa lỗi.
- **Nhược điểm/Anti-pattern:** Ứng dụng thực tế của OCP có thể dẫn đến sự phức tạp mâu thuẫn với sự đơn giản mà nó đề xuất. Việc sử dụng lớp con để mở rộng chức năng một cách an toàn có thể dẫn đến **thiết kế quá mức (over-engineering)** và sự gia tăng quá mức số lượng lớp, làm phức tạp cả mã nguồn và việc phát triển trong tương lai.

#### Liskov Substitution Principle (LSP - Nguyên Tắc Thay Thế Liskov)

- **Bản chất:** Các đối tượng của một lớp con **nên có thể thay thế cho các đối tượng của lớp cha mà không làm thay đổi tính đúng đắn** của chương trình.
- **Hướng dẫn chính:**
  - **Tương thích kiểu tham số:** Tham số của phương thức trong lớp con phải khớp hoặc trừu tượng hơn so với lớp cha.
  - **Tương thích kiểu trả về:** Kiểu trả về của phương thức trong lớp con phải khớp hoặc là kiểu con của kiểu trả về của phương thức lớp cha.
  - **Xử lý ngoại lệ:** Phương thức lớp con không được ném ra các ngoại lệ mà phương thức lớp cha không mong đợi.
  - **Bảo toàn tiền điều kiện:** Lớp con không được tăng cường các tiền điều kiện của các phương thức của lớp cha.
  - **Bảo toàn hậu điều kiện:** Lớp con không được làm suy yếu các hậu điều kiện của các phương thức của lớp cha.
  - **Bảo toàn bất biến:** Các điều kiện bất biến của lớp cha phải được bảo toàn trong lớp con.
  - **Tính toàn vẹn trường private:** Lớp con không được thay đổi giá trị của các trường private từ lớp cha thông qua reflection.
- **Tư duy:** LSP là một tập hợp các kiểm tra giúp dự đoán liệu một lớp con có tương thích với mã có thể làm việc với các đối tượng của lớp cha hay không. Nó đảm bảo tính tương thích của các lớp con với các lớp cha của chúng, điều này rất quan trọng để duy trì mã nguồn phụ thuộc vào các lớp này, đặc biệt trong các thư viện và framework.
- **Ví dụ về vi phạm:** Một lớp con `ReadOnlyDocument` ném ra một ngoại lệ trong phương thức `save` bị ghi đè của nó, trong khi lớp cha không làm vậy.

#### Interface Segregation Principle (ISP - Nguyên Tắc Phân Tách Giao Diện)

- **Bản chất:** **Client không nên bị buộc phụ thuộc vào các phương thức mà họ không sử dụng.** Nên tạo các giao diện hẹp để đảm bảo rằng các lớp client chỉ triển khai các hành vi cần thiết.
- **Tư duy:** Việc tuân theo ISP có nghĩa là biến các giao diện "fat" (quá lớn) thành các giao diện chi tiết và cụ thể hơn. Điều này cho phép client chỉ triển khai các phương thức mà họ thực sự cần, ngăn ngừa sự gián đoạn không cần thiết từ các thay đổi trong các giao diện rộng hơn có thể ảnh hưởng đến client không sử dụng các phương thức đã thay đổi. Một lớp có thể triển khai nhiều giao diện đồng thời, vì vậy không cần phải làm quá tải một giao diện duy nhất với các phương thức không liên quan.
- **Ưu điểm:** Nâng cao tính mô đun hóa hệ thống và tránh buộc client phải phụ thuộc vào những thứ không cần thiết.
- **Nhược điểm/Anti-pattern:** Việc phân chia giao diện quá mức có thể dẫn đến sự phức tạp không cần thiết trong mã nguồn. Nếu có quá nhiều giao diện, hệ thống có thể trở nên khó bảo trì và khó hiểu hơn. Phải duy trì sự cân bằng.

#### Dependency Inversion Principle (DIP - Nguyên Tắc Đảo Ngược Phụ Thuộc)

- **Bản chất:** Các lớp cấp cao (high-level classes) **không nên phụ thuộc vào các lớp cấp thấp (low-level classes). Cả hai nên phụ thuộc vào các khái niệm trừu tượng (abstractions).** Các khái niệm trừu tượng không nên phụ thuộc vào các chi tiết. Các chi tiết nên phụ thuộc vào các khái niệm trừu tượng.
- **Mục tiêu:** Nâng cao thiết kế phần mềm bằng cách thúc đẩy sự tách biệt các mối quan tâm giữa các lớp cấp cao và cấp thấp.
- **Vấn đề với thiết kế truyền thống:** Thường thì các lớp cấp thấp được phát triển trước, dẫn đến các lớp cấp cao phụ thuộc quá mức vào các triển khai và chi tiết cấp thấp.
- **Hướng dẫn nguyên tắc:**
  1.  **Định nghĩa giao diện:** Tạo giao diện cho các hoạt động cấp thấp mà các lớp cấp cao có thể sử dụng.
  2.  **Phụ thuộc vào giao diện:** Các lớp cấp cao nên phụ thuộc vào các giao diện đã định nghĩa này thay vì các lớp cấp thấp cụ thể.
  3.  **Triển khai bởi các lớp cấp thấp:** Sau khi định nghĩa giao diện, các lớp cấp thấp có thể triển khai chúng, do đó **đảo ngược hướng phụ thuộc ban đầu**.
- **Mối quan hệ với OCP:** DIP thường tương quan với OCP, cho phép các lớp cấp thấp được mở rộng với logic nghiệp vụ mới mà không làm thay đổi mã hiện có.
- **Ưu điểm:** Tăng cường khả năng bảo trì mã, cải thiện khả năng kiểm thử và tăng tính linh hoạt. Bằng cách tách các lớp cấp cao khỏi các triển khai cấp thấp, việc thay đổi hoặc sửa đổi các thành phần trở nên dễ dàng hơn mà không ảnh hưởng đến toàn bộ hệ thống.

---

## 4. Tổng Hợp Các Design Pattern Quan Trọng

Các mẫu thiết kế được phân loại theo mục đích thành: **Creational (Khởi tạo), Structural (Cấu trúc), và Behavioral (Hành vi)**.

### Nhóm Creational Patterns (Các Mẫu Khởi Tạo)

Tập trung vào việc tạo đối tượng để tăng tính linh hoạt và khả năng tái sử dụng.

#### 1. Factory Method (Phương Thức Nhà Máy)

- **Bản chất/Mục đích:** Là một mẫu thiết kế khởi tạo cung cấp một giao diện để tạo đối tượng trong một lớp cha trong khi cho phép các lớp con quyết định chính xác loại đối tượng sẽ được tạo. Nó giải quyết vấn đề mã kết nối chặt chẽ với các lớp cụ thể, gây khó khăn khi thêm các loại đối tượng mới mà không cần sửa đổi mã nguồn hiện có.
- **Tình huống sử dụng:**
  - Khi bạn không biết trước các kiểu và phụ thuộc chính xác của các đối tượng mà mã của bạn nên làm việc.
  - Bạn muốn cho phép người dùng của một thư viện hoặc framework mở rộng các thành phần nội bộ của nó.
  - Bạn cần tiết kiệm tài nguyên hệ thống bằng cách tái sử dụng các đối tượng hiện có thay vì tạo các thể hiện mới.
- **Cấu trúc chính:**
  - **Product:** Khai báo một giao diện chung cho tất cả các triển khai sản phẩm.
  - **Concrete Products:** Các triển khai của giao diện sản phẩm.
  - **Creator (Lớp tạo):** Khai báo phương thức factory sẽ trả về các đối tượng sản phẩm, thường bao gồm logic nghiệp vụ cốt lõi.
  - **Concrete Creators:** Các lớp con triển khai phương thức factory để tạo các loại sản phẩm cụ thể.
- **Ưu điểm:** Tránh kết nối chặt chẽ giữa lớp tạo và các sản phẩm cụ thể. Áp dụng SRP bằng cách tập trung việc tạo sản phẩm. Hỗ trợ OCP, cho phép giới thiệu các loại sản phẩm mới mà không làm hỏng mã client hiện có.
- **Nhược điểm:** Có thể dẫn đến tăng độ phức tạp của mã do cần tạo nhiều lớp con để triển khai mẫu một cách hiệu quả.
- **Tư duy thiết kế:** Phương thức nhà máy tách biệt mã tạo sản phẩm khỏi mã thực sự sử dụng sản phẩm. Nó cho phép các lớp con thay đổi lớp của các đối tượng được trả về bởi phương thức nhà máy.

#### 2. Abstract Factory (Nhà Máy Trừu Tượng)

- **Bản chất/Mục đích:** Là một mẫu thiết kế khởi tạo cho phép tạo ra các họ đối tượng liên quan mà không cần chỉ định các lớp cụ thể của chúng. Nó giúp đảm bảo các sản phẩm phù hợp về kiểu dáng và hệ thống vẫn linh hoạt khi các sản phẩm hoặc họ sản phẩm mới được thêm vào mà không cần thay đổi mã hiện có.
- **Tình huống sử dụng:** Khi bạn xử lý các họ sản phẩm mà các lớp cụ thể nên được ẩn khỏi client để tăng khả năng mở rộng.
- **Cấu trúc chính:**
  - **Abstract Products:** Giao diện cho các sản phẩm liên quan trong một họ.
  - **Concrete Products:** Các triển khai của các sản phẩm trừu tượng, được phân loại theo biến thể.
  - **Abstract Factory:** Giao diện với các phương thức để tạo các sản phẩm trừu tượng.
  - **Concrete Factories:** Triển khai các phương thức tạo cho mỗi biến thể sản phẩm, đảm bảo tính tương thích.
  - **Client Interaction:** Client làm việc với các sản phẩm thông qua các kiểu trừu tượng, tránh kết nối chặt chẽ với các biến thể sản phẩm cụ thể.
- **Ưu điểm:** Đảm bảo tính tương thích của sản phẩm. Giảm kết nối chặt chẽ giữa sản phẩm và mã client. Hỗ trợ tuân thủ SRP và OCP.
- **Nhược điểm:** Có thể làm tăng độ phức tạp của mã do có thêm các giao diện và lớp.
- **Tư duy thiết kế:** Client không nên quan tâm đến lớp cụ thể của nhà máy mà nó làm việc. Mẫu này đảm bảo rằng tất cả các mục từ một nhà máy sẽ khớp và duy trì một phong cách nhất quán. Nó cho phép bạn giới thiệu các biến thể sản phẩm mới mà không làm hỏng mã client hiện có.

#### 3. Builder (Bộ Xây Dựng)

- **Bản chất/Mục đích:** Là một mẫu thiết kế khởi tạo cho phép **xây dựng các đối tượng phức tạp từng bước**. Nó cung cấp một cách để tạo ra các cấu hình khác nhau của một đối tượng mà không gây nhầm lẫn từ nhiều tham số hoặc lớp con. Nó giải quyết vấn đề tạo đối tượng phức tạp thường dẫn đến các constructor phức tạp hoặc nhiều lớp con cho mọi cấu hình có thể.
- **Tình huống sử dụng:**
  - Để loại bỏ các constructor rườm rà với nhiều tham số tùy chọn.
  - Để tạo các biểu diễn đa dạng của một sản phẩm.
  - Để xây dựng các cây composite hoặc các cấu trúc phức tạp khác yêu cầu cách tiếp cận từng bước.
- **Cấu trúc chính:**
  - **Builder Interface:** Khai báo các bước xây dựng chung.
  - **Concrete Builders:** Triển khai các bước xây dựng và có thể tạo ra các sản phẩm khác nhau không chia sẻ cùng một hệ thống phân cấp lớp.
  - **Products:** Các đối tượng kết quả từ các builder.
  - **Director Class:** Quản lý thứ tự các bước xây dựng.
  - **Client:** Liên kết một builder với director và khởi tạo quá trình xây dựng.
- **Ưu điểm:** Cho phép xây dựng từng bước, thúc đẩy tái sử dụng, hỗ trợ SRP.
- **Nhược điểm:** Làm tăng độ phức tạp tổng thể của mã do có nhiều lớp được tạo ra hơn.
- **Tư duy thiết kế:** Mẫu Builder đề xuất rằng bạn trích xuất mã xây dựng đối tượng ra khỏi lớp của chính nó và chuyển nó sang các đối tượng riêng biệt gọi là builder. Các builder khác nhau thực hiện cùng một nhiệm vụ theo nhiều cách khác nhau. Director biết các bước xây dựng nào cần thực hiện để có được một sản phẩm hoạt động. Nó tách biệt logic xây dựng khỏi logic nghiệp vụ của sản phẩm.

#### 4. Prototype (Nguyên Mẫu)

- **Bản chất/Mục đích:** Là một mẫu thiết kế khởi tạo cho phép **sao chép các đối tượng hiện có mà không cần mã phụ thuộc vào các lớp của chúng**. Nó giải quyết vấn đề sao chép một đối tượng theo cách truyền thống đòi hỏi kiến thức về lớp của nó và quyền truy cập vào các trường của nó, điều này có thể gây ra vấn đề do các trường private và sự phụ thuộc vào các lớp cụ thể.
- **Tình huống sử dụng:** Khi mã của bạn không nên phụ thuộc vào các lớp cụ thể của các đối tượng mà bạn cần sao chép.
- **Cấu trúc chính:**
  - **Prototype Interface:** Khai báo phương thức cloning (`clone`).
  - **Concrete Prototype Class:** Triển khai phương thức cloning trong khi xử lý các trường hợp đặc biệt cụ thể.
  - **Client:** Sử dụng giao diện prototype để tạo bản sao đối tượng.
- **Ưu điểm:** Giảm kết nối giữa mã và các lớp cụ thể. Loại bỏ mã khởi tạo lặp lại. Cung cấp sự tiện lợi trong việc tạo các đối tượng phức tạp. Một giải pháp thay thế cho kế thừa để cấu hình các đối tượng phức tạp.
- **Nhược điểm:** Sao chép các đối tượng phức tạp với các tham chiếu vòng tròn có thể rất khó khăn và có thể yêu cầu xử lý bổ sung.
- **Tư duy thiết kế:** Mẫu Prototype ủy quyền quá trình cloning cho chính các đối tượng được clone. Nó cho phép bạn tạo một bản sao của một đối tượng mà không biết bất cứ điều gì về kiểu của nó. Các nguyên mẫu được xây dựng sẵn có thể là một giải pháp thay thế cho việc sử dụng lớp con.

#### 5. Singleton (Đơn Thể)

- **Bản chất/Mục đích:** Là một mẫu thiết kế khởi tạo đảm bảo **một lớp chỉ có một thể hiện duy nhất** trong khi cung cấp một điểm truy cập toàn cục đến thể hiện đó. Nó giải quyết hai vấn đề chính, mặc dù nó vi phạm SRP:
  1.  **Kiểm soát thể hiện duy nhất:** Đảm bảo chỉ có một thể hiện của một lớp, thường được sử dụng để điều chỉnh quyền truy cập vào các tài nguyên được chia sẻ như cơ sở dữ liệu hoặc tệp.
  2.  **Điểm truy cập toàn cục:** Cung cấp một điểm truy cập an toàn, không giống như các biến toàn cục có thể được sửa đổi ở bất kỳ đâu trong mã.
- **Tình huống sử dụng:** Khi một lớp trong chương trình của bạn chỉ nên có một thể hiện duy nhất có sẵn cho tất cả các client; ví dụ, một đối tượng cơ sở dữ liệu duy nhất được chia sẻ bởi các phần khác nhau của chương trình.
- **Cấu trúc chính:** Lớp Singleton khai báo một phương thức tĩnh `getInstance` để trả về thể hiện duy nhất của chính nó trong khi ẩn constructor.
- **Ưu điểm:** Đảm bảo một lớp có một thể hiện duy nhất. Cung cấp quyền truy cập toàn cục. Thể hiện được khởi tạo khi được yêu cầu (lazy initialization).
- **Nhược điểm:** Vi phạm SRP bằng cách giải quyết hai vấn đề. Có thể cho thấy mối quan hệ thiết kế kém giữa các thành phần. Yêu cầu xử lý đặc biệt trong các kịch bản đa luồng để ngăn chặn việc tạo nhiều lần. Kiểm thử có thể phức tạp do các constructor private.
- **Tư duy thiết kế:** Singleton là một thực thể duy nhất đại diện cho quyền lực, có thể truy cập toàn cầu (ví dụ: chính phủ). Mặc dù tương tự như biến toàn cục trong việc cung cấp truy cập toàn cầu, Singleton đảm bảo chỉ có một thể hiện tồn tại và bảo vệ nó khỏi bị ghi đè.

### Nhóm Structural Patterns (Các Mẫu Cấu Trúc)

Mô tả cách lắp ráp các đối tượng và lớp thành các cấu trúc linh hoạt.

#### 6. Adapter (Bộ Chuyển Đổi)

- **Bản chất/Mục đích:** Là một mẫu thiết kế cấu trúc cho phép **sự hợp tác giữa các đối tượng có giao diện không tương thích**. Nó giải quyết vấn đề khi cần tích hợp một thư viện phân tích của bên thứ ba chỉ chấp nhận dữ liệu ở định dạng JSON, trong khi ứng dụng của bạn hoạt động với XML. Thay đổi thư viện hoặc định dạng XML có thể làm hỏng mã hiện có.
- **Tình huống sử dụng:**
  - Bạn muốn sử dụng một lớp hiện có với một giao diện không tương thích.
  - Bạn muốn tái sử dụng các lớp con thiếu chức năng chung mà không lặp lại mã.
- **Cấu trúc chính:**
  - **Client:** Lớp với logic nghiệp vụ hiện có.
  - **Client Interface:** Định nghĩa giao thức để hợp tác.
  - **Service:** Lớp hữu ích với giao diện không tương thích.
  - **Adapter:** Triển khai giao diện client và gói đối tượng service, dịch các cuộc gọi giữa client và service.
- **Ưu điểm:** Tuân thủ SRP bằng cách tách chuyển đổi giao diện khỏi logic nghiệp vụ. Tuân thủ OCP, cho phép các adapter mới mà không sửa đổi mã client.
- **Nhược điểm:** Làm tăng độ phức tạp của mã với các giao diện và lớp bổ sung.
- **Tư duy thiết kế:** Adapter gói một trong các đối tượng để ẩn đi sự phức tạp của việc chuyển đổi diễn ra "hậu trường". Nó cho phép chuyển đổi giữa hai định dạng mà không thay đổi các đối tượng hiện có. Giống như bộ chuyển đổi phích cắm điện cho phép các thiết bị kết nối với các tiêu chuẩn ổ cắm khác nhau.

#### 7. Bridge (Cầu Nối)

- **Bản chất/Mục đích:** Là một cách tiếp cận thiết kế cấu trúc tách biệt một lớp lớn hoặc một tập hợp các lớp liên quan thành **hai hệ thống phân cấp riêng biệt: trừu tượng và triển khai**, cho phép phát triển độc lập. Nó giải quyết vấn đề khi cố gắng mở rộng một hệ thống phân cấp lớp (ví dụ: `Shape` với `Circle` và `Square`) để bao gồm các thuộc tính bổ sung như màu sắc, độ phức tạp tăng theo cấp số nhân. Mỗi sự kết hợp mới đòi hỏi việc tạo ra nhiều lớp con, dẫn đến một cấu trúc lớp khó quản lý.
- **Tình huống sử dụng:** Khi bạn muốn chia và tổ chức một lớp nguyên khối có nhiều biến thể của một số chức năng.
- **Cấu trúc chính:**
  - **Abstraction (Trừu tượng):** Cung cấp logic điều khiển thông qua các tham chiếu đến các đối tượng triển khai.
  - **Implementation (Triển khai):** Khai báo các giao diện chung cho các triển khai cụ thể.
- **Ưu điểm:** Ngăn chặn sự bùng nổ của hệ thống phân cấp lớp. Dễ dàng thay đổi các module nhỏ hơn, được định nghĩa rõ ràng. Có thể giới thiệu các khái niệm trừu tượng và triển khai mới một cách độc lập.
- **Nhược điểm:** Có thể làm phức tạp mã một cách không cần thiết nếu áp dụng cho một lớp đã có tính cố kết cao.
- **Tư duy thiết kế:** Mẫu Bridge cho phép bạn chia lớp nguyên khối thành nhiều hệ thống phân cấp lớp.

#### 8. Composite (Hợp Thành)

- **Bản chất/Mục đích:** Là một mẫu thiết kế cấu trúc cho phép **thành phần các đối tượng thành cấu trúc cây**, cho phép chúng được xử lý như các đối tượng cá nhân. Nó hữu ích khi mô hình cốt lõi có thể được biểu diễn dưới dạng cây. Ví dụ, đơn hàng có thể chứa hỗn hợp sản phẩm và hộp, mỗi hộp có thể chứa thêm sản phẩm hoặc hộp khác. Tính tổng giá của các cấu trúc lồng nhau như vậy sẽ phức tạp và không thực tế nếu sử dụng cách tiếp cận trực tiếp.
- **Tình huống sử dụng:** Khi bạn phải triển khai một cấu trúc đối tượng giống cây.
- **Cấu trúc chính:**
  - **Component Interface:** Định nghĩa các hoạt động chung cho cả các phần tử đơn giản và phức tạp.
  - **Leaf:** Đại diện cho các phần tử cuối cùng không có phần tử con, thực hiện công việc thực tế.
  - **Container (Composite):** Có thể chứa các leaf hoặc các container khác. Nó ủy quyền các nhiệm vụ cho các thành phần của nó.
  - **Client:** Tương tác với tất cả các phần tử thông qua giao diện thành phần, xử lý các phần tử đơn giản và phức tạp một cách thống nhất.
- **Ưu điểm:** Đơn giản hóa việc làm việc với các cấu trúc phức tạp thông qua đa hình và đệ quy. Tuân thủ OCP, cho phép các loại phần tử mới mà không thay đổi mã hiện có.
- **Nhược điểm:** Tìm kiếm một giao diện chung có thể dẫn đến việc tổng quát hóa quá mức, làm phức tạp sự hiểu biết.
- **Tư duy thiết kế:** Mẫu Composite cho phép bạn chạy một hành vi một cách đệ quy trên tất cả các thành phần của một cây đối tượng. Bạn có thể xử lý tất cả chúng một cách giống nhau thông qua giao diện chung. Client có thể làm việc với các cấu trúc đối tượng rất phức tạp mà không bị kết nối chặt chẽ với các lớp cụ thể hình thành cấu trúc đó.

#### 9. Decorator (Trang Trí)

- **Bản chất/Mục đích:** Là một mẫu thiết kế cấu trúc cho phép **thêm các hành vi mới vào đối tượng bằng cách gói chúng trong các đối tượng đặc biệt (decorator) chứa các hành vi mong muốn**. Nó giải quyết vấn đề bùng nổ tổ hợp các lớp con khi thêm các loại thông báo bổ sung (SMS, Facebook, Slack) vào một lớp `Notifier` cơ bản chỉ gửi email.
- **Tình huống sử dụng:** Khi cần thêm các hành vi bổ sung vào thời gian chạy mà không làm thay đổi mã hiện có. Lý tưởng khi việc mở rộng thông qua kế thừa không thực tế hoặc không thể.
- **Cấu trúc chính:**
  - **Component:** Khai báo một giao diện cho cả decorator và đối tượng được gói.
  - **Concrete Component:** Triển khai hành vi cơ sở, có thể được sửa đổi bởi các decorator.
  - **Base Decorator:** Chứa tham chiếu đến đối tượng được gói và ủy quyền các hoạt động cho đối tượng này.
  - **Concrete Decorators:** Mở rộng base decorator để thêm các hành vi cụ thể.
  - **Client:** Tương tác với giao diện component để sử dụng các decorator.
- **Ưu điểm:** Mở rộng hành vi một cách động mà không cần lớp con. Kết hợp nhiều hành vi thông qua xếp chồng. Tuân thủ SRP.
- **Nhược điểm:** Khó khăn trong việc loại bỏ các decorator cụ thể khỏi một stack. Có thể phức tạp trong việc đảm bảo thứ tự của các decorator không ảnh hưởng đến hành vi.
- **Tư duy thiết kế:** Thay vì dựa vào kế thừa, giải pháp nằm ở việc sử dụng composition. Decorator cho phép kết hợp các hành vi một cách động bằng cách gói lớp `Notifier` bằng các lớp decorator, cho phép nhiều phương thức thông báo cùng tồn tại và được sử dụng đồng thời.

#### 10. Facade (Mặt Nạ)

- **Bản chất/Mục đích:** Là một mẫu thiết kế cấu trúc đơn giản hóa tương tác với các thư viện hoặc framework phức tạp bằng cách **cung cấp một giao diện rõ ràng**. Nó giải quyết vấn đề các thư viện phức tạp đòi hỏi quản lý đáng kể các đối tượng và phụ thuộc khác nhau, làm cho mã khó hiểu và bảo trì.
- **Tình huống sử dụng:** Khi bạn cần tương tác với một hệ thống phức tạp nhưng chỉ yêu cầu một phần chức năng của nó.
- **Cấu trúc chính:**
  - **Facade:** Cho phép truy cập thuận tiện vào một subsystem và điều phối các yêu cầu.
  - **Additional Facade:** Ngăn chặn sự phức tạp bằng cách tổ chức các tính năng không liên quan một cách riêng biệt.
  - **Complex Subsystem:** Bao gồm nhiều đối tượng cần quản lý phức tạp.
  - **Client:** Tương tác với các subsystem thông qua facade thay vì trực tiếp.
- **Ưu điểm:** Cô lập mã của bạn khỏi sự phức tạp của một subsystem. Cung cấp một mã nguồn sạch hơn.
- **Nhược điểm:** Có thể tạo ra một "đối tượng thần thánh" nếu lớp Facade trở nên quá lớn hoặc cố gắng xử lý quá nhiều trách nhiệm.
- **Tư duy thiết kế:** Facade cung cấp một giao diện đơn giản hóa cho một subsystem phức tạp, chỉ phơi bày các tính năng mà client cần. Nó giúp khắc phục vấn đề bằng cách cung cấp một lối tắt đến các tính năng được sử dụng nhiều nhất của subsystem phù hợp với hầu hết các yêu cầu của client.

#### 11. Flyweight (Hạng Nhẹ)

- **Bản chất/Mục đích:** Là một mẫu thiết kế cấu trúc **tối ưu hóa việc sử dụng bộ nhớ bằng cách chia sẻ các phần chung của trạng thái giữa nhiều đối tượng**, cho phép sử dụng RAM hiệu quả hơn. Nó giải quyết vấn đề trong một trò chơi điện tử có hệ thống hạt thực tế, việc tạo đối tượng quá mức (như hạt) dẫn đến mức tiêu thụ RAM cao, gây ra sự cố trên các máy yếu hơn.
- **Tình huống sử dụng:** Khi bạn xử lý số lượng lớn các đối tượng tương tự tiêu thụ nhiều RAM, đặc biệt nếu chúng chia sẻ trạng thái trùng lặp.
- **Cấu trúc chính:**
  - **Flyweight Class:** Chứa trạng thái nội tại được chia sẻ cho nhiều đối tượng.
  - **Context Class:** Giữ trạng thái ngoại tại, kết hợp nó với các đối tượng flyweight để biểu thị trạng thái đầy đủ.
  - **Client:** Quản lý trạng thái ngoại tại và tương tác với flyweight, truyền dữ liệu ngữ cảnh cần thiết trong các cuộc gọi phương thức.
  - **Flyweight Factory:** Xử lý việc tạo và truy xuất các đối tượng flyweight, đảm bảo tái sử dụng hiệu quả.
- **Ưu điểm:** Tiết kiệm bộ nhớ đáng kể với số lượng lớn các đối tượng tương tự.
- **Nhược điểm:** Tăng độ phức tạp và chi phí tiềm ẩn từ việc tính toán lại trạng thái ngoại tại.
- **Tư duy thiết kế:** Giải pháp liên quan đến việc phân biệt giữa trạng thái nội tại (intrinsic) và ngoại tại (extrinsic) của đối tượng. **Trạng thái nội tại (intrinsic state)** (dữ liệu được chia sẻ như màu sắc và sprite) vẫn nằm trong đối tượng; **trạng thái ngoại tại (extrinsic state)** (dữ liệu duy nhất như tọa độ và chuyển động) được truyền cho các phương thức và lưu trữ bên ngoài. Đối tượng Flyweight nên là **bất biến (immutable)**, chỉ được khởi tạo thông qua constructor.

#### 12. Proxy (Đại Diện)

- **Bản chất/Mục đích:** Là một mẫu thiết kế cấu trúc đóng vai trò là **người thay thế cho một đối tượng khác**, kiểm soát quyền truy cập vào đối tượng gốc và cho phép tiền hoặc hậu xử lý các yêu cầu. Nó giải quyết vấn đề kiểm soát quyền truy cập vào một đối tượng, đặc biệt là những đối tượng tốn tài nguyên. Khởi tạo lazy truyền thống có thể dẫn đến trùng lặp mã.
- **Tình huống sử dụng:**
  - **Khởi tạo Lazy (Lazy Initialization):** Trì hoãn việc tạo đối tượng nặng cho đến khi cần thiết.
  - **Kiểm soát truy cập (Access Control):** Hạn chế quyền truy cập của một số client nhất định.
  - **Thực thi cục bộ các dịch vụ từ xa:** Xử lý tương tác máy chủ từ xa.
  - **Ghi log yêu cầu:** Giữ lịch sử các yêu cầu dịch vụ.
  - **Lưu trữ kết quả (Caching):** Lưu trữ và quản lý các kết quả dịch vụ lớn.
  - **Tham chiếu thông minh:** Theo dõi và loại bỏ các đối tượng nặng khi không còn cần thiết.
- **Cấu trúc chính:**
  - **Service Interface:** Định nghĩa giao diện của dịch vụ mà proxy phải tuân theo.
  - **Service:** Triển khai cung cấp logic nghiệp vụ quan trọng.
  - **Proxy:** Giữ một tham chiếu đến đối tượng service và quản lý vòng đời của nó và bất kỳ xử lý bổ sung nào.
  - **Client:** Tương tác với cả service và proxy thông qua cùng một giao diện.
- **Ưu điểm:** Kiểm soát đối tượng service mà client không hề hay biết. Quản lý vòng đời của service. Hoạt động ngay cả khi service không có sẵn. Có thể thêm proxy mới mà không thay đổi mã hiện có.
- **Nhược điểm:** Tăng độ phức tạp với nhiều lớp hơn. Tiềm ẩn sự chậm trễ trong phản hồi của service.
- **Tư duy thiết kế:** Một thẻ tín dụng hoạt động như một proxy cho tài khoản ngân hàng, cho phép giao dịch mà không cần mang tiền mặt, quản lý cả sự tiện lợi và an toàn tài chính. Proxy tạo một lớp proxy mới chia sẻ cùng giao diện với đối tượng service gốc, ủy quyền các yêu cầu cho đối tượng service gốc, cho phép các chức năng bổ sung như khởi tạo lazy và lưu trữ kết quả mà client không biết.

### Nhóm Behavioral Patterns (Các Mẫu Hành Vi)

Giải quyết vấn đề giao tiếp và phân công trách nhiệm giữa các đối tượng.

#### 13. Chain of Responsibility (Chuỗi Trách Nhiệm)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi cho phép **chuyển các yêu cầu qua một chuỗi các trình xử lý (handler)**. Mỗi handler có thể chọn xử lý yêu cầu hoặc chuyển tiếp nó cho handler tiếp theo trong chuỗi. Nó giải quyết vấn đề khi trong một hệ thống đặt hàng trực tuyến, nhiều kiểm tra phải được thực hiện tuần tự để xác thực người dùng và dữ liệu. Khi các kiểm tra bổ sung cho bảo mật và hiệu suất được thêm vào, mã trở nên ngày càng phức tạp và khó bảo trì.
- **Tình huống sử dụng:** Khi chương trình của bạn được mong đợi xử lý các loại yêu cầu khác nhau theo nhiều cách khác nhau, nhưng các loại yêu cầu và chuỗi của chúng không được biết trước.
- **Cấu trúc chính:**
  - **Handler:** Khai báo một giao diện chung cho tất cả các handler cụ thể, thường có một phương thức để xử lý yêu cầu.
  - **Base Handler:** Triển khai logic chung cho việc xây dựng chuỗi và xử lý yêu cầu.
  - **Concrete Handlers:** Triển khai logic xử lý cụ thể.
- **Ưu điểm:** Tuân thủ SRP bằng cách tách rời các lớp gọi hoạt động khỏi các lớp thực hiện hoạt động. Hỗ trợ OCP, cho phép dễ dàng thêm các handler mới mà không sửa đổi mã hiện có.
- **Nhược điểm:** Có thể xảy ra trường hợp yêu cầu không được xử lý nếu không tìm thấy handler phù hợp. Quản lý chuỗi phức tạp có thể trở nên khó khăn. Khó khăn trong việc gỡ lỗi luồng yêu cầu qua chuỗi.
- **Tư duy thiết kế:** Mẫu Chain of Responsibility cung cấp một cách để quản lý các kiểm tra này bằng cách **đóng gói mỗi hành vi vào các đối tượng độc lập gọi là handler**. Mỗi handler được liên kết trong một chuỗi, nơi nó có thể xử lý yêu cầu và sau đó chọn chuyển nó đi hoặc kết thúc xử lý thêm. Tương tự như cuộc gọi hỗ trợ kỹ thuật đi qua nhiều cấp độ nhà điều hành, mỗi cấp độ quyết định có trực tiếp hỗ trợ hay không hoặc chuyển vấn đề lên cấp độ tiếp theo.

#### 14. Command (Lệnh)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi biến một yêu cầu thành một **đối tượng độc lập**, chứa tất cả thông tin cần thiết. Điều này cho phép tham số hóa phương thức, trì hoãn hoặc xếp hàng các yêu cầu và hỗ trợ các hoạt động có thể hoàn tác. Nó giải quyết vấn đề trong một ứng dụng soạn thảo văn bản, các nút cho các hoạt động khác nhau được tạo từ một lớp `Button` cơ sở. Tuy nhiên, việc sử dụng nhiều lớp con để xử lý các hành vi nhấp nút khác nhau dẫn đến sự phức tạp, nơi mã GUI trở nên kết nối chặt chẽ với logic nghiệp vụ.
- **Tình huống sử dụng:**
  - Khi bạn muốn tham số hóa các đối tượng bằng các hoạt động.
  - Sử dụng khi các hoạt động có thể được xếp hàng, lên lịch hoặc thực thi từ xa.
  - Triển khai cho các hoạt động có thể hoàn tác.
- **Cấu trúc chính:**
  - **Sender (Invoker):** Khởi tạo yêu cầu và giữ một tham chiếu đến một lệnh.
  - **Command Interface:** Khai báo một phương thức để thực thi lệnh.
  - **Concrete Commands:** Triển khai các yêu cầu cụ thể mà không tự thực hiện công việc.
  - **Receiver:** Chứa logic nghiệp vụ để thực hiện các hoạt động thực tế.
  - **Client:** Tạo và cấu hình các đối tượng lệnh.
- **Ưu điểm:** Tuân thủ SRP. Hỗ trợ OCP. Hỗ trợ chức năng undo/redo. Cho phép các hoạt động trì hoãn.
- **Nhược điểm:** Có thể làm phức tạp cấu trúc mã bằng cách thêm các lớp giữa các thành phần.
- **Tư duy thiết kế:** Mẫu Command đề xuất rằng các đối tượng GUI không nên gửi các yêu cầu này trực tiếp. Thay vào đó, bạn nên trích xuất tất cả các chi tiết yêu cầu, chẳng hạn như đối tượng được gọi, tên phương thức và danh sách đối số, vào một lớp lệnh riêng biệt với một phương thức duy nhất kích hoạt yêu cầu này. Lệnh trở thành một lớp trung gian tiện lợi giúp giảm kết nối giữa các lớp GUI và logic nghiệp vụ.

#### 15. Iterator (Bộ Lặp)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi cho phép **duyệt qua các phần tử của một tập hợp (collection) mà không để lộ biểu diễn cơ bản của nó** (như danh sách, stack hoặc cây). Nó giải quyết vấn đề các cấu trúc phức tạp, như cây, có thể yêu cầu các phương thức duyệt khác nhau (duyệt theo chiều sâu, chiều rộng hoặc truy cập ngẫu nhiên) mà không làm lộn xộn chức năng chính của tập hợp với các thuật toán duyệt này.
- **Tình huống sử dụng:**
  - Để ẩn sự phức tạp của cấu trúc dữ liệu của một tập hợp khỏi client.
  - Giảm sự trùng lặp của mã duyệt.
  - Cung cấp một phương tiện để duyệt các tập hợp khác nhau hoặc các cấu trúc dữ liệu không xác định vào thời gian chạy.
- **Cấu trúc chính:**
  - **Iterator Interface:** Khai báo các hoạt động cần thiết để duyệt một tập hợp.
  - **Concrete Iterators:** Triển khai các thuật toán duyệt cụ thể trong khi duy trì tiến trình duyệt độc lập.
  - **Collection Interface:** Định nghĩa các phương thức để lấy các iterator tương thích với tập hợp.
  - **Concrete Collections:** Trả về các thể hiện của các lớp iterator cụ thể theo yêu cầu.
  - **Client:** Làm việc với các tập hợp và iterator thông qua các giao diện của chúng, đảm bảo kết nối lỏng lẻo và tính linh hoạt trong việc sử dụng các loại iterator khác nhau.
- **Ưu điểm:** Nâng cao SRP bằng cách tách logic duyệt. Tuân thủ OCP cho phép mở rộng mà không sửa đổi. Hỗ trợ lặp song song và tiếp tục lặp bị trì hoãn.
- **Nhược điểm:** Có thể là quá mức cần thiết cho các tập hợp đơn giản. Có thể gây ra một số không hiệu quả so với truy cập phần tử trực tiếp.
- **Tư duy thiết kế:** Ý tưởng chính của mẫu Iterator là **trích xuất hành vi duyệt của một tập hợp vào một đối tượng riêng biệt gọi là iterator**. Iterator này đóng gói các chi tiết duyệt, cho phép nhiều iterator duyệt cùng một tập hợp một cách độc lập. Tất cả các iterator tuân thủ cùng một giao diện, đảm bảo mã client tương thích với bất kỳ loại tập hợp hoặc thuật toán duyệt nào.

#### 16. Mediator (Người Hòa Giải)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi đơn giản hóa giao tiếp giữa các đối tượng bằng cách **hạn chế tương tác trực tiếp và thực thi sự hợp tác thông qua một đối tượng hòa giải (mediator object)**. Nó giải quyết vấn đề các phụ thuộc lẫn nhau phức tạp trong các phần tử giao diện người dùng có thể dẫn đến hỗn loạn khi các ứng dụng phát triển. Ví dụ, các điều khiển biểu mẫu trong một hộp thoại có thể tương tác theo những cách khó đoán.
- **Tình huống sử dụng:** Khi khó thay đổi một số lớp vì chúng được kết nối chặt chẽ với một loạt các lớp khác.
- **Cấu trúc chính:**
  - **Components:** Các lớp chứa logic nghiệp vụ với các tham chiếu đến giao diện mediator.
  - **Mediator Interface:** Khai báo các phương thức giao tiếp với các component, thường là một phương thức thông báo duy nhất.
  - **Concrete Mediators:** Quản lý và đóng gói giao tiếp giữa các component.
- **Ưu điểm:** Tuân thủ SRP. Cho phép sửa đổi dễ dàng và giảm chi phí từ nhiều phụ thuộc. Nâng cao khả năng tái sử dụng của các component bằng cách tách rời chúng.
- **Nhược điểm:** Mediator có thể phát triển thành "đối tượng thần thánh" (God Object), tập trung quá nhiều logic và trở nên khó quản lý.
- **Tư duy thiết kế:** Mẫu Mediator thúc đẩy giao tiếp gián tiếp giữa các thành phần thông qua một mediator, giảm sự phụ thuộc giữa chúng. Điều này thúc đẩy khả năng tái sử dụng và dễ sửa đổi. Lớp hộp thoại có thể đóng vai trò là mediator, đơn giản hóa các tác vụ xử lý và xác thực giao tiếp. Tương tự như kiểm soát không lưu, nơi các phi công giao tiếp thông qua một tháp điều khiển thay vì trực tiếp với nhau.

#### 17. Memento (Ảnh Chụp/Kỷ Vật)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi cho phép **lưu và khôi phục trạng thái trước đó của một đối tượng mà không để lộ chi tiết triển khai của nó**. Nó giải quyết thách thức khi cố gắng ghi lại trạng thái của một đối tượng do các hạn chế đóng gói, đặc biệt khi xử lý các trường private.
- **Tình huống sử dụng:**
  - Để tạo ảnh chụp nhanh để khôi phục trạng thái trước đó của một đối tượng.
  - Lý tưởng cho các kịch bản mà việc truy cập trực tiếp vào các trường vi phạm tính đóng gói.
- **Cấu trúc chính:**
  - **Originator:** Tạo và khôi phục ảnh chụp nhanh trạng thái của nó.
  - **Memento:** Đại diện cho một ảnh chụp nhanh trạng thái của originator; được thiết kế để **bất biến (immutable)**.
  - **Caretaker:** Quản lý thời điểm lưu và khôi phục trạng thái của originator.
- **Ưu điểm:** Bảo toàn tính đóng gói trong khi cho phép quản lý trạng thái. Đơn giản hóa thiết kế của originator.
- **Nhược điểm:** Tiềm ẩn việc sử dụng bộ nhớ cao nếu memento được tạo quá thường xuyên. Caretaker phải quản lý vòng đời của memento một cách hiệu quả.
- **Tư duy thiết kế:** Mẫu Memento ủy quyền việc tạo ảnh chụp nhanh trạng thái cho chính đối tượng sở hữu trạng thái đó, **originator**. Điều này hạn chế quyền truy cập vào trạng thái được giữ trong memento, chỉ cho phép originator sửa đổi nó trong khi caretaker truy cập nó thông qua một giao diện hạn chế. Thiết kế này thúc đẩy tính đóng gói tốt hơn và tách biệt quản lý trạng thái khỏi các đối tượng khác.

#### 18. Observer (Người Quan Sát)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi cho phép một **cơ chế đăng ký để thông báo cho nhiều đối tượng về các sự kiện xảy ra trong đối tượng mà chúng đang quan sát**. Nó giải quyết vấn đề trong các kịch bản mà `Customer` muốn được thông báo về tình trạng sẵn có của một sản phẩm cụ thể tại một `Store`, việc truy cập thường xuyên có thể dẫn đến lãng phí thời gian, trong khi thông báo liên tục từ `Store` có thể làm tràn ngập khách hàng bằng thông tin không mong muốn.
- **Tình huống sử dụng:** Khi thay đổi trạng thái trong một đối tượng có thể yêu cầu thay đổi trong các đối tượng khác, và tập hợp các đối tượng cụ thể không được biết trước hoặc thay đổi linh hoạt.
- **Cấu trúc chính:**
  - **Publisher:** Quản lý danh sách subscriber và thông báo cho họ về các thay đổi hoặc sự kiện.
  - **Subscriber Interface:** Định nghĩa phương thức mà subscriber triển khai để nhận thông báo.
  - **Concrete Subscribers:** Thực hiện các hành động cụ thể để phản ứng với thông báo.
  - **Contextual Information:** Về các sự kiện có thể được truyền dưới dạng tham số trong phương thức thông báo.
  - **Client:** Tạo và đăng ký subscriber với publisher.
- **Ưu điểm:** Tuân thủ OCP, cho phép các lớp subscriber mới mà không sửa đổi mã hiện có. Hỗ trợ mối quan hệ động giữa các đối tượng.
- **Nhược điểm:** Thông báo có thể không xảy ra theo thứ tự dự đoán.
- **Tư duy thiết kế:** Giải pháp liên quan đến việc tạo một **Publisher** (chủ đề) duy trì danh sách **Subscribers**. Các Subscriber có thể tham gia hoặc rời khỏi danh sách này theo sở thích của họ. Publisher thông báo cho tất cả các subscriber đã đăng ký về các sự kiện quan trọng thông qua các phương thức thông báo được định nghĩa, cho phép giao tiếp hiệu quả mà không cần kết nối không cần thiết giữa các đối tượng. Tương tự như việc đăng ký báo hoặc tạp chí, bạn không cần phải đến cửa hàng mỗi lần để kiểm tra số báo mới.

#### 19. State (Trạng Thái)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi cho phép một đối tượng **thay đổi hành vi dựa trên trạng thái nội bộ của nó**, khiến nó dường như đã thay đổi lớp. Khái niệm này liên quan chặt chẽ đến Finite-State Machine, nơi một chương trình chỉ có thể ở một số lượng trạng thái hữu hạn và hành xử khác nhau trong mỗi trạng thái, với các chuyển đổi được định nghĩa giữa các trạng thái. Nó giải quyết vấn đề triển khai máy trạng thái bằng cách sử dụng các toán tử điều kiện (như `if` hoặc `switch`) có thể dẫn đến mã phức tạp và khó bảo trì khi số lượng trạng thái tăng lên.
- **Tình huống sử dụng:** Khi bạn có một đối tượng hoạt động khác nhau tùy thuộc vào trạng thái hiện tại của nó, số lượng trạng thái rất lớn, và mã cụ thể cho từng trạng thái thay đổi thường xuyên.
- **Cấu trúc chính:**
  - **Context:** Duy trì tham chiếu đến đối tượng trạng thái hiện tại và ủy quyền các tác vụ liên quan đến trạng thái cho nó.
  - **State Interface:** Định nghĩa một giao diện chung cho tất cả các lớp trạng thái cụ thể.
  - **Concrete States:** Triển khai các hành vi cụ thể cho mỗi trạng thái.
- **Ưu điểm:** Giảm đáng kể độ phức tạp của các điều kiện trong lớp gốc. Tuân thủ SRP bằng cách tách mã liên quan đến các trạng thái cụ thể thành các lớp riêng biệt. Tuân thủ OCP, cho phép thêm các trạng thái mới mà không sửa đổi các trạng thái hiện có. Giảm sự trùng lặp mã bằng cách tạo các hệ thống phân cấp lớp trạng thái và trích xuất các hành vi chung vào các lớp cơ sở trừu tượng.
- **Nhược điểm:** Có thể là quá mức cần thiết nếu máy trạng thái chỉ có một vài trạng thái hoặc hiếm khi thay đổi.
- **Tư duy thiết kế:** Mẫu State đề xuất **tạo các lớp riêng biệt cho mỗi trạng thái có thể có của một đối tượng, đóng gói các hành vi cụ thể cho từng trạng thái**. Đối tượng chính (context) giữ một tham chiếu đến đối tượng trạng thái hiện tại và ủy quyền các tác vụ liên quan đến trạng thái cho nó. Chuyển đổi trạng thái liên quan đến việc hoán đổi đối tượng trạng thái hiện tại bằng một đối tượng khác đại diện cho trạng thái mới. Các nút điện thoại thông minh hoạt động khác nhau tùy thuộc vào trạng thái của thiết bị.

#### 20. Strategy (Chiến Lược)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi định nghĩa một họ các thuật toán, đóng gói mỗi thuật toán trong một lớp riêng biệt và làm cho chúng có thể hoán đổi cho nhau. Nó giải quyết vấn đề khi triển khai một ứng dụng điều hướng, việc hỗ trợ các tuyến đường ô tô ban đầu đã mở rộng để bao gồm các tùy chọn định tuyến đi bộ, phương tiện công cộng, đi xe đạp và tham quan. Điều này dẫn đến mã nguồn cồng kềnh và các vấn đề bảo trì.
- **Tình huống sử dụng:** Khi bạn cần chuyển đổi giữa nhiều thuật toán vào thời gian chạy. Giúp củng cố các lớp tương tự thành một cấu trúc duy nhất, giảm sự trùng lặp mã. Cô lập logic nghiệp vụ khỏi triển khai thuật toán. Loại bỏ các câu lệnh điều kiện phức tạp để chọn thuật toán.
- **Cấu trúc chính:**
  - **Context:** Giữ một tham chiếu đến một chiến lược.
  - **Strategy Interface:** Định nghĩa một phương thức để thực thi một thuật toán.
  - **Concrete Strategies:** Triển khai các phiên bản cụ thể của thuật toán.
  - **Execution Method:** Context gọi phương thức thực thi trên chiến lược mà không biết chi tiết.
  - **Client:** Tạo và gán một chiến lược cụ thể cho context.
- **Ưu điểm:** Cho phép hoán đổi thuật toán vào thời gian chạy. Cô lập chi tiết triển khai. Thay thế kế thừa bằng composition. Tuân thủ OCP.
- **Nhược điểm:** Tăng độ phức tạp nếu chỉ có ít thuật toán tồn tại. Yêu cầu client chọn chiến lược phù hợp.
- **Tư duy thiết kế:** Mẫu Strategy đề xuất rằng bạn lấy một lớp làm một việc cụ thể theo nhiều cách khác nhau và **trích xuất tất cả các thuật toán này vào các lớp riêng biệt gọi là chiến lược**. Lớp chính (context) ủy quyền công việc cho các đối tượng chiến lược này. Context được giữ độc lập với các chiến lược cụ thể, thúc đẩy tính linh hoạt và khả năng bảo trì. Việc chọn phương tiện di chuyển đến sân bay (xe buýt, taxi, xe đạp) đại diện cho các chiến lược khác nhau dựa trên sở thích hoặc hạn chế cá nhân.

#### 21. Template Method (Phương Thức Mẫu)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi phác thảo **khung sườn của một thuật toán trong một lớp cha**, cho phép các lớp con ghi đè các bước cụ thể mà không làm thay đổi cấu trúc tổng thể. Nó giải quyết vấn đề trong một ứng dụng khai thác dữ liệu xử lý các định dạng tài liệu khác nhau (PDF, DOC, CSV), mã trùng lặp xuất hiện giữa các lớp được thiết kế cho mỗi định dạng do sự tương đồng trong logic xử lý và phân tích dữ liệu.
- **Tình huống sử dụng:** Khi bạn muốn cho phép client chỉ mở rộng các bước cụ thể của một thuật toán, nhưng không phải toàn bộ thuật toán hoặc cấu trúc của nó.
- **Cấu trúc chính:**
  - **Abstract Class:** Khai báo các phương thức đại diện cho các bước thuật toán và chứa phương thức template định nghĩa thứ tự của chúng.
  - **Concrete Classes:** Triển khai tất cả các bước trừu tượng nhưng không thể thay đổi chính phương thức template.
- **Ưu điểm:** Tính linh hoạt cho client để ghi đè các phần cụ thể của thuật toán. Giảm sự trùng lặp mã thông qua các triển khai lớp cha được chia sẻ.
- **Nhược điểm:** Client có thể bị giới hạn bởi khung của thuật toán. Tiềm ẩn sự phức tạp bảo trì tăng lên với nhiều bước hơn.
- **Tư duy thiết kế:** Mẫu Template Method đề xuất **chia nhỏ thuật toán thành các bước riêng biệt, được đóng gói trong các phương thức**. Một phương thức template điều phối các cuộc gọi này, trong khi các lớp con triển khai các bước trừu tượng hoặc ghi đè các triển khai mặc định tùy chọn. Cách tiếp cận này giảm sự trùng lặp mã bằng cách trích xuất chức năng chung vào lớp cơ sở.

#### 22. Visitor (Khách Tham Quan)

- **Bản chất/Mục đích:** Là một mẫu thiết kế hành vi cho phép **tách biệt các thuật toán khỏi các đối tượng mà chúng hoạt động**, nâng cao tính mô đun và linh hoạt của mã. Nó giải quyết vấn đề khi xuất một biểu đồ thông tin địa lý sang XML, việc sửa đổi các lớp nút hiện có để thêm phương thức xuất gây ra rủi ro đáng kể. Kiến trúc sư hệ thống lo ngại về các lỗi tiềm ẩn và các yêu cầu trong tương lai đối với các định dạng xuất khác nhau.
- **Tình huống sử dụng:** Khi bạn tìm thấy mình đang giới thiệu một hành vi mới liên quan đến các nút (node), tất cả những gì bạn phải làm là triển khai một lớp visitor mới.
- **Cấu trúc chính:**
  - **Visitor Interface:** Định nghĩa các phương thức `visit` cho từng loại phần tử cụ thể.
  - **Concrete Visitors:** Triển khai các hoạt động cụ thể cho từng loại phần tử.
  - **Element Interface:** Khai báo phương thức `accept(Visitor)` để cho phép visitor "thăm" phần tử.
  - **Concrete Elements:** Triển khai phương thức `accept`.
- **Ưu điểm:** Cho phép bạn thêm các hoạt động mới mà không sửa đổi các lớp hiện có. Tuân thủ OCP. Tuân thủ SRP bằng cách củng cố nhiều hoạt động vào các lớp visitor duy nhất.
- **Nhược điểm:** Bạn cần cập nhật tất cả các lớp visitor mỗi khi một lớp phần tử mới được thêm vào hoặc xóa bỏ, điều này có thể dẫn đến tăng chi phí bảo trì. Visitor có thể không có quyền truy cập trực tiếp vào các trường private hoặc phương thức của các lớp phần tử.
- **Tư duy thiết kế:** Mẫu Visitor đề xuất **tạo một lớp visitor riêng biệt để đóng gói hành vi mới**, cho phép thêm chức năng xuất XML mà không thay đổi các lớp nút hiện có. Bằng cách triển khai các phương thức visitor khác nhau cho các loại nút khác nhau, visitor có thể xử lý các hoạt động theo lớp của nút. Để giải quyết thách thức thực thi phương thức visitor phù hợp mà không cần các điều kiện rườm rà, mẫu Visitor sử dụng kỹ thuật gọi là **Double Dispatch**. Các đối tượng có thể chấp nhận visitor và xác định phương thức nào nên được thực thi.

---

## 5. Tư Duy & Triết Lý Phần Mềm Đích Thực

Ngoài việc học các mẫu thiết kế cụ thể, "Dive Into Design Patterns" còn truyền tải những triết lý và tư duy cốt lõi giúp hình thành một kỹ sư phần mềm thực thụ:

- **Thay đổi là hằng số duy nhất:** "Change is the only constant thing in a programmer’s life.". Hãy thiết kế hệ thống của bạn với khả năng mở rộng (extensibility) làm trọng tâm, vì yêu cầu sẽ luôn thay đổi. Nếu ai đó yêu cầu bạn thay đổi một cái gì đó trong ứng dụng của bạn, điều đó có nghĩa là vẫn còn người quan tâm đến nó.
- **Giảm thiểu độ phức tạp:** "The main goal of this principle is reducing complexity.". Đơn giản là đủ cho các chương trình nhỏ hơn, nhưng những thách thức phát sinh khi chương trình phát triển, dẫn đến các lớp lớn hơn khó điều hướng, khó hiểu và khó bảo trì.
- **Kết nối lỏng lẻo (Loose Coupling) và Tính cố kết cao (High Cohesion):** Các nguyên tắc thiết kế tốt nhất đều xoay quanh việc giảm sự phụ thuộc lẫn nhau giữa các thành phần và làm cho mỗi thành phần tập trung vào một nhiệm vụ duy nhất. "The fewer dependencies a class has, the easier it becomes to modify, extend or reuse that class.".
- **Tái sử dụng là chìa khóa:** "Code reuse is one of the most common ways to reduce development costs.". Các mẫu thiết kế giúp cải thiện tính linh hoạt của thành phần, giúp việc tái sử dụng dễ quản lý hơn.
- **Ngôn ngữ chung:** "Design patterns define a common language that you and your teammates can use to communicate more efficiently.". Việc hiểu các mẫu thiết kế thiết lập một vốn từ vựng chung, tạo điều kiện giao tiếp rõ ràng hơn giữa các thành viên trong nhóm.
- **Cân bằng giữa nguyên tắc và thực tế:** Mặc dù các nguyên tắc như SOLID là nền tảng, việc áp dụng chúng một cách máy móc có thể dẫn đến thiết kế quá mức (over-engineering) và sự phức tạp không cần thiết. "Developers must balance adherence to this principle with the realities of debugging and code maintenance, where simplicity and direct modifications may sometimes yield better outcomes.". "Keep the balance.".
- **Hộp công cụ giải pháp:** "Design patterns are a toolkit of tried and tested solutions to common problems in software design.". Chúng không phải là công thức cứng nhắc mà là những bản thiết kế linh hoạt cần được điều chỉnh cho phù hợp với ngữ cảnh.
- **Cải thiện liên tục và Refactoring:** Nhận biết khi nào cần refactor (tái cấu trúc) mã là một kỹ năng quan trọng. Các mẫu thiết kế thường xuất hiện sau quá trình refactoring để cải thiện thiết kế hiện có.

---

Tài liệu này là một bản đồ tư duy, cung cấp cho bạn những công cụ và triết lý cần thiết để điều hướng thế giới phức tạp của thiết kế phần mềm. Hãy xem nó như một người bạn đồng hành, giúp bạn không ngừng nâng cao kỹ năng thiết kế hệ thống và refactoring trong suốt hành trình sự nghiệp lập trình của mình.
