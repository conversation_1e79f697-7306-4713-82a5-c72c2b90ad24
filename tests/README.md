# 🧪 Enterprise Testing Framework

> **Comprehensive Testing Strategy** - Implementing Testing Pyramid với coverage requirements

## 📋 Tổng Quan

Testing framework này implement **Testing Pyramid** theo knowledge base:

### **Testing Pyramid Structure**
```
           ┌─────────────────┐
           │   E2E Tests     │ ← Few, Expensive (70%+ coverage)
           │   (Playwright)  │
           └─────────────────┘
         ┌───────────────────────┐
         │  Integration Tests    │ ← Some, Moderate (80%+ coverage)
         │  (Supertest, Pytest) │
         └───────────────────────┘
     ┌─────────────────────────────────┐
     │        Unit Tests               │ ← Many, Fast (90%+ coverage)
     │    (Jest, Pytest, Go Test)     │
     └─────────────────────────────────┘
```

## 🎯 Testing Strategy

### **Core Testing Principles**
- **Shift Left**: Test early và frequently
- **Test Automation**: Minimize manual testing
- **Fast Feedback**: Quick test execution
- **Reliable Tests**: Consistent results
- **Maintainable**: Easy to update và debug

### **Coverage Requirements**
| Test Type | Coverage Target | Execution Time | Frequency |
|-----------|----------------|----------------|-----------|
| **Unit** | ≥90% | <5 minutes | Every commit |
| **Integration** | ≥80% | <15 minutes | Every PR |
| **E2E** | ≥70% | <30 minutes | Daily/Release |
| **Performance** | Key scenarios | <10 minutes | Weekly |

## 🏗️ Framework Architecture

### **Technology Stack**
| Layer | Technology | Purpose |
|-------|------------|---------|
| **Unit Testing** | Jest, Pytest, Go Test | Fast, isolated tests |
| **Integration** | Supertest, TestContainers | API và service tests |
| **E2E Testing** | Playwright, Cypress | User journey tests |
| **Performance** | k6, Artillery | Load và stress testing |
| **Security** | OWASP ZAP, Snyk | Security vulnerability tests |
| **API Testing** | Postman/Newman | API contract testing |

### **Test Organization**
```
tests/
├── unit/                    # Unit Tests (90%+ coverage)
│   ├── libs/               # Shared library tests
│   ├── services/           # Service layer tests
│   └── utils/              # Utility function tests
├── integration/            # Integration Tests (80%+ coverage)
│   ├── api/               # API endpoint tests
│   ├── database/          # Database integration tests
│   └── external/          # External service tests
├── e2e/                   # End-to-End Tests (70%+ coverage)
│   ├── user-journeys/     # Complete user workflows
│   ├── admin-flows/       # Admin functionality tests
│   └── mobile/            # Mobile app tests
├── performance/           # Performance Tests
│   ├── load/              # Load testing scenarios
│   ├── stress/            # Stress testing scenarios
│   └── spike/             # Spike testing scenarios
├── security/              # Security Tests
│   ├── authentication/    # Auth security tests
│   ├── authorization/     # Access control tests
│   └── vulnerabilities/   # Security scan tests
└── fixtures/              # Test Data & Utilities
    ├── data/              # Test data sets
    ├── mocks/             # Mock implementations
    └── helpers/           # Test helper functions
```

## 🚀 Quick Start

### **Setup Testing Environment**
```bash
# Install dependencies
npm install

# Setup test databases
npm run test:setup

# Run all tests
npm run test

# Run with coverage
npm run test:coverage
```

### **Running Specific Test Types**
```bash
# Unit tests only
npm run test:unit

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Performance tests
npm run test:performance

# Security tests
npm run test:security
```

## 🧪 Testing Patterns

### **Unit Testing Best Practices**
```typescript
// ✅ Good Unit Test Example
describe('UserService', () => {
  let userService: UserService;
  let mockRepository: jest.Mocked<IUserRepository>;

  beforeEach(() => {
    mockRepository = createMockRepository();
    userService = new UserService(mockRepository);
  });

  describe('createUser', () => {
    it('should create user with valid data', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', name: 'Test User' };
      const expectedUser = User.create(userData).getValue();
      mockRepository.save.mockResolvedValue(expectedUser);

      // Act
      const result = await userService.createUser(userData);

      // Assert
      expect(result.isSuccess).toBe(true);
      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          email: userData.email,
          name: userData.name
        })
      );
    });

    it('should fail with invalid email', async () => {
      // Arrange
      const invalidData = { email: 'invalid-email', name: 'Test User' };

      // Act
      const result = await userService.createUser(invalidData);

      // Assert
      expect(result.isFailure).toBe(true);
      expect(result.error).toContain('Invalid email format');
      expect(mockRepository.save).not.toHaveBeenCalled();
    });
  });
});
```

### **Integration Testing Patterns**
```typescript
// ✅ Good Integration Test Example
describe('User API Integration', () => {
  let app: INestApplication;
  let testDb: TestDatabase;

  beforeAll(async () => {
    testDb = await TestDatabase.create();
    app = await createTestApp(testDb.getConnection());
  });

  afterAll(async () => {
    await testDb.cleanup();
    await app.close();
  });

  beforeEach(async () => {
    await testDb.seed();
  });

  afterEach(async () => {
    await testDb.clear();
  });

  describe('POST /api/users', () => {
    it('should create user and return 201', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'SecurePass123!'
      };

      // Act
      const response = await request(app.getHttpServer())
        .post('/api/users')
        .send(userData)
        .expect(201);

      // Assert
      expect(response.body).toMatchObject({
        id: expect.any(String),
        email: userData.email,
        name: userData.name
      });
      expect(response.body.password).toBeUndefined();

      // Verify in database
      const savedUser = await testDb.findUserByEmail(userData.email);
      expect(savedUser).toBeDefined();
      expect(savedUser.email).toBe(userData.email);
    });
  });
});
```

### **E2E Testing Patterns**
```typescript
// ✅ Good E2E Test Example
import { test, expect } from '@playwright/test';

test.describe('User Registration Flow', () => {
  test('should complete user registration successfully', async ({ page }) => {
    // Navigate to registration page
    await page.goto('/register');

    // Fill registration form
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=name-input]', 'Test User');
    await page.fill('[data-testid=password-input]', 'SecurePass123!');
    await page.fill('[data-testid=confirm-password-input]', 'SecurePass123!');

    // Submit form
    await page.click('[data-testid=register-button]');

    // Verify success
    await expect(page.locator('[data-testid=success-message]')).toBeVisible();
    await expect(page).toHaveURL('/dashboard');

    // Verify user is logged in
    await expect(page.locator('[data-testid=user-menu]')).toContainText('Test User');
  });

  test('should show validation errors for invalid data', async ({ page }) => {
    await page.goto('/register');

    // Submit empty form
    await page.click('[data-testid=register-button]');

    // Verify validation errors
    await expect(page.locator('[data-testid=email-error]')).toContainText('Email is required');
    await expect(page.locator('[data-testid=name-error]')).toContainText('Name is required');
    await expect(page.locator('[data-testid=password-error]')).toContainText('Password is required');
  });
});
```

## 📊 Test Reporting

### **Coverage Reports**
- **HTML Reports**: Detailed coverage visualization
- **JSON Reports**: Machine-readable coverage data
- **LCOV Reports**: Integration với CI/CD tools
- **Cobertura Reports**: XML format cho enterprise tools

### **Test Results**
- **JUnit XML**: Standard test result format
- **Allure Reports**: Rich test reporting với screenshots
- **Mochawesome**: Beautiful HTML test reports
- **Custom Dashboards**: Real-time test metrics

## 🔧 CI/CD Integration

### **GitHub Actions Workflow**
```yaml
name: Test Pipeline
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run unit tests
        run: npm run test:unit:coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install Playwright
        run: npx playwright install
      - name: Run E2E tests
        run: npm run test:e2e
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

## 🎯 Quality Gates

### **Pre-commit Hooks**
- Lint code style
- Run unit tests
- Check test coverage
- Security vulnerability scan

### **Pull Request Checks**
- All tests must pass
- Coverage thresholds met
- No security vulnerabilities
- Performance regression checks

### **Release Criteria**
- 100% test pass rate
- Coverage targets achieved
- Performance benchmarks met
- Security scans clean

---

**🎯 Mục tiêu**: Đảm bảo chất lượng code cao thông qua comprehensive testing strategy theo industry best practices.
