services:
  # Development service
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: deno-app-dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - DENO_ENV=development
      - DENO_NO_UPDATE_CHECK=1
      - DENO_NO_PROMPT=1
    networks:
      - deno-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "deno",
          "eval",
          "try { await fetch('http://localhost:8000/health'); } catch { Deno.exit(1); }",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M

  # Production service
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: deno-app-prod
    ports:
      - "8001:8000"
    environment:
      - DENO_ENV=production
      - DENO_NO_UPDATE_CHECK=1
      - DENO_NO_PROMPT=1
    networks:
      - deno-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "C<PERSON>",
          "deno",
          "eval",
          "try { await fetch('http://localhost:8000/health'); } catch { Deno.exit(1); }",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 512M

  # Load balancer for production (optional)
  nginx:
    image: nginx:alpine
    container_name: deno-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app-prod
    networks:
      - deno-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--quiet",
          "--tries=1",
          "--spider",
          "http://localhost/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: 128M

  # Redis for caching (optional)
  redis:
    image: redis:alpine
    container_name: deno-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - deno-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: 256M

  # PostgreSQL for database (optional)
  postgres:
    image: postgres:15-alpine
    container_name: deno-postgres
    environment:
      POSTGRES_DB: deno_app
      POSTGRES_USER: deno_user
      POSTGRES_PASSWORD: deno_password
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - deno-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U deno_user -d deno_app"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512M

networks:
  deno-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  postgres-data:
    driver: local
