<PERSON><PERSON><PERSON> <PERSON><PERSON>, để bạn có thể sao chép vào AI Agent và kiểm tra yêu cầu dự án, tôi đã tổng hợp toàn bộ kiến thức từ các nguồn bạn cung cấp thành một danh sách chi tiết, đ<PERSON><PERSON><PERSON> tổ chức theo các lĩnh vực chính trong Công nghệ Thông tin. Các điểm kiến thức quan trọng sẽ được in đậm để bạn dễ theo dõi.

---

Dưới đây là danh sách toàn diện các kiến thức bạn cần nắm vững, đ<PERSON><PERSON><PERSON> trích xuất và hệ thống hóa từ các tài liệu bạn đã cung cấp:

### **I. Lập Trình & Ngô<PERSON> (JavaScript, Python, Go, C/C++/Rust)**

- **Nền tảng Ngôn ngữ JavaScript & TypeScript**

  - Biết về **ES5, ES6+** và các tính năng nổi bật, vận dụng trong trình duyệt web console và môi trường Node.js.
  - Hiểu biết về **JavaScript** là ngôn ngữ kịch bản **đơn luồng** với mô hình **vòng lặp sự kiện (event loop)** để xử lý bất đồng bộ. Hỗ trợ lập trình hướng đối tượng qua **prototype** và **class** (ES6+).
  - Hiểu biết về **TypeScript** là **siêu tập** (superset) của JavaScript, bổ sung hệ thống **kiểu tĩnh (static typing)** và khái niệm **lớp giao diện (interface)**.
    - Ưu điểm của TypeScript: **phát hiện lỗi từ giai đoạn biên dịch**, tổ chức mã dễ bảo trì hơn, **tự sinh API Docs, refactor an toàn, pattern-matching qua conditional/utility types**.
  - Biết về **biến** (var, let, const), **kiểu dữ liệu** (JS có kiểu động, TS/Go có kiểu tĩnh), **hàm**, **phạm vi (Scope)** và **đóng gói (Closure)**. Kiểu nguyên thủy như số, chuỗi là **bất biến**.
  - Cơ chế **bất đồng bộ** trong JavaScript: **callbacks, Promise, và async/await**.
  - Các tính năng sẵn có trên nền tảng chạy JavaScript như **Node.js, Deno**.
  - Tối ưu **cold-start** cho Serverless với ESBuild.
  - Xu hướng tương lai: **Edge Runtime** (Deno, Bun), **Module Federation v2**, **WebGPU**, **ECMAScript 2025+** (Decorators, Temporal API, Records & Tuples).

- **Lập trình Hướng đối tượng (OOP)**.

  - Hiểu về khái niệm **Class** (Lớp) và **Object** (Đối tượng).
  - Bốn nguyên lý OOP cơ bản: **Abstraction** (Trừu tượng), **Encapsulation** (Đóng gói), **Inheritance** (Kế thừa), **Polymorphism** (Đa hình).
  - Nguyên lý thiết kế **SOLID**: **SRP** (Single Responsibility Principle), **OCP** (Open/Closed Principle), **LSP** (Liskov Substitution Principle), **ISP** (Interface Segregation Principle), **DIP** (Dependency Inversion Principle).
  - Mẫu thiết kế (Design Patterns) phân loại bởi "Gang of Four": **Creational Patterns** (Singleton, Factory, Builder, Prototype), **Structural Patterns** (Adapter, Composite, Proxy, Facade, Decorator), **Behavioral Patterns** (Observer, Strategy, Iterator, Template Method, Command).

- **Lập trình Python Chuyên Sâu**

  - **Python Fundamentals for AI**: Thành thạo các kiểu dữ liệu cơ bản (Lists, tuples, dicts, sets), Lập trình hướng đối tượng (OOP: Classes và inheritance), Các tính năng nâng cao (Decorators, Generators, Context Managers).
  - Sử dụng hiệu quả các thư viện khoa học dữ liệu: **NumPy** (xử lý mảng đa chiều, phép toán đại số tuyến tính), **Pandas** (đọc/xử lý dữ liệu, DataFrames, ETL), **Matplotlib/Seaborn** (Trực quan hóa dữ liệu), **Scikit-learn** (thư viện ML cơ bản).
  - Cách triển khai Gradient Descent cơ bản.
  - Ví dụ ước lượng tham số MLE/MAP.
  - Sử dụng thư viện **CVXOPT** để giải các bài toán Linear Programming/Quadratic Programming.

- **Lập trình Golang**

  - Thành thạo cú pháp, kiểu dữ liệu, quản lý bộ nhớ, interface, và thư viện chuẩn.
  - Khả năng xử lý **đồng thời (concurrency)** với **goroutine và channel**.
  - Thư viện web **net/http** (hoặc Gin/Echo), kết nối CSDL (database/sql, GORM) và testing tích hợp.
  - Golang xử lý HTTP & crawl cực nhanh.

- **Ngôn ngữ khác (C++/Rust)**
  - Sử dụng **WebAssembly** để gọi thuật toán C++/Rust khi cần tăng tốc.

### **II. Khoa Học Máy Tính & Cấu Trúc Dữ Liệu/Thuật Toán**

- **Cấu trúc dữ liệu cơ bản**

  - **Mảng (Array)**, **Danh sách liên kết (Linked List)**, **Ngăn xếp (Stack - LIFO)**, **Hàng đợi (Queue - FIFO)**.
  - **Bảng băm (Hash Table / Map)**: Lưu trữ cặp khóa-giá trị, tra cứu trung bình O(1).
  - **Cây (Tree)**: Cây nhị phân (Binary Tree), Cây tìm kiếm nhị phân (BST) - tìm kiếm O(log n) trung bình, Heap (Hàng đợi ưu tiên) - push/pop O(log n), Trie (Cây tiền tố) - tìm kiếm/thêm O(m), Segment Tree (Cây phân đoạn).
  - **Đồ thị (Graph)**: Tập hợp đỉnh/cạnh, biểu diễn bằng danh sách kề hoặc ma trận kề. Duyệt **BFS** (dùng hàng đợi) và **DFS** (dùng ngăn xếp/đệ quy) đều O(V+E).
  - **Union-Find (Disjoint Set Union - DSU)**: Quản lý tập hợp các tập hợp con không giao nhau. Gần O(1) nhờ path compression.

- **Các thuật toán cơ bản & kỹ thuật nâng cao**

  - **Tìm kiếm (Searching)**: Tuyến tính (Linear Search - O(n)), Nhị phân (Binary Search - O(log n) trên mảng đã sắp xếp).
  - **Sắp xếp (Sorting)**: O(n^2) (Bubble Sort, Selection Sort, Insertion Sort), O(n log n) (Merge Sort, Quick Sort, Heap Sort).
  - **Kỹ thuật hai con trỏ (Two Pointers)**: Duyệt mảng/danh sách bằng hai chỉ số, giảm độ phức tạp từ O(n^2) xuống O(n).
  - **Kỹ thuật cửa sổ trượt (Sliding Window)**: Tối ưu để giải bài toán về đoạn con/chuỗi con liên tục.
  - **Tham lam (Greedy Algorithms)**: Chọn phương án tốt nhất tại mỗi bước.
  - **Quay lui (Backtracking)**: Thử và sai có hệ thống, tìm tất cả các lời giải.
  - **Lập trình động (Dynamic Programming - DP)**: Chia thành các bài toán con chồng lấn, tận dụng kết quả (Top-down/Memoization, Bottom-up/Tabulation).
  - **Thao tác bit (Bit Manipulation)**: Khai thác biểu diễn nhị phân để thực hiện phép toán nhanh.
  - **Thuật toán toán học (Mathematical Algorithms)**: GCD (Ước chung lớn nhất - Euclid O(log(min(a,b)))), Số nguyên tố (Sàng Eratosthenes O(N log log N)), Số học mô-đun (Lũy thừa nhanh), Toán tổ hợp (Hệ số nhị thức, số Fibonacci).

- **Phân tích độ phức tạp (Big O Notation)**

  - Đo độ phức tạp thời gian và không gian trong trường hợp xấu nhất.
  - **O(1)** (Hằng số), **O(n)** (Tuyến tính), **O(log n)** (Logarit), **O(n log n)** (Sắp xếp hiệu quả), **O(n²)** (Hàm bậc hai), **O(2^n)** (Hàm mũ).

- **Toán học cho AI Engineer**
  - **Đại số Tuyến tính**: Vectors, Ma trận (phép nhân, đơn vị, nghịch đảo, tuyến tính độc lập, cơ sở), Trị riêng/Vector riêng, Chéo hoá ma trận, Chuẩn (Norm), Vết (Trace).
  - **Giải tích**: Đạo hàm riêng, Gradient descent, Chain rule, Đạo hàm bậc nhất/hai.
  - **Xác suất Thống kê**: Biến ngẫu nhiên, Hàm mật độ xác suất (PDF), Xác suất đồng thời/có điều kiện, Quy tắc Bayes, Độc lập, Kỳ vọng/Phương sai/Hiệp phương sai, Phân phối chuẩn, Ước lượng tham số (MLE, MAP).
  - **Toán Rời rạc**: Lý thuyết đồ thị, Lý thuyết tập hợp, Lý thuyết thông tin (Entropy).
  - **Tối ưu Lồi (Convex Optimization)**: Tập lồi, Hàm lồi, Các dạng bài toán (Linear Programming, Quadratic Programming, Geometric Programming), Bài toán đối ngẫu/KKT.

### **III. Thiết Kế & Kiến Trúc Hệ Thống (System Design)**

- **Nguyên tắc Nền tảng Bất biến (Core Principles)**

  - **Thiết kế hệ thống** là tạo ra kiến trúc tổng thể, tập trung vào **các nguyên lý nền tảng** vững bền.
  - **Mục tiêu**: Đảm bảo hệ thống đáp ứng yêu cầu về tính năng, độ ổn định, hiệu năng và bảo mật.
  - **Yêu cầu Chức năng (Functional Requirements)**: Tính năng và nghiệp vụ chính.
  - **Yêu cầu Phi chức năng (Non-Functional Requirements - NFRs)**: **Khả năng mở rộng (Scalability)**, **Khả năng chịu lỗi và tính sẵn sàng (Reliability/Availability)**, **Hiệu năng (Performance)**, **Tính bảo trì (Maintainability)**, **Tính nhất quán dữ liệu (Consistency)**, **An ninh (Security)**, **Tính dễ kiểm thử (Testability)**, **Khả năng quan sát (Observability)**.
  - **Bộ Nguyên lý Thiết kế Expert (8 Kim chỉ nam)**: **SRP** (Single Responsibility), **SoC** (Separation of Concerns), **High Cohesion - Loose Coupling**, **KISS** (Keep It Simple), **DRY** (Don't Repeat Yourself), **Abstraction** (Trừu tượng hóa), **Modularity** (Tính module), **IaC** (Infrastructure as Code).
  - Thiết kế cho sự thất bại (**Design for failure**).
  - Tính bất biến khi lặp lại (**Idempotence**).
  - **YAGNI** (You Aren’t Gonna Need It).

- **Quy trình Thiết kế Hệ thống**

  1.  **Phân tích yêu cầu**: Thu thập và làm rõ functional & non-functional requirements.
  2.  **Mô hình hóa luồng dữ liệu**: Xác định data flow và các thành phần tương tác.
  3.  **Thiết kế thành phần chính**: Client, máy chủ, cơ sở dữ liệu, cache, proxy.
  4.  **Tính toán tài nguyên**: Dự đoán tải và capacity planning.
  5.  **High-Level Design**: Thiết kế tổng quan kiến trúc.
  6.  **Low-Level Design**: Chi tiết implementation của từng component.
  7.  **Đánh giá trade-offs**: Hiệu năng vs chi phí vs độ phức tạp.

- **Mẫu Kiến trúc (Architectural Patterns)**

  - **Monolithic (Tích hợp)**: Ứng dụng đóng gói như một khối đơn nhất. Khi nào dùng: Startup, prototype, team nhỏ.
  - **Multi-tier (Phân tầng)**: Chia thành các tầng rõ ràng (presentation, business, data). Khi nào dùng: Enterprise application cần separation of concerns.
  - **Service-Oriented Architecture (SOA)**: Nhiều dịch vụ độc lập giao tiếp qua message/API, có thể có ESB. Khi nào dùng: Large enterprise cần tích hợp nhiều hệ thống legacy.
  - **Microservices**: Chia nhỏ chức năng thành các service độc lập, deploy riêng biệt. Khi nào dùng: Large scale, multiple teams, frequent deployments.
  - **Event-Driven Architecture (EDA – Hướng sự kiện)**: Các thành phần giao tiếp qua sự kiện (message queue, pub/sub). Khi nào dùng: Lưu lượng bursty, scale vô hạn, audit log. Nguyên lý: **Eventual Consistency**, **Backpressure & Flow Control**, **Idempotence**, **Ordering**, **Event Sourcing**, **CQRS**, **Choreography**.
  - **Serverless**: Chạy hàm chức năng (Function-as-a-Service) trên đám mây. Khi nào dùng: Workload biến động, logic < 15′, không cần OS control.
  - **Kiến trúc AI-Native**: **AI as Semantic Memory** (vector embedding, Vector Database), **AI as Logic Orchestrator** (LLM Orchestrator), Tích hợp Nhiều Thành phần Chuyên biệt, Giảm Logic Code Cứng, Tăng Tính Linh hoạt, Cá nhân hóa và Tự động hóa Cao, Đặc điểm Mô hình Kinh doanh AI-Native.

- **Thiết kế Hướng miền (DDD - Domain-Driven Design)**

  - **Ubiquitous Language**: Ngôn ngữ chung, nhất quán giữa nhóm phát triển và chuyên gia nghiệp vụ.
  - **Bounded Context**: Chia miền lớn thành các ngữ cảnh nhỏ hơn.
  - **Entity, Value Type, Aggregate, Aggregate Root**: Các khối xây dựng cơ bản trong mô hình miền.
  - Nguyên lý: Tập trung nghiệp vụ chính, Cộng tác dev–domain expert, Nhất quán dữ liệu trong Aggregate.

- **Các kỹ thuật thực thi quan trọng**
  - **Caching**: Lưu trữ tạm thời dữ liệu để truy xuất nhanh hơn và giảm tải backend. Các chiến lược: Cache-Aside, Read-Through, Write-Through, Write-Back, Cache Invalidation (TTL). Redis là Distributed cache phổ biến.
  - **Containerization (Docker, Kubernetes)**: Đóng gói ứng dụng cùng môi trường vào container, giúp chạy nhất quán trên mọi môi trường. Kubernetes quản lý container ở quy mô lớn.
  - **Message Brokers & Queues**: Queue (Point-to-Point) và Pub/Sub (Topic) cho giao tiếp bất đồng bộ.
  - **API Gateway**: Điểm vào duy nhất cho clients, quản lý bảo mật, caching, rate limiting.
  - **Service Mesh**: Lớp hạ tầng chuyên biệt quản lý việc giao tiếp giữa các dịch vụ ở tầng mạng (Data Plane, Control Plane).
  - **Load Balancing**: Phân phối request đến nhiều instance.
  - **Stateless Design**: Mọi request độc lập, session lưu ngoài (Redis).
  - **Partitioning & Sharding**: Chia dữ liệu theo vùng địa lý, user ID, time range.
  - **Định luật Little**: Số request đang xử lý = Throughput × Average Latency.
  - **Kiến trúc sạch/Hexagonal (Clean/Hexagonal Architecture)**: Tách biệt logic nghiệp vụ khỏi các chi tiết triển khai (DB, UI, framework).
  - **Cơ chế chịu lỗi (Fault Tolerance)**: Circuit Breaker, Retry.

### **IV. Cơ Sở Dữ Liệu (Database Management)**

- **Nguyên lý cốt lõi của Database Engineering**

  - "Data is the new oil, but refined data is the new gold".
  - **ACID Properties** (Atomicity, Consistency, Isolation, Durability) là bốn trụ cột bất biến của RDBMS.
  - **Định lý CAP** (Consistency, Availability, Partition tolerance): chỉ có thể chọn được 2/3. RDBMS thường ưu tiên CP; NoSQL thường hy sinh nhất quán tức thời để đạt AP.
  - **Database as Code**: Schema, queries, configurations đều phải được version control.
  - **Performance by Design**: Tối ưu hóa từ giai đoạn thiết kế.
  - **12 Nguyên tắc Thiết kế Bất biến**: Normalization First, Index Strategy, Query Optimization, Data Integrity, Security by Design, Backup Everything, Monitor Continuously, Document Relentlessly, Test Thoroughly, Scale Horizontally, Automate Repetitive, Evolve Gradually.

- **Thiết kế Cơ sở dữ liệu (Database Design)**

  - Quá trình lựa chọn, phân nhóm và tổ chức dữ liệu.
  - **Mục tiêu và Quy trình**:
    - **Mô hình khái niệm (Conceptual Model)**: Biểu diễn thực thể và mối quan hệ (biểu đồ **ERD**).
    - **Mô hình logic (Logical Model)**: Chuyển ERD thành các bảng với cột, khóa chính (PRIMARY KEY) và khóa ngoại (FOREIGN KEY), chuẩn hóa.
    - **Mô hình vật lý (Physical Model)**: Xác định chi tiết loại dữ liệu, chỉ mục, phân vùng và tối ưu lưu trữ.
  - **Nguyên lý Chuẩn hóa dữ liệu (Normalization)**: Chia bảng để loại bỏ hoặc giảm thiểu dư thừa và phụ thuộc dữ liệu (**1NF, 2NF, 3NF, BCNF**).
  - **Công thức 10 Bước Thiết kế CSDL**:
    1.  Thu Thập Yêu Cầu (Requirements Gathering).
    2.  Phân Tích Yêu Cầu (Requirements Analysis).
    3.  Xác Định Thực Thể (Entity Identification).
    4.  Xác Định Thuộc Tính (Attribute Definition).
    5.  Xác Định Mối Quan Hệ (Relationship Definition).
    6.  Tạo ERD Khái Niệm (Conceptual ERD).
    7.  Chuyển đổi sang Mô hình Logic (Logical Model).
    8.  Chuẩn Hóa (Normalization).
    9.  Thiết kế Vật lý (Physical Design).
    10. Kiểm thử và Triển khai.
  - **7 Nguyên tắc cốt lõi của công thức thiết kế CSDL**: Noun Technique, Verb Technique, One-to-Many Rule, Many-to-Many Rule, Primary Key Rule, Normalization Rule, Business Rule Mapping.
  - **Các mẫu Thiết kế Bất hủy**: Key-Value Pair Storage (EAV), Repository Pattern, State Machine Implementation, Change Data Capture (CDC) Pattern, Dimensional Modeling (Kimball Methodology - Fact table, Dimension table, SCD Type 2, Date dimension), Covering Index.

- **Cơ sở dữ liệu quan hệ (SQL Relational Databases - RDBMS)**

  - Dữ liệu lưu trữ dưới dạng **bảng** gồm hàng và cột.
  - **Cấu trúc bảng và Ràng buộc toàn vẹn**: **Khóa chính (Primary Key - PK)**, **Khóa ngoại (Foreign Key - FK)**, **Khóa UNIQUE**, **Ràng buộc NOT NULL**, **Ràng buộc DEFAULT**.
  - **Các loại lệnh SQL**: **DDL** (Data Definition Language), **DML** (Data Manipulation Language), **DCL** (Data Control Language), **TCL** (Transaction Control Language), **DQL** (Data Query Language - chủ yếu SELECT).
  - **Giao dịch (Transaction)**: RDBMS hỗ trợ giao dịch **ACID**.

- **Thao tác truy vấn cơ bản (SQL Mastery)**

  - **Thứ tự thực hiện lệnh SQL (Logical Order of Operations)**: FROM, JOIN → WHERE → GROUP BY → Aggregate functions → HAVING → Window functions → SELECT → DISTINCT → UNION/INTERSECT/EXCEPT → ORDER BY → OFFSET → LIMIT/FETCH/TOP.
  - **Hàm tổng hợp (Aggregate Functions)**: AVG, COUNT, MAX, MIN, SUM.
  - **Gom nhóm và lọc dữ liệu**: **GROUP BY**, **HAVING**.
  - **Các phép JOIN (Phép nối)**: INNER JOIN, LEFT JOIN, RIGHT JOIN, FULL JOIN, SELF JOIN, CROSS JOIN, NATURAL JOIN, Multiple JOINs, JOIN with Multiple Conditions.
  - **UNION vs UNION ALL**: Kết hợp tập kết quả, UNION loại bỏ trùng lặp, UNION ALL giữ tất cả.
  - **Subquery (Truy vấn lồng)**: Lồng bên trong truy vấn khác, luôn thực thi trước. Single Value Subquery, Multiple Values Subquery, Correlated Subquery.
  - **CTE (Common Table Expressions)**.
  - **Window Functions**: Ranking Functions (ROW_NUMBER, RANK, DENSE_RANK, NTILE), Value Functions (FIRST_VALUE, LAST_VALUE, NTH_VALUE), Lead/Lag Functions (LAG, LEAD), Distribution Functions (PERCENT_RANK, CUME_DIST).
  - **Stored Procedures**: Chương trình SQL lưu trong DB, chứa logic nghiệp vụ phức tạp, kiểm soát giao dịch, cải thiện hiệu suất/bảo mật.
  - **DELETE vs TRUNCATE vs DROP**: Xóa dữ liệu ở các mức độ khác nhau.

- **Cơ sở dữ liệu NoSQL**

  - **Các loại phổ biến**: Document Database (MongoDB), Key-Value Store (Redis, Memcached), Column-Family (Cassandra, HBase), Graph Database (Neo4j).
  - **Ưu điểm**: Dễ mở rộng (scale-out), phù hợp dữ liệu lớn và không cấu trúc, hiệu năng cao khi truy vấn phân tán.
  - **Nhược điểm**: Thường chỉ đảm bảo nhất quán **cuối cùng (eventual consistency)**, dữ liệu dễ trùng lặp, thiếu tính liên kết nghiêm ngặt.

- **Tối ưu hóa Cơ sở dữ liệu (Database Tuning)**

  - **Chỉ mục (Index)**: Tạo cấu trúc dữ liệu bổ sung để tăng tốc truy vấn. Loại chỉ mục (B-tree, Hash, Full-text), EXPLAIN để kiểm tra.
  - **Các mẫu Viết lại Truy vấn (Query Rewriting Patterns)**: Thay thế truy vấn con vô hướng bằng JOIN, Thay thế OR bằng IN, Tránh LIKE với wildcard dẫn đầu.
  - **Chiến lược tối ưu hóa khác**: Tối ưu câu truy vấn, Phân tán và sao chép dữ liệu (Replication & Sharding), Bộ nhớ đệm (Caching), Thiết lập cấu hình, Giám sát và phân tích.

- **Giám sát & Vận hành Database**

  - **Các chỉ số Giám sát cốt lõi**: Hiệu suất (thời gian phản hồi, thông lượng, kết nối), Tài nguyên (CPU, Bộ nhớ, I/O), Cơ sở dữ liệu (khóa chờ, deadlocks, buffer cache hit ratio), Lưu trữ (dung lượng đĩa, sao lưu, log file growth).
  - **Script Giám sát Tự động**.
  - **Script Bảo trì Database Tự động**.

- **Bảo mật Cơ sở dữ liệu**

  - **Khung Bảo mật 10 điểm (Defense in Depth)**: Physical, Network, Authentication, Authorization, Data Encryption, Audit Logging, Backup Security, Patch Management, Database Firewall, Incident Response.
  - **Phòng chống SQL Injection**: Sử dụng **truy vấn tham số hóa (Parameterized Queries)** hoặc stored procedure với tham số.
  - **Các mẫu Bảo mật Nâng cao**: Row-Level Security (RLS), Data Masking.
  - **Linux Security Hardening cho Database**: Quy tắc Firewall, Quyền hạn tệp, Vô hiệu hóa dịch vụ không cần thiết, Bật nhật ký kiểm toán, Giới hạn hệ thống, Tối ưu tham số Kernel.

- **Chiến lược Sao lưu & Phục hồi**

  - **Chiến lược Sao lưu Toàn diện**: **Quy tắc 3-2-1** (3 bản sao, 2 phương tiện khác nhau, 1 bản offsite). Các loại sao lưu (Full, Incremental, Differential, Point-in-time).
  - **Khôi phục tại điểm thời gian (Point-in-Time Recovery - PITR)**.

- **Kho dữ liệu & ETL**

  - **Kiến trúc ETL hiện đại (Extract, Transform, Load)**: Trích xuất, Biến đổi, Tải.
  - **Mô hình Thiết kế Kho dữ liệu (Data Warehouse Design Patterns)**: **Kimball Methodology** (Fact table, Dim table, SCD Type 2, Date dimension).

- **Phương pháp Xử lý sự cố Cơ sở dữ liệu**

  - **Phương pháp 5W1H**: What, Where, When, Who, Why, How.
  - **Quy trình Xử lý sự cố**: Đánh giá ngay lập tức, Ổn định, Phân tích nguyên nhân gốc rễ, Giải quyết & Phục hồi, Đánh giá sau sự cố.

- **Xu hướng Công nghệ & Tương lai Database**
  - **AI-Driven Database Management**: Tối ưu hóa và quản trị CSDL tự động bởi AI.
  - **Cloud Databases & Containerization**.
  - **Real-Time Analytics & Streaming**: Change Data Capture (CDC), Stream Processing với Materialized Views.
  - **Vector Databases**: Lưu trữ và truy vấn **vector embeddings** cho dữ liệu phức tạp, tìm kiếm dựa trên độ tương đồng (Similarity Search), xử lý dữ liệu nhiều chiều.

### **V. DevOps & Điện Toán Đám Mây (Cloud Computing)**

- **Tổng quan về Cloud Computing & DevOps**

  - Kết hợp kiến trúc hạ tầng linh hoạt (Cloud) và triết lý văn hóa kỹ thuật (DevOps).
  - Nguyên lý bất biến để thiết kế và vận hành bền vững, hiệu quả, an toàn.

- **Kiến thức Cơ bản về Điện toán Đám mây (Cloud Fundamentals)**

  - **Định nghĩa & 5 Đặc tính Cốt lõi (theo NIST)**: Tự phục vụ theo yêu cầu, Truy cập mạng rộng rãi, Tập trung tài nguyên, Khả năng co giãn nhanh chóng, Đo lường tài nguyên.
  - **Mô hình Dịch vụ Chính**: **IaaS** (Infrastructure as a Service), **PaaS** (Platform as a Service), **SaaS** (Software as a Service).
  - **Ảo hóa (Virtualization) và Containerization (Docker)**: Container nhẹ hơn VM, khởi động nhanh hơn, dễ triển khai và rollback.
  - **Đa nhà cung cấp & Hybrid Cloud**: Đa vùng (Region)/đa tài khoản, Đa đám mây (Multi-cloud), Hybrid Cloud.
  - **Kiến trúc Cloud-native**: Phát triển **microservices**, kiến trúc hướng sự kiện (event-driven), và serverless (Function-as-a-Service). Nguyên lý bất biến: **"Hạ tầng bất biến" (immutable infrastructure)**.
  - **Khả năng Phân tán & Chịu lỗi**: **Độ bền (reliability)** và **khả năng chịu lỗi (fault tolerance)** cao, **Định lý CAP** (CP/AP), **Nhất quán cuối cùng (Eventual Consistency)**.
  - **Cơ sở dữ liệu & Lưu trữ**: Lưu trữ đối tượng (Object Storage), Block Storage, CSDL quan hệ (RDBMS), NoSQL. Phương pháp: **"design for failure"**, partitioning, replication, backup, disaster recovery.
  - **Infrastructure as Code (IaC)**: Định nghĩa hạ tầng bằng code (CloudFormation, Terraform, ARM templates).

- **CI/CD (Tích hợp Liên tục & Triển khai Liên tục)**

  - **Khái niệm CI/CD**: CI (Continuous Integration - build/test tự động), CD (Continuous Delivery/Deployment - đóng gói/triển khai staging/production).
  - **Nguyên lý Bất biến**: Mỗi artifact build 1 lần, triển khai **idempotent**, dùng Docker image.
  - **Quy trình Best-practices**: Git, code review, tự động hóa, môi trường test tái tạo tự động (IaC), checklist triển khai.
  - **Công cụ và Dịch vụ**: AWS CodePipeline/CodeBuild/CodeDeploy, Azure Pipelines, GCP Cloud Build/Cloud Deploy, Jenkins, GitLab CI, GitHub Actions.
  - **Chiến lược Triển khai**: **Blue-green deployment**, **canary release**, rollback tự động, feature flags.
  - **DevSecOps**: Tích hợp kiểm tra bảo mật tự động (static code analysis, container scanning) trong pipeline.

- **Giám sát & Quan sát (Monitoring & Observability)**

  - Khái niệm & Khác biệt: Monitoring (cái gì đang sai), Observability (tại sao nó sai).
  - **Ba Trụ cột (theo SRE/DORA)**: **Metrics**, **Logs**, **Traces** (distributed tracing).
  - **Công cụ & Dịch vụ**: AWS CloudWatch Metrics/Logs/Alarms, X-Ray (Tracing); Azure Monitor; GCP Cloud Monitoring/Logging/Trace; **Prometheus** (metrics), **Grafana** (dashboard), **ELK** (ElasticSearch-Logstash-Kibana), Datadog, New Relic.
  - **SLO/SLA/SLI & SRE**: SLO (Service Level Objective), SLI (Service Level Indicator), SRE (Site Reliability Engineering - "reliability is the most important feature").
  - Cảnh báo (Alert) & Phản ứng Sự cố (Incident Response).

- **Bảo mật (Security)**

  - **Nguyên lý Cơ bản**: **CIA Triad** (Confidentiality, Integrity, Availability), **Least Privilege** (Quyền tối thiểu), **Defense in Depth** (Phòng thủ nhiều lớp), **Zero Trust** (Không tin cậy mặc định).
  - **Mô hình Trách nhiệm Chung (Shared Responsibility Model)**: Nhà cung cấp đám mây vs Khách hàng.
  - **Quản lý Danh tính & Truy cập (IAM)**: IAM Role/Policy/Group, MFA, KMS (Key Management Service), Secrets Manager.
  - **Mạng & Hạ tầng bảo mật**: VPC/Subnet, Firewall (Security Group, NACL, NSG), VPN, VPC peering, PrivateLink, DDoS Protection.
  - **Giám sát An ninh**: IDS/IPS, Security Logs, SIEM.

- **Khả năng Mở rộng & Chịu lỗi (Scalability & Resilience)**

  - **Scalability**: **Scale Up** (Vertical scaling) vs **Scale Out** (Horizontal scaling). **Định luật Amdahl**. Phương pháp: Partitioning, Load Balancing, Stateless Design.
  - **Resilience**: "Failure is inevitable". Dự phòng (Redundancy), Cách ly lỗi (Isolation - Bulkhead pattern), Circuit Breaker (Cầu dao tự động), Tự động khôi phục (Auto-Recovery), Suy giảm từ từ (Graceful Degradation), Backpressure & Load Shedding.
  - **Chaos Engineering**: Kiểm thử khả năng chịu lỗi bằng cách tạo lỗi có kiểm soát (Chaos Monkey, Chaos Mesh).

- **Message Brokers & Queues (Hệ thống Hàng đợi Thông điệp)**

  - Giải pháp **bất đồng bộ**, giúp **tách rời người gửi và nhận**.
  - **Mô hình Point-to-Point (Queue)** và **Pub/Sub (Topic)**.
  - **Đảm bảo giao nhận**: At-most-once, At-least-once (yêu cầu consumer **idempotent**), Exactly-once.
  - Công cụ & Framework: **RabbitMQ**, **Apache Kafka**, **Amazon SQS & SNS**.

- **Service Mesh (Lưới Dịch vụ)**

  - Lớp hạ tầng chuyên biệt cho giao tiếp giữa các microservice. Thành phần: **Data Plane** (sidecar proxy) và **Control Plane**.
  - Lợi ích: Observability, Traffic Control (cân bằng tải, retry, timeout, circuit breaking), Security (mTLS), Release strategies (canary, blue-green).
  - Công cụ & Framework: **Istio**, **Linkerd**, **Envoy Proxy**.

- **Từ Senior tới Expert 10/10 trong Cloud Computing & DevOps**
  - Chuyển từ “vận hành – tối ưu” sang “tư duy kiến trúc & tài chính toàn cục”, gồm **FinOps** + **Platform Engineering** + **Tự động hoá chính sách (Policy-as-Code)**.
  - Cần nắm **eBPF/Observability hiện đại**, **GitOps** chuẩn hóa toàn chuỗi, **Zero-Trust** & **Confidential Computing** cho dữ liệu “in-use”.
  - **MLOps & AI-Ops** trở thành phần không thể thiếu của pipeline; **“green-cloud” (sustainability)** được thêm như ràng buộc thiết kế.

### **VI. APIs & Giao Tiếp Phần Mềm**

- **APIs & Software Communication: Tổng Quan Từ Góc Độ Chuyên Gia**

  - Nguyên lý bất biến, khả năng đánh đổi (trade-off) giữa hiệu năng, tính linh hoạt và độ phức tạp, cùng với việc lựa chọn công cụ phù hợp với ngữ cảnh là tối quan trọng.

- **RESTful APIs**

  - Phong cách kiến trúc dựa trên tài nguyên, sử dụng các phương thức HTTP chuẩn (GET, POST, PUT, DELETE, PATCH).
  - **6 nguyên tắc cơ bản**: **Client-Server**, **Stateless** (Không trạng thái), **Cacheable** (Có thể Cache), **Uniform Interface** (Giao diện Đồng nhất - URI, biểu diễn, tự mô tả, HATEOAS tùy chọn), **Layered System** (Hệ thống Phân lớp), **Code on Demand** (Mã theo yêu cầu - tùy chọn).
  - **Ưu điểm**: Đơn giản, phổ biến, dễ tích hợp, dễ debug, hỗ trợ cache và CDN tốt. Thích hợp cho dịch vụ web công khai.
  - **Nhược điểm**: Vấn đề over-fetching/under-fetching, hiệu năng kém hơn các giao thức nhị phân (gRPC), không hỗ trợ giao tiếp realtime hai chiều trực tiếp.

- **GraphQL**

  - **Ngôn ngữ truy vấn cho API**, tập trung vào việc cho phép client xác định chính xác dữ liệu cần lấy. Sử dụng **một endpoint duy nhất**.
  - Định nghĩa **schema (lược đồ) kiểu mạnh** ở phía server bằng **GraphQL SDL**.
  - **Resolver**: Mỗi trường trong schema gắn với hàm xử lý.
  - **3 loại thao tác chính**: Query (truy vấn), Mutation (thay đổi), Subscription (thời gian thực).
  - Phù hợp cho mô hình **BFF (Backend for Frontend)**, tổng hợp dữ liệu từ nhiều dịch vụ backend.
  - **Ưu điểm**: Client chỉ nhận đúng dữ liệu yêu cầu, gom nhiều dữ liệu liên quan trong một lần gọi. Tăng tính tự phục vụ cho front-end.
  - **Nhược điểm**: Server phức tạp hơn, khó cache HTTP truyền thống, rủi ro tốn tài nguyên, đường cong học tập cao hơn REST.
  - **Kỹ thuật Nâng cao**: Batching & DataLoader, Persisted Queries, Federation & Microservices, Bảo mật (field-level authorization, giới hạn độ sâu truy vấn).

- **gRPC**

  - **Framework RPC (Remote Procedure Call)** hiện đại, xây dựng trên **HTTP/2** và **Protocol Buffers (protobuf)**.
  - **Service Definition (.proto file)**: Định nghĩa các hàm (RPC) mà client có thể gọi.
  - **Code Generation**: Sinh mã client và server stub cho nhiều ngôn ngữ.
  - **4 kiểu tương tác RPC (Streaming)**: Unary RPC, Server-streaming RPC, Client-streaming RPC, Bidirectional-streaming RPC.
  - Chủ yếu dùng cho **giao tiếp nội bộ (service-to-service)** trong kiến trúc microservices.
  - **Ưu điểm**: Hiệu suất cao, hợp đồng API rõ ràng và kiểm tra kiểu chặt chẽ, hỗ trợ streaming, đa ngôn ngữ, đa nền tảng.
  - **Nhược điểm**: Không human-readable (khó debug thủ công), độ phức tạp triển khai, không phù hợp cho web client trực tiếp.
  - **Kỹ thuật Nâng cao**: Interceptors (middleware), Error Handling, Deadline & Cancellation, Security (TLS, JWT/OAuth2), Streaming nâng cao.

- **WebSockets**

  - Giao thức liên lạc **hai chiều (bi-directional)** trên một **kết nối đơn và liên tục (persistent TCP)**, cho phép **giao tiếp thời gian thực**.
  - **Ưu điểm**: Giao tiếp thời gian thực hai chiều (server push chủ động), độ trễ rất thấp sau khi kết nối, giảm tải mạng so với long-polling.
  - **Nhược điểm**: Stateful server (khó mở rộng - Scaling), giao thức tùy biến, một số proxy/firewall cũ có thể không hỗ trợ.
  - **Kỹ thuật Nâng cao**: Socket.IO, kiểm soát lỗi, Xác thực Origin.

- **Authentication (Xác thực) trong API**

  - **Xác thực (Authentication)**: Quá trình xác minh danh tính. Khác với **Ủy quyền (Authorization)**.
  - **Nguyên tắc bất biến**: Không truyền mật khẩu thô qua mạng (trừ login ban đầu qua HTTPS), Hạn chế thời gian hiệu lực của token/key, Một token - một mục đích, Truyền an toàn (HTTPS), Lưu trữ an toàn phía server (hash mật khẩu).
  - **Cách thực hiện & Triển khai**: HTTP Basic Authentication, API Key, **OAuth 2.0 & Bearer Token**, **JSON Web Token (JWT)**, **mTLS (mutual TLS)**.
  - **Xác thực nâng cao**: OpenID Connect (OIDC) cho SSO, Xác thực hai yếu tố (2FA/MFA), Auth cho microservice nội bộ (mTLS/JWT "Service Account").
  - **Best Practices bảo mật**: Luôn cập nhật thư viện, dùng chuẩn thay vì tự chế, pentest.

- **API Gateways**
  - Điểm nút trung tâm đứng trước các dịch vụ backend, đóng vai trò **cổng duy nhất** để client bên ngoài truy cập.
  - **Tính năng**: Routing & Load Balancing, Authentication & Authorization, Rate Limiting & Quota, Caching, Transformation & Orchestration, Quản lý phiên bản, Giám sát & Logging tập trung.
  - **Lợi ích**: Đơn giản hóa phía client, tối ưu cho từng loại client (BFF), bảo mật và quản lý tập trung, giảm tải cho dịch vụ yếu, khả năng mở rộng linh hoạt.
  - **Thách thức**: Độ trễ thêm, điểm lỗi tập trung (SPoF), phức tạp khi thay đổi/vận hành.
  - **Công cụ**: Kong, Tyk, Apigee, AWS API Gateway, NGINX, Traefik, Express Gateway.
  - **Phân biệt với Service Mesh**: Gateway xử lý traffic từ bên ngoài vào, Service Mesh quản lý giao tiếp nội bộ giữa các service.

### **VII. Kiểm Thử & Đảm Bảo Chất Lượng (Testing & QA)**

- **Nguyên lý cốt lõi và bất biến nhất về Testing & QA**

  - Kiểm thử chỉ phát hiện lỗi (Testing shows presence of defects).
  - Kiểm thử toàn diện là không thể (Exhaustive testing is impossible).
  - Nguyên lý “pesticide paradox” (Nghịch lý thuốc trừ sâu).
  - "Vết lỗi 80/20” (defect clustering).
  - Test depends on context (Kiểm thử phụ thuộc ngữ cảnh).
  - **Kiểm thử sớm (Shift Left)**.
  - Nguyên lý “cầu trục chi phí” (cost/benefit balance).

- **Các loại kiểm thử & cấp độ kiểm thử**

  - Kiểm thử thủ công (Manual Testing) vs. Kiểm thử tự động (Automated Testing).
  - **Unit Test**: Kiểm thử từng đơn vị mã (hàm/lớp) riêng biệt. Nhanh, thường do developer viết (TDD).
  - **Integration Test**: Kiểm tra sự phối hợp giữa các module hoặc với hệ thống ngoài (DB, API).
  - **End-to-End Test (E2E)**: Kiểm thử hệ thống toàn bộ như người dùng cuối.
  - **Performance Test**: Đánh giá hiệu năng, tải, sức chịu tải (load, stress).
  - **Security Test**: Xác định lỗ hổng an ninh (SQLi, XSS, CSRF).
  - **Accessibility Test**: Đảm bảo phần mềm dễ tiếp cận cho người khuyết tật.
  - **Acceptance Test & Exploratory Test**.
  - **Tam giác kiểm thử (Testing Pyramid)**: Ưu tiên nhiều unit test (đáy), ít integration test, và ít nhất E2E test (đỉnh).

- **Công cụ và framework kiểm thử**

  - Unit/Integration: JUnit (Java), Pytest (Python), **Jest/Vitest** (JavaScript).
  - Frontend/E2E: **Playwright, Cypress**, TestCafe, Selenium WebDriver.
  - API Testing: Postman/Newman, REST-assured (Java), Karate (Java).
  - Performance: Apache JMeter, Gatling, **k6**, Locust.
  - Security: OWASP ZAP, Burp Suite, SonarQube, Snyk.
  - Accessibility: axe-core, Lighthouse.
  - Property-based Testing (fast-check).
  - Contract test giữa micro-service.
  - Mutation Testing (Stryker).

- **QA trong Agile/DevOps**: Vai trò QA chuyển từ "kiểm thử cuối" sang hợp tác xuyên suốt, trở thành cố vấn và chiến lược gia. Thường nhúng QA vào từng nhóm chức năng.
- **Quy Trình Phát Triển Agile & TDD**: **Test-Driven Development (TDD)** (F.I.R.S.T. cho Unit Tests, The "Button").

### **VIII. Hệ Điều Hành & Mạng Máy Tính (OS & Networking)**

- **Nguyên lý cốt lõi và bất biến nhất về Networking & OS**

  - **Mô hình phân tầng (Layered Model)**: OSI 7 tầng, TCP/IP 4 tầng.
  - **Địa chỉ và Định tuyến (Addressing & Routing)**.
  - **Giao thức truyền tải (Transport Protocols: TCP & UDP)**: Cung cấp độ tin cậy và hiệu năng khác nhau.
  - **Quản lý tiến trình và luồng (Process & Thread Management)**.
  - **Quản lý bộ nhớ ảo (Virtual Memory Management)**.
  - **Hệ thống tập tin (File Systems)**.
  - **Gọi hệ thống (System Calls)**.
  - **Tính bảo mật (Security)**: Áp dụng nguyên lý **CIA triad**, **least privilege**, **defense in depth**, **Zero Trust**.

- **Công thức/phương pháp thực hiện cụ thể và các lệnh command line**

  - **Networking**:
    - Kiểm tra cấu hình IP & Interface: **ip addr show**, `sudo ip link set dev eth0 up/down`, `sudo ip addr add`.
    - Định tuyến (Routing): **ip route show**, `sudo ip route add default via`, giao thức định tuyến động (**RIP, OSPF**).
    - Giao thức truyền tải (TCP/UDP): **ss -tuln** hoặc **netstat -tuln**.
    - Phân giải tên miền (DNS): **dig example.com**, **nslookup google.com**.
    - HTTP/HTTPS: **curl -I https://www.example.com**.
    - Firewall (trên Linux): **sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT**, **sudo iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE** (NAT).
  - **Operating Systems (Linux)**:
    - Quản lý tiến trình: **ps aux**, **pstree**, **top/htop**, **kill PID**, **nice/renice**.
    - Quản lý bộ nhớ: **free -h**, **vmstat 1**, `cat /proc/meminfo`.
    - Hệ thống tập tin: **sudo mkfs.ext4**, **sudo mount**, **lsblk**, **df -h**, **du -sh**, `ls -l`, `cd`, `pwd`, `cp`, `mv`, `rm`, `mkdir`, `rmdir`, `find`, `chmod`, `chown`, `tar`.
    - Quản lý dịch vụ và nhật ký (systemd): **sudo systemctl start/enable/status nginx.service**, **journalctl -u nginx.service**, **dmesg**.
    - Quản lý người dùng & quyền: `id`, `whoami`, `groups`, `sudo`.
    - Cài đặt gói: `sudo apt update && sudo apt install nginx` (Debian/Ubuntu), `yum/dnf/rpm` (CentOS).

- **Checklist thực hiện từng bước cho các tác vụ networking và OS**

  - Khắc phục sự cố mạng (Network Troubleshooting).
  - Cấu hình hệ thống (System Configuration).
  - Tối ưu hiệu năng (Performance Tuning).
  - Củng cố bảo mật (Security Hardening).

- **Ví dụ cụ thể về network topology, routing scenarios, system administration tasks và tình huống xử lý sự cố thực tế**

  - Network Topology (danh sách kề).
  - VLAN Setup (Phân đoạn mạng ảo).
  - Firewall Rules (Quy tắc tường lửa với iptables).
  - Process Scheduling (Lập lịch tiến trình với renice).
  - Memory Allocation (Phân bổ bộ nhớ với free, vmstat).
  - System Administration Task (Quản lý dịch vụ Nginx với systemctl).

- **Dấu hiệu và bối cảnh khi nào nên áp dụng từng protocol, service và kỹ thuật**

  - **TCP vs UDP**.
  - **Static vs Dynamic Routing**.
  - **Monolithic vs Microkernel** (kiến trúc Kernel).
  - **Containerization** (Đóng gói Container).

- **Kiến trúc 3 lớp Internet**

  - **Physical Layer**: Cáp quang biển, Internet Exchange Points (IXPs), data centers.
  - **Logical Layer**: Protocols (TCP/IP, BGP, OSPF), routing tables, DNS hierarchy.
  - **Application Layer**: HTTP/HTTPS, email, VoIP, cloud services.

- **Triết lý Internet**: Decentralized, End-to-end principle, Best effort delivery, Layered architecture.
- **Linux kế thừa triết lý UNIX**: “Mọi thứ là file; chương trình nhỏ, làm một việc duy nhất và ghép nối qua dòng chảy văn bản”.
- **UNIX philosophy**: Mỗi chương trình làm một việc và làm tốt. Kết quả của một chương trình trở thành đầu vào của chương trình khác qua pipe. Ưu tiên CLI. Mọi cấu hình lưu ở file văn bản để dễ diff, version control.
- **Linux Kernel Documentation**: docs.kernel.org.

### **IX. Quản Lý Nhóm & Dự Án (Team & Project Management)**

- **Nguyên lý cốt lõi và bất biến nhất về Quản lý Nhóm & Dự án**

  - **Tinh thần Agile và Giá trị cốt lõi**: Cá nhân và tương tác, Phần mềm chạy tốt, Hợp tác với khách hàng, Phản hồi thay đổi. Học hỏi qua trải nghiệm, tự tổ chức và liên tục phản tỉnh. Kiểm chứng thực nghiệm (minh bạch, thanh tra, thích nghi). Tinh thần "Inspect & Adapt".
  - **Nguyên lý Code Review và Cộng tác**: Đảm bảo chất lượng code và chia sẻ hiểu biết, Tất cả cùng tạo ra sản phẩm tốt hơn, Mọi thay đổi quan trọng nên được ít nhất một người khác xem qua, Phổ biến kiến thức và thúc đẩy sở hữu mã nguồn tập thể, Không để "single point of failure" trong kiến thức về code, Giữ giọng điệu xây dựng, Tin cậy và khen ngợi, Giữ kích thước thay đổi nhỏ, Tự động hóa việc vặt, Mọi ý kiến đều đáng giá, Con người quyết định chất lượng, "Bốn mắt bao giờ cũng tốt hơn hai".
  - **Thực tiễn Quản lý Codebase Lớn**: Tính mô-đun (Modularity), Separation of Concerns (Tách biệt nhiệm vụ), Quản lý phiên bản (Version Control System), Phong cách code nhất quán, Tài liệu hóa hợp lý (ADR), Kiểm thử tự động sớm và thường xuyên, Refactoring định kỳ, Quản lý sự phụ thuộc, Không tối ưu sớm (KISS, YAGNI), Tính dễ thay đổi, Truyền thông và đào tạo liên tục, **Định luật Conway** (Cấu trúc hệ thống phản ánh cấu trúc tổ chức).

- **Phương pháp thực hiện cụ thể và các Framework/Methodologies**

  - **Phương pháp Agile (Scrum/Kanban)**:
    - **Scrum**: **Sprint Planning**, **Daily Scrum (Daily Stand-up)**, **Sprint Review**, **Sprint Retrospective**, Quản lý Product Backlog, Ước lượng (Planning Poker, Story Points).
    - **Kanban**: Minh bạch hóa công việc (bảng Kanban), Giới hạn **Work In Progress (WIP)**, Hệ thống kéo (Pull system), Theo dõi Metric (cycle time), Cải tiến liên tục, tiệm tiến.
    - **Mở rộng Agile**: Scrum of Scrums, LeSS (Large-Scale Scrum), SAFe (Scaled Agile Framework).
  - **Code Reviews & Cộng tác**: Quy trình cơ bản (tạo PR, mời review, nhận xét xây dựng, chỉnh sửa, merge). Best Practices (kích thước PR nhỏ, tự động hóa linters, guideline coding, khen ngợi, mentoring, câu hỏi gợi mở, đo lường chỉ số review, quy tắc 24h, checklist review, code owners, pair programming).
  - **Quản lý Codebase Lớn**: Kiến trúc và phân tách, Quản lý phiên bản (Git, Git Flow, Trunk Based Development, đánh tag), Chất lượng Code (style guide, tài liệu hóa, CI/CD, phụ thuộc, refactoring), Công cụ và Quy trình nâng cao (Code owners, Rotating people, ADR, phân tích codebase, InnerSource, Profiling, Backwards compatibility, Đào tạo nội bộ, bus factor, Design review meeting).

- **Checklist thực hiện từng bước cho các hoạt động Team & Project Management**

  - Sprint Planning, Daily Standups (Daily Scrum), Sprint Review, Sprint Retrospective, Code Review (Quy trình), Quản lý Nợ Kỹ thuật (Refactoring).

- **Dấu hiệu và bối cảnh khi nào nên áp dụng từng phương pháp quản lý và Leadership Style**
  - **Waterfall vs Agile**: Agile khi yêu cầu thay đổi thường xuyên, cần giao sản phẩm sớm, hợp tác chặt chẽ.
  - **Scrum vs Kanban**: Scrum khi cần cấu trúc rõ ràng, nhịp điệu phát hành cố định. Kanban linh hoạt cho luồng công việc liên tục.
  - **Leadership Style**: Lãnh đạo trao quyền (Empowering Leadership).
  - **Centralized vs Distributed Teams**: Nhóm QA nhúng (Embedded QA), đội phân tán.

### **X. An Toàn Thông Tin & An Ninh Mạng**

- **Nguyên lý cơ bản**

  - **Tam giác CIA** (Confidentiality, Integrity, Availability): Bí mật, Toàn vẹn, Sẵn sàng.
  - **Least Privilege** (Quyền tối thiểu).
  - **Defense in Depth** (Phòng vệ theo chiều sâu).
  - **Zero Trust** (Không tin cậy mặc định).
  - Không truyền mật khẩu thô qua mạng.
  - Hạn chế thời gian hiệu lực (token/key).
  - Truyền an toàn (TLS/HTTPS).
  - Lưu trữ an toàn phía server (hash mật khẩu).

- **Mô hình trách nhiệm chung (Cloud)**

  - Bảo mật trên đám mây chia sẻ giữa nhà cung cấp (bảo mật của đám mây - infrastructure) và khách hàng (bảo mật trong đám mây - OS, network config, ứng dụng).

- **Quản lý danh tính & truy cập**

  - **IAM** (Identity and Access Management).
  - **MFA** (Multi-Factor Authentication).
  - **KMS** (Key Management Service).
  - **Secrets Manager**.

- **Mạng & hạ tầng bảo mật**

  - **VPC/Subnet**: Xây dựng mạng ảo riêng tư.
  - **Firewall** (Security Group, NACL, NSG): Lọc gói để bảo vệ mạng.
  - VPN, VPC peering, PrivateLink: Kết nối an toàn giữa các mạng.
  - **DDoS Protection**: Chống lại tấn công từ chối dịch vụ phân tán.

- **Các dạng tấn công & Phần mềm độc hại**

  - **Tấn công**: Mật khẩu, Buffer Overflow, DoS/DDoS, **SQL Injection**, Social Engineering.
  - **Malware**: Logic bomb, Trojan, Back door, Virus, Worm, Zombie/Botnet, Rootkit.

- **Bảo Mật Nâng Cao (JS)**
  - **CSP, SRI** chặn XSS chuỗi cung ứng.
  - **DOMPurify** làm sạch dữ liệu HTML đầu vào.
  - Tránh **eval**, **Function constructor**, tránh phép thực thi tùy ý.
  - **SameSite cookie, CSRF double-submit** cho SPA sử dụng cookie-based auth.

### **XI. Phân Tích Dữ Liệu & Kể Chuyện Bằng Dữ Liệu (Data Analysis & Storytelling)**

- **Vai trò của Data Analyst (DA) và sự phổ biến của SQL**

  - Tìm ra **insights thông qua những con số** giúp doanh nghiệp đưa ra các quyết định có cơ sở.
  - **6 giai đoạn phân tích dữ liệu**: Data Specifications, Data Collection, Data Preparation, Data Exploratory, Modelling & Evaluation, Communication of Insights.
  - **SQL xuất hiện chủ yếu ở Data Preparation**.
  - SQL sẽ phổ biến giống như Excel, sớm trở thành yêu cầu cơ bản.

- **Roadmap Tự học Data Analyst**

  - **Data Preparation** (Chuẩn bị Dữ liệu): SQL (DQL), Python (Numpy, Pandas).
  - **Data Exploratory** (Khai phá Dữ liệu): Problem Solving Skill, Domain Knowledge, Statistics (Descriptive), BI tools (Tableau, Power BI), Python (Matplotlib, Seaborn).
  - **Modeling & Evaluation**: Machine Learning (Andrew Ng).
  - **Communication of Insights**: Data Storytelling, Focus on audience.
  - Xây dựng Portfolio và Resume.

- **Data Storytelling - Truyền tải Insight hiệu quả**
  - **Khái niệm & Vai trò**: Kết hợp **dữ liệu (Data)**, **hình ảnh hiệu quả (Effective Visuals)** và **lời kể (Narrative)**. Giúp câu chuyện tạo sự cộng hưởng, ghi nhớ, có cấu trúc, kết nối thông tin, thuyết phục hành động, giải thích "tại sao".
  - **Bộ kỹ năng cần thiết**: Analytical Skills, UI/UX Skills, Data Visualization Skills.
  - **Phân loại Ứng dụng Phân tích (Dashboard Types)**: Strategic Dashboard, Analytical Dashboard, Operational Dashboard.
  - **Giải phẫu Dashboard (Dashboard Anatomy)**: Header, Date and Currency, Summary Section, Report Header, Report Area, Footer, Layout Navigation, Primary Navigation, Secondary Navigation, Call to Action, Info Icons, Menu Icons.
  - **Nguyên tắc Thiết kế Dashboard tốt nhất**: Information Hierarchy, Exceeding Boundaries, Highlight Important Information Effectively, Avoid Displaying Excessive Details, Make Appropriate Use of Visualization, Avoid Oversizing of Visualization, Avoid All Non Data Ink Elements, Font Selection, Use of Grids, Use of Colors.
  - **Lựa chọn Hình ảnh Trực quan Hiệu quả**: Văn bản Đơn giản, Bảng (Tables - Heatmap), Biểu đồ (Graphs - Scatterplot, Line Graph, Slopegraph, Bar Chart, Stacked Bar Chart, Waterfall Chart, Horizontal Bar Chart, Area Chart, Box Plot, Gantt Chart, Histogram, Pareto Chart, Radar Chart), Các loại biểu đồ Nâng cao và ít phổ biến hơn.
  - **Các loại hình ảnh trực quan nên tránh**: **Pie charts**, **Donut charts**, **3D**, Secondary Y-axis, đường cong làm mượt, hình dạng kỳ lạ cho bong bóng.
  - **Loại bỏ sự lộn xộn (Clutter)**: Các yếu tố trực quan chiếm không gian nhưng không làm tăng sự hiểu biết. Giảm **Cognitive Load**. **Data-ink ratio / Signal-to-noise ratio**. **Gestalt Principles of Visual Perception** (Proximity, Similarity, Enclosure, Closure, Continuity, Connection). Lack of Visual Order (Alignment, White Space). Non-Strategic Use of Contrast. Các bước Decluttering.
  - **Tập trung sự chú ý của khán giả (Focus Audience's Attention)**: Sử dụng **Preattentive Attributes** (kích thước, màu sắc, vị trí, hình dạng, độ đậm nhạt, hướng, chiều dài). Phân biệt Iconic, Short-term, Long-term Memory.
  - **Tư duy như một nhà thiết kế (Think Like a Designer)**: Form follows function, Affordances, Highlight the important stuff, Eliminate distractions, Create a clear visual hierarchy, Accessibility, Don't overcomplicate, Text is your friend, Aesthetics, Acceptance.
  - **Bài học về kể chuyện (Lessons in Storytelling)**: The Magic of Story, Constructing the Story (Beginning, Middle, End), The Narrative Structure (Narrative Flow, Spoken/Written Narrative), The Power of Repetition (Bing, Bang, Bongo), Tactics to Help Ensure that Your Story is Clear (Horizontal Logic, Vertical Logic, Reverse Storyboarding, A Fresh Perspective). Khán giả là nhân vật chính.

### **XII. Toán Học & Machine Learning/AI**

- **Nền tảng Toán học Bất Biến**

  - **Đại số Tuyến tính**: Vectors, Matrix operations, Trị riêng/Vector riêng, Chuẩn (Norm).
  - **Giải tích**: Đạo hàm riêng, Gradient descent, Chain rule.
  - **Xác suất Thống kê**: Biến ngẫu nhiên, Hàm mật độ xác suất (PDF), Quy tắc Bayes, Kỳ vọng, Phương sai, Ma trận hiệp phương sai, Phân phối chuẩn, Ước lượng tham số (MLE, MAP).
  - **Toán Rời rạc**: Lý thuyết đồ thị, Lý thuyết tập hợp, Lý thuyết thông tin (Entropy).
  - **Tối ưu Lồi (Convex Optimization)**: Tập lồi, Hàm lồi, Bài toán đối ngẫu/KKT.

- **Thuật toán Machine Learning & AI**

  - **Các khái niệm cơ bản**: Định nghĩa học máy (Mitchell), Data point/Feature vector, Các loại nhiệm vụ (Classification, Regression, Machine Translation, Clustering, Completion), Phép đánh giá (Training set, Test set, Validation set, Training error, Test error), Kinh nghiệm (Supervised, Unsupervised, Semi-supervised, Reinforcement Learning), Hàm mất mát (Loss function/Cost function), Tham số mô hình.
  - **Trích chọn đặc trưng (Feature Engineering)**: Biểu diễn dữ liệu thô, Lựa chọn đặc trưng, Giảm chiều dữ liệu, Bag of Words (BoW), Transfer Learning, Chuẩn hóa vector.
  - **Các Thuật toán ML cơ bản**: Linear Regression, K-Nearest Neighbors (KNN), K-means Clustering, Naive Bayes Classifier (NBC).
  - **Hệ thống Khuyến nghị (Recommendation Systems)**: Content-based, Neighborhood-based Collaborative Filtering (NBCF), Matrix Factorization Collaborative Filtering (MFCF).
  - **Giảm chiều dữ liệu (Dimensionality Reduction)**: Principal Component Analysis (PCA), Linear Discriminant Analysis (LDA).

- **Deep Learning**

  - **Nền tảng Neural Network**: Perceptron (PLA), Multilayer Neural Network (MLP), Feedforward Network.
  - **Hàm kích hoạt (Activation Function)**: Sigmoid, Tanh, **ReLU**.
  - **Backpropagation**: Phương pháp tính đạo hàm.
  - Overfitting trong NN, Regularization (Weight decay).
  - **Logistic Regression** (trong ngữ cảnh NN).
  - **Softmax Regression**: Mở rộng của logistic regression cho multi-class classification.
  - **Support Vector Machine (SVM)**: Hard-margin SVM, Soft-margin SVM, **Kernel SVM** ("kernel trick", Linear, Polynomial, RBF, Sigmoid), Multi-class SVM.

- **MLOps (Machine Learning Operations)**

  - Quy trình phát triển và triển khai mô hình học máy vào môi trường production.
  - Khả năng kiểm thử (Testing), triển khai (Deployment), và duy trì (Maintainability).
  - Quản lý phiên bản mô hình (Model versioning).
  - Giám sát hiệu suất mô hình (Model monitoring).
  - Tái huấn luyện mô hình (Model retraining).
  - **Kỹ thuật Prompt (Prompt Engineering)**: Thiết kế và tinh chỉnh câu lệnh/ngữ cảnh đưa vào mô hình AI.

- **AI Ethics (Đạo đức AI)**

  - Luôn xem xét tác động xã hội của AI, phát triển AI có trách nhiệm.
  - Các vấn đề liên quan đến thiên vị (bias), công bằng (fairness), minh bạch (transparency), và quyền riêng tư (privacy).

- **Lộ trình học tập đề xuất (AI Engineer)**
  - Tháng 1-3: Toán học + Python Fundamentals.
  - Tháng 4-6: ML/DL + dự án thực hành.
  - Tháng 7-9: Chuyên sâu các lĩnh vực cụ thể (CV/NLP/RL).
  - Tháng 10-12: MLOps + Ethics + Production deployment.

### **XIII. Tư Duy & Kỹ Năng Mềm**

- **Tư duy tổng thể (Holistic Thinking Method)**

  - Nhận định kiến thức là một **hệ thống sống động**, xem xét toàn bộ hệ thống và mối quan hệ giữa các phần.
  - Công thức: **Nhân cách hóa tri thức** thành Bộ Não (Chiến lược), Trái Tim (Động lực), Cơ Bắp (Kiến thức thực thi), Hệ Thần Kinh (Tương tác & Phản hồi).

- **Tách biệt mối quan tâm (Separation of Concerns - SoC) / Nguyên lý đơn trách nhiệm (Single Responsibility Principle - SRP)**

  - Mỗi thành phần/lớp/module chỉ nên đảm nhận **một mục đích hoặc chức năng duy nhất** (một lý do để thay đổi).

- **Tư duy hệ thống (Systems Thinking)**

  - Hiểu cơ chế tương tác giữa các thành phần trong một hệ thống, tập trung vào bức tranh toàn cảnh.
  - Nhận diện các mối quan hệ, vòng phản hồi, và điểm đòn bẩy để tối ưu hóa hiệu suất tổng thể.

- **Pháp - Thế - Thuật (Principle - Context - Technique)**

  - Nguyên lý cốt lõi cho tư duy chiến lược đa chiều: **Pháp** (Nguyên tắc căn bản), **Thế** (Bối cảnh/Tình thế), **Thuật** (Kỹ thuật/Công cụ thực thi).

- **Quản lý rủi ro và Tâm lý giao dịch (Risk Management & Trading Psychology)**

  - **Quản lý rủi ro**: Quy tắc 1-2% vốn mỗi lệnh, đặt Stop-Loss, tính toán khối lượng lệnh phù hợp, Tỷ lệ Risk/Reward (R:R), Đa dạng hóa, Kế hoạch cho chuỗi thua lỗ.
  - **Tâm lý giao dịch**: Kỷ luật và kiên định với kế hoạch, kiểm soát lòng tham và nỗi sợ, tách biệt cảm xúc khỏi quyết định.

- **Kỹ thuật ngữ cảnh (Context Engineering)**

  - Quản lý toàn bộ hệ sinh thái thông tin ảnh hưởng đến hành vi AI, bao gồm ngữ cảnh xác định (prompt, cơ sở tri thức) và ngữ cảnh xác suất (thông tin truy cập qua công cụ/web).
  - Mục tiêu: lấp đầy cửa sổ ngữ cảnh của mô hình bằng tất cả các sự kiện, quy tắc, công cụ và thông tin liên quan để giảm ảo giác và giúp mô hình hiểu đúng nhiệm vụ.
  - **6 Nguyên tắc**: Thiết kế cho Đường cao tốc Ngữ nghĩa, Giám sát Chất lượng Nguồn, Triển khai Các Biện pháp Bảo mật, Đo lường Độ chính xác Quyết định, Kiểm soát Phiên bản Mọi thứ, Định hình Cửa sổ Khám phá.
  - **4 hoạt động cơ bản**: Viết, Chọn, Nén, Cô lập.

- **Học hỏi liên tục & Khả năng thích nghi (Continuous Learning & Adaptability) / Tư duy phát triển (Growth Mindset)**

  - Duy trì tinh thần học tập suốt đời, không ngừng nâng cấp kiến thức và kỹ năng.
  - Khả năng điều chỉnh nhanh chóng với thay đổi công nghệ và môi trường.
  - Niềm tin rằng khả năng có thể cải thiện qua nỗ lực và kiên trì, xem khó khăn là cơ hội để tiến bộ.
  - **Kỹ thuật Feynman**: Giải thích đơn giản để xác nhận và gắn kết hiểu biết.
  - **Quy luật Boy Scout**: Luôn để lại mã sạch hơn lúc bạn tìm thấy nó.
  - **Luyện tập có chủ đích (Deliberate Practice)**: Vượt qua vùng an toàn.
  - **Ôn tập cách quãng (Spaced Repetition)** & **Luyện tập truy hồi (Retrieval Practice)**.
  - **Tư duy Nguyên lý Đầu tiên (First Principles Thinking)**.

- **Bản Đồ Hệ Sinh Thái Tư Duy (16 kiểu tư duy)**
  - **Nhóm Phân tích – Đánh giá**: Analytical (AN), Critical (CR), Diagnostic (DG), Convergent (CV).
  - **Nhóm Sáng tạo – Phát triển**: Creative (CT), Divergent (DV), Growth Mindset (GM), Synthetic (SY).
  - **Nhóm Điều hướng hệ thống**: Systems (ST), Problem-Solving (PS), Decision-Making (DM), Computational (CP).
  - **Nhóm Thích nghi – Xã hội**: Flexible/Adaptive (FX), Emotional (EQ), Collaborative (CO), Independent (ID).
  - **Nhóm Siêu nhận thức**: Metacognition (MC), Cognitive Reflection (RF).

---

Hy vọng danh sách chi tiết này sẽ giúp bạn kiểm tra các yêu cầu dự án của mình một cách hiệu quả với AI Agent và định hướng lộ trình học tập để đạt được mục tiêu trở thành chuyên gia!
